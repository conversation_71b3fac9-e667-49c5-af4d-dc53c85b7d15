# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- New "Clear Filters" button in Requests page for better user experience
- Collapsible filter section in Reports page
- Added 'Ask AI' menu item with ChatGPT-like interface
- Comprehensive test suite for components and services
- Unit tests for App, Login, Sidebar, Reports components
- Unit tests for API service
- Configurable notification polling interval in API configuration
- New ReportSelect page at path /report/select to display different report types
- New ReportCustomer page at path /report/customer with company, month (MM/YYYY format), and customer filters
- Item selection with checkboxes in report tables for selective Excel export
- Auto-hiding success and error messages with smooth fade animations

### Improved
- Enhanced test coverage with 8+ passing tests
- Fixed React act() warnings in tests using proper wrapping
- Improved test selectors for better reliability
- Added proper mocking for axios and API services
- Fixed axios.create mocking in tests
- Implemented proper axios interceptors mocking
- Added detailed documentation in test files
- Enhanced customer filtering with autocomplete input field instead of dropdown select
- Improved user experience with automatic message dismissal after actions
- Filtered staff lists by role in RequestDetail.js and CreateTicket.js:
  - Pricing staff filtered by role = 'purchaser'
  - Dispatch staff filtered by role = 'moderator'
  - Sale staff filtered by role = 'sale'
- Added staff information display in Requests.js:
  - Display staff names instead of IDs in both grid and table views
  - Added columns for Sale Staff, Pricing Staff, and Dispatch Staff in table view
  - Added staff information in ticket cards in grid view
- Synchronized status colors between status badges and status buttons:
  - Updated getStatusClass function to map statuses to appropriate CSS classes
  - Standardized colors for all status types across the application
  - Added specific button styles for Customer Paid and Supplier Paid statuses
- Enhanced Reports page with improved visualizations:
  - Added revenue and cost time charts to customer and supplier report tabs
  - Added ticket status distribution charts to customer and supplier report tabs
  - Implemented consistent status colors across all report tabs using a unified getStatusColor function
  - Improved "Clear filter" button to reset all filters including company and date range

### Fixed
- Fixed issue with `case 'operator' || 'accountant' || 'sale'` in Sidebar.js by using proper case syntax
- Fixed ESLint warnings about unused variables in Reports.js and Requests.js
- Fixed missing recharts library dependency
- Fixed textarea inputs to automatically increase height as user types

### Changed
- Completely redesigned Reports UI with modern dashboard layout
- Improved filter organization in Reports page
- Enhanced data visualization with better charts and tables
- Removed status filter in favor of status count cards in Reports page
- Updated RequestDetail page UI with 4-quadrant layout

## [1.2.0] - 2024-01-15

### Added
- Whitelist Platform with Query Builder functionality
- React Awesome Query Builder integration (version 6.6.14)
- Query Builder visual interface with SQL generation
- Query history and saved queries features
- Two-way binding between SQL query and visual query builder

### Changed
- Improved Query Builder UI with better group nesting (max 3 levels)
- Enhanced Query Builder with connecting lines between rules/groups
- Added background colors to differentiate nested group levels

## [1.1.0] - 2024-01-01

### Added
- Comments functionality with editing, deleting, and replying capabilities
- Facebook-style comment UI with visual connecting lines
- Support for up to 3 levels of nested replies
- Text formatting preservation in comments
- Customer summary report feature with separate tabs for customers and suppliers
- Timeline charts for visualizing customer data changes over time

### Changed
- Redesigned UI layout with more synchronized reports
- Organized reports into customer reports and supplier reports groups
- Improved dashboard layouts with summary cards and multiple dimension-based reports
- Enhanced summary tables for comprehensive data visualization

## [1.0.0] - 2023-12-15

### Added
- Initial release of the Ticket Management System
- User authentication and authorization
- Dashboard with key metrics
- Ticket management (create, view, edit, delete)
- Customer management with validation for customer name
- Supplier management cloned from Customer page with updated endpoints
- User management with proper password handling
- Reports and analytics with filtering capabilities
- Notification system
- Settings page
- Help center
- Kanban UI views for request pages

### Features
- Role-based access control (Admin, Approval, Operator, Accountant, Sale)
- Ticket status tracking with default status set to 'initial'
- Search and filter functionality with real-time search
- Pagination for data tables with items per page stored in localStorage
- Excel export for reports and data tables
- Dark mode support throughout the application
- Responsive design for mobile and desktop
- Autocomplete functionality for manager and company selection

### API Integration
- Ticket API endpoints for CRUD operations
- Customer and Supplier API integration
- User API with proper authentication
- Company API for basic information

## [0.9.0] - 2023-11-30

### Added
- Beta version with core functionality
- Basic UI components with modern, aesthetically pleasing design
- API integration with proper error handling
- Data models and services
- Sidebar component with role-based menu items
- Filter functionality for data tables

### Changed
- Improved UI interfaces to match existing system design
- Maximized utilization of horizontal screen space
- Updated table designs with sorting and filtering capabilities

### Known Issues
- Performance issues with large datasets
- Limited reporting capabilities
- Incomplete mobile responsiveness

## [0.8.0] - 2023-11-15

### Added
- Alpha version for internal testing
- Basic authentication with token-based system
- Preliminary UI design with consistent styling
- Core data structures and state management
- Local storage integration for user preferences

### Changed
- Switched from class components to functional components with hooks
- Improved state management approach with React hooks
- Enhanced form validation and error handling

## [0.7.0] - 2023-10-30

### Added
- Proof of concept with basic functionality
- Project structure setup with component organization
- Development environment configuration
- Initial component library with reusable elements
- API service structure with proper error handling

### Technical
- React 18 setup with modern practices
- Webpack configuration for optimal builds
- ESLint and Prettier setup for code quality
- CSS organization with modular approach
