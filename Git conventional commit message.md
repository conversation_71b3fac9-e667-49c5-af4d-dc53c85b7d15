## **Conventional commit message**

### Default

<pre>
<b><a href="#types">&lt;type&gt;</a></b></font>(<b><a href="#scopes">&lt;optional scope&gt;</a></b>): <b><a href="#description">&lt;description&gt;</a></b>
<sub>empty separator line</sub>
<b><a href="#body">&lt;optional body&gt;</a></b>
<sub>empty separator line</sub>
<b><a href="#footer">&lt;optional footer&gt;</a></b>
</pre>

### Merge Commit

<pre>
Merge branch '<b>&lt;branch name&gt;</b>'
</pre>

<sup>Follows default git merge message</sup>

### Revert Commit

<pre>
Revert "<b>&lt;reverted commit subject line&gt;</b>"
</pre>

<sup>Follows default git revert message</sup>

### Inital Commit

```
chore: init
```

### Types

- `feat`: Adds, modify or remove a new feature
- `fix`: Fixes a bug
- `refactor`: A code change that neither fixes a bug nor adds a feature
  - `refactor(migrate)`: migrate old code
- `perf`: A code change that improves performance
- `style`: Changes that do not affect the meaning of the code (e.g., formatting, missing semi-colons, etc.)
- `test`: Add missing tests or correcting existing tests
- `docs`: Affect documentation only
- `build`: Affect build components like build tool, ci pipeline, dependencies, project version, ...
- `ops`: Affect operational components like infrastructure, deployment, backup, recovery, ...
- `chore`: Miscellaneous commits e.g. modifying `.gitignore`
<!-- - `migrate`: migrate code -->

### Scopes

The `scope` provides additional contextual information.

- Is an **optional** part of the format
- Allowed Scopes depends on the specific project
- Don't use issue identifiers as scopes

### Breaking Changes Indicator

Breaking changes should be indicated by an `!` before the `:` in the subject line e.g. `feat(api)!: remove status endpoint`

- Is an **optional** part of the format

### Description

The `description` contains a concise description of the change.

- Is a **mandatory** part of the format
- Use the imperative, present tense: "change" not "changed" nor "changes"
  - Think of `This commit will...` or `This commit should...`
- Don't capitalize the first letter
- No dot (`.`) at the end

### Body

The `body` should include the motivation for the change and contrast this with previous behavior.

- Is an **optional** part of the format
- Use the imperative, present tense: "change" not "changed" nor "changes"
- This is the place to mention issue identifiers and their relations

### Footer

The `footer` should contain any information about **Breaking Changes** and is also the place to **reference Issues** that this commit refers to.

- Is an **optional** part of the format
- **optionally** reference an issue by its id.
- **Breaking Changes** should start with the word `BREAKING CHANGES:` followed by space or two newlines. The rest of the commit message is then used for this.

### Examples

- Adding a new feature: 
    ```
    feat: add new data ingestion pipeline for fc data
    ```
    ```
    feat(kpi): add new data ingestion pipeline for kpi manual update
    ```
    ```
    feat(telco): add schema force check before write data
    ```    
    ```
    feat!: remove column trans fc.fc_oao

    refers to JIRA-1337

    BREAKING CHANGES: ticket enpoints no longer supports list all entites.
    ```
- Fixing:
    ```
    fix: correct null handling in data transformation script
    
    The error occurred because of <reasons>.
    ```
- Refactoring code:
    ```
    refactor: modularize data validation functions
    ```
- Migration
    ```
    refactor(migrate): migrate old dags
    ```
- Improving performance:
    ```
    perf: optimize data loading process for faster execution
    ```
- Code formatting changes:
    ```
    style: remove empty line
    ```
- Adding documentation:
    ```
    docs: update README with new setup instructions
    ```
- Adding tests:
    ```
    test: add unit tests for data cleaning functions
    ```
- Miscellaneous tasks:
    ```
    chore: update dependencies for data processing library
    ```
- Build components of service
    ```
    build: update dependencies
    ```
    ```
    build(release): bump version to 1.0.0
    ```
- Deployment
    ```
    ops: upgrade airflow to latest version
    ```
