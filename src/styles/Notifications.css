.notifications-page {
  width: 100%;
  background-color: var(--bg-color, #fff);
  color: var(--text-color, #333);
  min-height: 50vh;
  padding: 0;
}

/* Page Header */
.notifications-page-header {
  display: grid;
  grid-template-columns: 1fr 3fr 2.5fr 1.5fr;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  gap: 20px;
}

.notifications-page-title {
  display: flex;
  flex-direction: column;
}

.notifications-page-title h2 {
  margin: 0 0 5px 0;
  font-size: 22px;
  font-weight: 600;
}

.notifications-count {
  color: var(--text-color, #666);
  font-size: 14px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.noti-unread-badge {
  margin-left: 10px;
  padding: 2px 8px;
  background-color: var(--error-text, #f44336);
  color: white;
  border-radius: 10px;
  font-size: 12px;
}

.notifications-filters {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
}

.notifications-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
}

/* Refresh button */
.noti-btn-refresh {
  background: none;
  border: 1px solid var(--border-color, #e0e0e0);
  color: var(--text-color, #333);
  font-size: 16px;
  padding: 8px;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.noti-btn-refresh:hover {
  background-color: var(--btn-secondary-hover, #f0f0f0);
}

/* Notifications container */
.notifications-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notifications-loading,
.notifications-error,
.notifications-empty {
  padding: 30px;
  text-align: center;
  background-color: var(--section-bg, #fff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  color: var(--text-color, #666);
}

.notifications-error {
  color: var(--error-text, #f44336);
}

/* Notifications list */
.notifications-list {
  /* display: flex; */
  flex-direction: column;
  gap: 16px;
}

/* Notification groups */
.notification-group {
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  margin-top: 10px;
}

.notification-group-header {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
  padding: 10px 20px;
  background-color: #f9f9f9;
  border-bottom: 1px solid var(--border-color, #f0f0f0);
}

.notification-group-header:hover {
  background-color: #f0f0f0;
}

.notification-group-header h4 {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin: 0;
}

.notification-group-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-count-badge {
  background-color: #f1f1f1;
  color: #555;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  min-width: 24px;
  text-align: center;
}

.noti-collapse-indicator {
  color: #888;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

/* Notification items */
.notification-item {
  display: flex;
  padding: 15px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
  border-bottom: 1px solid #f5f5f5;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.highlight {
  background-color: #f5f9ff;
}

.notification-item:hover {
  background-color: #f0f7fc;
}

.notification-item.unread {
  background-color: #e4f3ff;
}

.notification-item.unread:hover {
  background-color: #daecfc;
}

/* Notification icon */
.notification-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  margin-right: 15px;
  flex-shrink: 0;
}

.notification-icon {
  font-size: 18px;
  color: #555;
}

/* Notification content */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.notification-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
}

.notification-time {
  font-size: 12px;
  color: #888;
  margin-left: 10px;
  white-space: nowrap;
}

.notification-message {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mark as read button */
.noti-btn-mark-read {
  background: none;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #4CAF50;
  font-size: 16px;
  margin-left: 10px;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.noti-btn-mark-read:hover {
  background-color: rgba(76, 175, 80, 0.1);
}

/* Mark all as read button */
.noti-btn-mark-all-read {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  background-color: transparent;
  color: #4CAF50;
  border: 1px solid #4CAF50;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.noti-btn-mark-all-read:hover {
  background-color: rgba(76, 175, 80, 0.1);
}

/* Filter group styles */
.notification-filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color, #333);
  white-space: nowrap;
}

.notification-filter-group select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
}

/* Dark mode adjustments */
[data-theme="dark"] .notification-group {
  background-color: #2d333b;
  border-color: #444c56;
}

[data-theme="dark"] .notification-group-header {
  background-color: #22272e;
}

[data-theme="dark"] .notification-group-header h4,
[data-theme="dark"] .notification-message {
  color: #adbac7;
}

[data-theme="dark"] .notification-item {
  border-color: #373e47;
}

[data-theme="dark"] .notification-item:hover {
  background-color: #2d3339;
}

[data-theme="dark"] .notification-item.unread {
  background-color: #3a434e;
}

[data-theme="dark"] .notification-item.unread:hover {
  background-color: #414b58;
}

[data-theme="dark"] .notification-item.highlight {
  background-color: #283142;
}

[data-theme="dark"] .notification-icon-container {
  background-color: #444c56;
}

[data-theme="dark"] .notification-icon {
  color: #adbac7;
}

[data-theme="dark"] .notification-title {
  color: #e6edf3;
}

[data-theme="dark"] .notification-time {
  color: #768390;
}

[data-theme="dark"] .notification-count-badge {
  background-color: #3a3f45;
  color: #adbac7;
}

/* Dark mode adjustments for filters */
[data-theme="dark"] .notification-filter-group label {
  color: #e6edf3;
}

[data-theme="dark"] .notification-filter-group select {
  background-color: #2d333b;
  border-color: #444c56;
  color: #e6edf3;
}

[data-theme="dark"] .notification-group-header:hover {
  background-color: #333940;
}

/* Notification search styling similar to request search */
.notifications-search {
  position: relative;
  align-content: center;
  margin-left: auto;
}

.noti-search-container {
  position: relative;
}

.noti-search-input {
  padding: 8px 12px 8px 35px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 14px;
  width: 250px;
  transition: all 0.3s;
  outline: none;
}

.noti-search-input:focus {
  width: 300px;
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 16px;
}

/* Add media query for responsiveness */
@media (max-width: 900px) {
  .notifications-search {
    width: 100%;
    margin-left: 0;
    margin-bottom: 15px;
  }
  
  .noti-search-input,
  .noti-search-input:focus {
    width: 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .notifications-page-header {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
  }
  
  .notifications-search {
    grid-column: 1 / -1;
    grid-row: 1;
    margin-bottom: 15px;
  }
  
  .notifications-filters {
    grid-column: 1 / -1;
    grid-row: 2;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  
  .notifications-page-title {
    grid-column: 1;
    grid-row: 3;
  }
  
  .notifications-actions {
    grid-column: 2;
    grid-row: 3;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .notifications-page-header {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    gap: 15px;
  }
  
  .notifications-search,
  .notifications-filters,
  .notifications-page-title,
  .notifications-actions {
    grid-column: 1;
  }
  
  .notifications-search {
    grid-row: 1;
  }
  
  .notifications-filters {
    grid-row: 2;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
  
  .notifications-page-title {
    grid-row: 3;
  }
  
  .notifications-actions {
    grid-row: 4;
    width: 100%;
    justify-content: flex-end;
  }
  
  .notification-filter-group {
    width: 100%;
  }
  
  .notification-filter-group select {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .notification-item {
    padding: 12px 15px;
    flex-direction: column;
  }
  
  .notification-icon-container {
    margin-bottom: 10px;
    margin-right: 0;
  }
  
  .noti-btn-mark-read {
    position: absolute;
    top: 10px;
    right: 10px;
  }
}

/* NotificationCenter Component Styles */
.notification-container {
  position: relative;
}

.notification-bell {
  position: relative;
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  color: #555;
  transition: background-color 0.2s;
}

.notification-bell:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  font-size: 11px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifications-dropdown {
  position: absolute;
  top: 45px;
  right: 0;
  width: 380px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 1;
}

.notifications-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.mark-all {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #4CAF50;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.mark-all:hover {
  background-color: rgba(76, 175, 80, 0.1);
}

.no-notifications {
  padding: 30px 20px;
  text-align: center;
  color: #888;
  font-style: italic;
}

.notifications-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #eaeaea;
  background-color: #fff;
  position: sticky;
  bottom: 0;
}

.view-all-button {
  background: none;
  border: none;
  color: #2196F3;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 15px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.view-all-button:hover {
  background-color: rgba(33, 150, 243, 0.1);
}

/* Dark theme support for notification dropdown */
[data-theme="dark"] .notifications-dropdown,
[data-theme="dark"] .notifications-header,
[data-theme="dark"] .notifications-footer {
  background-color: #2d333b;
  border-color: #444c56;
}

[data-theme="dark"] .notification-bell {
  color: #adbac7;
}

[data-theme="dark"] .notification-bell:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .notifications-header h3 {
  color: #e6edf3;
}

[data-theme="dark"] .no-notifications {
  color: #768390;
}

/* Add responsive adjustments for notification dropdown */
@media (max-width: 768px) {
  .notifications-dropdown {
    width: 320px;
  }
}

@media (max-width: 480px) {
  .notifications-dropdown {
    width: 100vw;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }
} 