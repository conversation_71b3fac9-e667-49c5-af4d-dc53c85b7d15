.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fb;
  padding: 20px;
}

.login-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  padding: 30px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #4a6cf7;
  margin-bottom: 10px;
  font-size: 28px;
}

.login-header p {
  color: #666;
  font-size: 16px;
}

.login-error {
  background-color: #ffebee;
  color: #f44336;
  padding: 12px;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.login-error.success {
  background-color: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.login-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.login-form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #555;
}

.login-form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  font-size: 15px;
  transition: border-color 0.2s;
}

.login-form-group input:focus {
  border-color: #4a6cf7;
  outline: none;
}

.login-form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.login-remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
}

.login-remember-me input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.login-remember-me label {
  cursor: pointer;
}

.forgot-password {
  color: #4a6cf7;
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-button {
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-button:hover {
  background-color: #3a5ce5;
}

.login-button:disabled {
  background-color: #a0b0f8;
  cursor: not-allowed;
}

.login-password-input-container {
  position: relative;
  width: 100%;
}

.login-password-input-container input {
  width: 100%;
  padding-right: 40px;
}

.login-password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-password-toggle:hover {
  color: #333;
}

.login-password-toggle:focus {
  outline: none;
}

.form-switch {
  margin-top: 1rem;
  text-align: center;
}

.switch-button {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
  transition: color 0.2s;
}

.switch-button:hover {
  color: #0056b3;
  text-decoration: underline;
}

@media (max-width: 480px) {
  .login-card {
    padding: 20px;
  }
  
  .login-form-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
} 