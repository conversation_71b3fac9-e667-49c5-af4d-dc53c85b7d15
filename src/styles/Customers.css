/* Users container */
.customers-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Users header */
.customers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 10px;
}

.customers-filters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.customers-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.btn-icon-left {
  margin-right: 8px;
}

/* Users table */
.customers-table-container {
  overflow-x: auto;
}

.customers-table {
  width: 100%;
  border-collapse: collapse;
}

.customers-table th,
.customers-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.customers-table th {
  background-color: #f5f7fb;
  font-weight: 500;
  color: #555;
  cursor: pointer;
  user-select: none;
  position: relative;
}

.customers-table th:hover {
  background-color: #e9ecf5;
}

.customers-table tr:hover {
  background-color: #f9f9f9;
}

/* Role badges */
.role-badge {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.role-badge.admin {
  background-color: #e3f2fd;
  color: #2196f3;
}

.role-badge.approval {
  background-color: #e8f5e9;
  color: #4caf50;
}

.role-badge.user {
  background-color: #f5f5f5;
  color: #757575;
}

/* Status badges */
.status-badge.active {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-badge.inactive {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

.status-badge.remove {
  background-color: #f2d4d4;
  color: #eb6464;
}

/* User form */
.user-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-weight: 500;
  color: #555;
}

/* Required field indicator */
.required-field {
  color: #d32f2f;
  margin-left: 4px;
}

/* Modal error message */
.modal-error-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
  gap: 10px;
  background-color: #ffebee;
  color: #d32f2f;
  border-left: 4px solid #d32f2f;
  animation: fadeIn 0.3s ease-in-out;
}

.modal-error-message .error-icon {
  color: #d32f2f;
  font-size: 20px;
  flex-shrink: 0;
}

/* Disabled field */
.disabled-field {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.form-group input,
.form-group select {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.role-description {
  font-size: 14px;
  color: #757575;
  font-style: italic;
  margin-top: -10px;
  margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .customers-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .customers-filters {
    width: 100%;
  }

  .customers-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .filter-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-group select {
    width: 100%;
  }

  .customers-actions {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-box {
    width: 100%;
  }

  .btn-primary {
    width: 100%;
  }
}

/* Array values styling */
.array-values-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.array-value-item {
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 0.85em;
  white-space: nowrap;
}

/* Default array value style */
.array-value-default {
  background-color: #f0f0f0;
  color: #555;
}

/* Role-based colors */
.array-value-admin {
  background-color: #e4abdd;
  color: #840e51;
}

.array-value-approval {
  background-color: #edd9c4;
  color: #ca6f0e;
}

.array-value-user {
  background-color: #a8c1e7;
  color: #2475ee;
}

/* Role-based colors */
.array-value-whitelist {
  background-color: #e8eaf6;
  color: #1876f1;
}

.array-value-all {
  background-color: #e8eaf6;
  color: #f11818;
}

.array-value-funnel {
  background-color: #fff3e0;
  color: #d24fc0;
}

.array-value-zns {
  background-color: #fff3e0;
  color: #564738;
}

.array-value-sbv {
  background-color: #fce4ec;
  color: #c2185b;
}

.array-value-ads {
  background-color: #e0f7fa;
  color: #0097a7;
}

.array-value-config {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.array-value-tnex {
  background-color: #e8f5e9;
  color: #388e3c;
}

.array-value-shinhan {
  background-color: #e8f5e9;
  color: #dca358;
}

.array-value-samsung {
  background-color: #e8f5e9;
  color: #1dd5f1;
}

/* Status-based colors */
.array-value-active {
  background-color: #e8f5e9;
  color: #388e3c;
}

.array-value-inactive {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

.array-value-removed {
  background-color: #ffebee;
  color: #d32f2f;
}

/* Permission-based colors */
.array-value-read {
  background-color: #e3f2fd;
  color: #1976d2;
}

.array-value-write {
  background-color: #fff3e0;
  color: #f57c00;
}

/* Ensure table cells with array values don't overflow */
.customers-table td {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Multi-select styling */
.multi-select {
  min-height: 120px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  width: 100%;
}

.multi-select option {
  padding: 8px;
  margin-bottom: 4px;
  border-radius: 4px;
}

.multi-select option:checked {
  background-color: #e3f2fd;
  color: #1976d2;
}

.form-help {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #757575;
  font-style: italic;
}

/* Checkbox group styling */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  cursor: pointer;
}

.checkbox-text {
  font-size: 14px;
}

/* Toggle switch styling */
.toggle-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #2196F3;
}

input:focus + .toggle-slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Radio button styling */
.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.radio-label input[type="radio"] {
  margin-right: 8px;
  cursor: pointer;
}

.radio-text {
  font-size: 14px;
}