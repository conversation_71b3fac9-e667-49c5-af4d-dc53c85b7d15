/* Dark mode variables */
:root {
  --bg-color: #ffffff;
  --text-color: #333333;
  --border-color: #e0e0e0;
  --section-bg: #ffffff;
  --input-bg: #ffffff;
  --input-border: #ddd;
  --success-bg: #e7f7ed;
  --success-text: #1d915c;
  --error-bg: #ffeeee;
  --error-text: #d63031;
  --btn-primary-bg: #4caf50;
  --btn-primary-hover: #388e3c;
  --btn-secondary-bg: #f0f0f0;
  --btn-secondary-hover: #e0e0e0;
  --btn-secondary-text: #333333;
  --connection-bg: #f8f9fa;
  --filter-bg: #f8f9fa;
  --filter-option-bg: #e3f2fd;
  --filter-option-text: #1976d2;
}

[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --text-color: #ffffff;
  --border-color: #333333;
  --section-bg: #2d2d2d;
  --input-bg: #333333;
  --input-border: #444444;
  --success-bg: #1e3a1e;
  --success-text: #4caf50;
  --error-bg: #3a1e1e;
  --error-text: #ff5252;
  --btn-primary-bg: #388e3c;
  --btn-primary-hover: #2e7d32;
  --btn-secondary-bg: #333333;
  --btn-secondary-hover: #444444;
  --btn-secondary-text: #ffffff;
  --connection-bg: #2d2d2d;
  --filter-bg: #2d2d2d;
  --filter-option-bg: #1e3a1e;
  --filter-option-text: #4caf50;
}

.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
  margin: 0;
  font-size: 24px;
  color: var(--text-color);
}

.settings-actions {
  display: flex;
  gap: 10px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.settings-section {
  background-color: var(--section-bg);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  border: 1px solid var(--border-color);
}

.settings-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
  font-size: 18px;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}
/* Theme Toggle Styles */
.theme-toggle {
  display: flex;
  gap: 10px;
}

.theme-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  background-color: var(--input-bg);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s;
}

.theme-btn.active {
  background-color: var(--btn-primary-bg);
  color: white;
  border-color: var(--btn-primary-bg);
}

.theme-btn:hover {
  background-color: var(--btn-secondary-hover);
}

/* Connection Settings Styles */
.connections-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--connection-bg);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.connection-info {
  display: flex;
  gap: 15px;
  align-items: center;
}

.connection-name {
  font-weight: 500;
  color: var(--text-color);
}

.connection-type {
  padding: 2px 8px;
  background-color: var(--filter-option-bg);
  color: var(--filter-option-text);
  border-radius: 12px;
  font-size: 12px;
}

.connection-url {
  color: var(--text-color);
  opacity: 0.7;
  font-size: 14px;
}

.add-connection {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.add-connection h4 {
  margin: 0 0 15px 0;
  color: var(--text-color);
  font-size: 16px;
}

.connection-form {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr auto;
  gap: 10px;
  align-items: center;
}

.connection-form input,
.connection-form select {
  padding: 8px 12px;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--text-color);
}

@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .settings-actions {
    width: 100%;
  }
  
  .btn-save, .btn-reset {
    flex: 1;
    justify-content: center;
  }

  .connection-form {
    grid-template-columns: 1fr;
  }

  .connection-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .theme-toggle {
    flex-direction: column;
  }
} 