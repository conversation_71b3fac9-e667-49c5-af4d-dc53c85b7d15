.report-customer-container {
  padding: 20px;
  min-height: calc(100vh - 60px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.report-customer-header {
  margin-bottom: 30px;
}

.report-customer-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.report-customer-header p {
  font-size: 16px;
  color: #666;
}

.report-filter-section {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

.report-filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-end;
}

.report-filter-item {
  flex: 1;
  min-width: 200px;
}

.report-filter-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
  display: flex;
  align-items: center;
  gap: 5px;
}

.report-filter-item select,
.report-filter-item input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  color: #333;
}

.report-filter-item select:focus,
.report-filter-item input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.month-year-filter {
  flex: 1;
  min-width: 200px;
}

.month-year-inputs {
  display: flex;
  align-items: center;
  gap: 5px;
}

.month-year-inputs input {
  width: 70px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  color: #333;
  text-align: center;
}

.month-year-inputs span {
  font-size: 18px;
  color: #666;
}

.report-filter-actions {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.report-btn-filter {
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s, transform 0.2s;
}

.report-btn-filter:hover {
  transform: translateY(-2px);
}

.report-btn-filter:active {
  transform: translateY(0);
}

.report-btn-filter {
  background-color: #2196f3;
  color: white;
}

.report-btn-filter:hover {
  background-color: #1976d2;
}

.report-btn-clear {
  background-color: #f5f5f5;
  color: #333;
}

.report-btn-clear:hover {
  background-color: #e0e0e0;
}

.report-btn-export {
  background-color: #4caf50;
  color: white;
}

.report-btn-export:hover {
  background-color: #388e3c;
}

.report-btn-export:disabled {
  background-color: #9e9e9e;
  cursor: not-allowed;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.report-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #2196f3;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message,
.success-message {
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  animation: fadeIn 0.3s ease-in, fadeOut 0.5s ease-out 2.5s forwards;
}

.success-message {
  background-color: #e8f5e9;
  color: #388e3c;
  animation: fadeIn 0.3s ease-in, fadeOut 0.5s ease-out 2.5s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

.no-data-message {
  background-color: #e8f5e9;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  color: #2e7d32;
  margin-top: 20px;
}

.report-table-container {
  overflow-x: auto;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
}

.report-table th,
.report-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.report-table th {
  background-color: #f5f5f5;
  font-weight: 500;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

.report-table tr:hover {
  background-color: #f9f9f9;
}

.right-align {
  text-align: right;
}

.center-align {
  text-align: center;
}

/* Checkbox styles */
.checkbox-column {
  width: 40px;
  text-align: center;
}

.checkbox-container {
  display: block;
  position: relative;
  padding-left: 25px;
  margin: 0 auto;
  cursor: pointer;
  font-size: 16px;
  user-select: none;
  width: 20px;
  height: 20px;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 18px;
  width: 18px;
  background-color: #eee;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.checkbox-container:hover input ~ .checkmark {
  background-color: #ccc;
}

.checkbox-container input:checked ~ .checkmark {
  background-color: #2196f3;
  border-color: #2196f3;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-container .checkmark:after {
  left: 6px;
  top: 2px;
  width: 4px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.report-summary {
  display: flex;
  gap: 20px;
}

.report-summary span {
  font-weight: 500;
}

.select-loading {
  padding: 10px;
  color: #666;
  font-style: italic;
}

/* Autocomplete styles */
.autocomplete-wrapper {
  position: relative;
  width: 100%;
}

.autocomplete-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 5px 5px;
  z-index: 1000;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.autocomplete-suggestion {
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.autocomplete-suggestion:last-child {
  border-bottom: none;
}

.autocomplete-suggestion:hover {
  background-color: #f5f5f5;
}

/* Responsive styles */
@media (max-width: 768px) {
  .report-filter-group {
    flex-direction: column;
  }

  .report-filter-item {
    width: 100%;
  }

  .report-filter-actions {
    flex-direction: column;
    width: 100%;
  }

  .report-btn-filter {
    width: 100%;
  }
}
