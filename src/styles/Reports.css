.reports-container {
  /* padding: 20px; */
  /* background-color: #f5f7fa; */
  min-height: calc(100vh - 60px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Summary Cards Styles */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  /* margin-bottom: 20px; */
}

.summary-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.summary-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 100%);
  transform: skewX(-15deg) translateX(70px);
  transition: transform 0.5s;
}

.summary-card:hover::after {
  transform: skewX(-15deg) translateX(170px);
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #e3f2fd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: #2196f3;
}

.summary-content {
  flex: 1;
}

.summary-content h3 {
  margin: 0 0 5px;
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.summary-number {
  font-size: 22px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px;
}

.summary-trend {
  font-size: 12px;
  margin: 0;
  display: flex;
  align-items: center;
}

.summary-trend.positive {
  color: #4caf50;
}

.summary-trend.negative {
  color: #f44336;
}

.reports-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.reports-header h2 {
  margin: 10px 0;
  font-size: 24px;
  color: #333;
}

.main-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  width: 100%;
}

.main-tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  color: #555;
  transition: all 0.3s;
  flex: 1;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.main-tab-button:hover {
  background-color: #f9f9f9;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.main-tab-button.active {
  background-color: #819cfe;
  color: white;
  font-weight: 500;
  /* box-shadow: 0 4px 6px rgba(33, 150, 243, 0.3); */
}

.reports-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.reports-actions {
  display: flex;
  gap: 10px;
}

.report-btn-export {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.report-btn-export:hover {
  background-color: #45a049;
}

.report-filter-container {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

/* .report-filter-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #2196f3, #00c49f);
  z-index: 1;
} */

.report-filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: flex-end;
  margin-bottom: 15px;
  position: relative;
}

.report-filter-group.compact {
  gap: 10px;
}

.report-filter-group:first-child::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  height: 1px;
  background: #f0f0f0;
}

.report-filter-group:last-child {
  margin-bottom: 0;
}

.report-filter-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
  min-width: 180px;
  position: relative;
}

.report-filter-item.date-filter {
  min-width: 150px;
  max-width: 180px;
}

.report-filter-item.others-filter {
  min-width: 200px;
  max-width: 220px;
}

.report-filter-item label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
  color: #555;
  font-weight: 500;
  margin-bottom: 3px;
}

.report-filter-item input,
.report-filter-item select {
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  background-color: #f9f9f9;
  transition: all 0.3s;
}

.report-filter-item input:hover,
.report-filter-item select:hover {
  border-color: #bdbdbd;
}

.report-filter-item input:focus,
.report-filter-item select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.15);
  background-color: white;
}

.select-loading {
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  color: #888;
  background-color: #f9f9f9;
}

.report-filter-actions {
  display: flex;
  /* justify-content: flex-end;
  align-items: flex-end; */
  gap: 10px;
  min-width: 220px;
  flex-direction: row;
}

.report-btn-filter {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  min-width: 100px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(33, 150, 243, 0.3);
}

.report-btn-filter:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

.report-btn-filter:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(33, 150, 243, 0.3);
}

.report-btn-filter.report-btn-clear {
  background-color: #f5f5f5;
  color: #555;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.report-btn-filter.report-btn-clear:hover {
  background-color: #e0e0e0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.report-loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #2196f3;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border-left: 4px solid #d32f2f;
}

.no-data-message {
  background-color: #e8f5e9;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  color: #2e7d32;
  margin-top: 20px;
}

.report-content {
  background-color: transparent;
  padding: 0;
}

/* Dashboard Grid Layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

/* Report Group Styles */
.report-group {
  margin-bottom: 30px;
}

.report-group-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #eaeaea;
  display: flex;
  align-items: center;
  gap: 8px;
}

.report-group-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.report-group-content.full-width {
  grid-column: 1 / -1;
}

.report-group-content.single-column {
  grid-template-columns: 1fr;
}

.report-group-content.cards-grid {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.report-group-content.three-cards-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.dashboard-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  margin-top: 20px;
}

.dashboard-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.dashboard-card.full-width {
  grid-column: 1 / -1;
}

.dashboard-card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f8faff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-card-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* .search-box {
  display: flex;
  align-items: center;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  gap: 8px;
}

.search-box input {
  border: none;
  outline: none;
  font-size: 14px;
  width: 150px;
} */

.action-button {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.action-button:hover {
  background-color: #1976d2;
}

.dashboard-card-body {
  padding: 20px;
}

.report-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #555;
  transition: all 0.3s;
}

.tab-button:hover {
  background-color: #f5f5f5;
}

.tab-button.active {
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: 500;
}

.report-table-container {
  overflow-x: auto;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 4px;
  overflow: hidden;
}

.report-table th,
.report-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.report-table th {
  background-color: #f5f5f5;
  font-weight: 500;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

.report-table tbody tr:hover {
  background-color: #f9f9f9;
}

.report-table tfoot {
  font-weight: 500;
}

.report-table tfoot td {
  border-top: 2px solid #ddd;
  background-color: #f5f5f5;
}

.profit-positive {
  color: #4caf50;
  font-weight: 500;
}

.profit-negative {
  color: #f44336;
  font-weight: 500;
}

/* Modern table styles */
.modern-table {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-table th {
  background-color: #2196f3;
  color: white;
  font-weight: 400;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
  padding: 15px;
}

.modern-table tbody tr {
  transition: background-color 0.2s;
}

.modern-table tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

.modern-table tbody tr:hover {
  background-color: #e3f2fd;
}

.center-align {
  text-align: center;
}

.right-align {
  text-align: right;
}

.entity-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.entity-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #2196f3;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.progress-bar-container {
  width: 100%;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 12px;
  font-weight: 500;
}

/* Ranking table styles */
.ranking-table-container {
  max-height: 300px;
  overflow-y: auto;
}

.ranking-table {
  width: 100%;
  border-collapse: collapse;
}

.ranking-table th,
.ranking-table td {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
}

.ranking-table th {
  background-color: #f5f5f5;
  font-weight: 500;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

.rank-cell {
  font-weight: 600;
  text-align: center;
  width: 40px;
}

.percentage-bar-container {
  width: 100%;
  height: 16px;
  background-color: #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  min-width: 60px; /* Thêm chiều rộng tối thiểu */
}

.percentage-bar {
  height: 100%;
  background-color: #2196f3;
  transition: width 0.3s ease;
}

.percentage-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 11px;
  font-weight: 500;
}

.chart-content {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Căn giữa biểu đồ tròn */
.dashboard-card-body .chart-content .recharts-wrapper {
  margin: 0 auto;
}

/* Modern Chart Styles */
.revenue-timeline-card,
.customer-analysis-card {
  position: relative;
  overflow: hidden;
}

.revenue-timeline-card::before,
.customer-analysis-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #2196f3, #00c49f);
  z-index: 1;
}

.chart-period-selector,
.chart-view-selector {
  display: flex;
  gap: 5px;
}

.period-button,
.view-button {
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.period-button:hover,
.view-button:hover {
  background-color: #e0e0e0;
}

.period-button.active,
.view-button.active {
  background-color: #2196f3;
  color: white;
}

.chart-summary {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-summary-item {
  display: flex;
  flex-direction: column;
}

.chart-summary-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.chart-summary-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.chart-summary-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.chart-summary-trend.positive {
  color: #4caf50;
}

.chart-summary-trend.negative {
  color: #f44336;
}

.timeline-chart,
.bar-chart {
  margin-top: 10px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

/* Modern Report Header */
.reports-header.modern {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.reports-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.reports-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* Report View Tabs */
.report-view-tabs {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.report-view-tab {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  background-color: #f5f5f5;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.report-view-tab:hover {
  background-color: #e0e0e0;
}

.report-view-tab.active {
  background-color: #4c6ef5;
  color: white;
}

/* Collapsible Filter Container */
.report-filter-container {
  transition: all 0.3s ease;
  overflow: hidden;
  margin-bottom: 20px;
}

.report-filter-container.collapsed {
  max-height: 0;
  opacity: 0;
  margin: 0;
  padding: 0;
}

.report-filter-container.expanded {
  max-height: 500px;
  opacity: 1;
}

.report-btn-filter-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  color: #555;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.report-btn-filter-toggle:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

/* Report Content Container */
.report-content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.report-summary-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Dashboard Grid Layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  /* margin-top: 20px; */
}

.dashboard-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.dashboard-card.full-width {
  grid-column: 1 / -1;
}

.dashboard-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.dashboard-card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dashboard-card-body {
  padding: 20px;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  background-color: #f5f5f5;
  color: #555;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #e0e0e0;
}

/* Entity Name with Avatar */
.entity-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.entity-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #4c6ef5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

/* Progress Bar */
.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -18px;
  right: 0;
  font-size: 12px;
  font-weight: 500;
}

/* Chart Selectors */
.chart-period-selector, .chart-view-selector, .chart-dimension-selector {
  display: flex;
  gap: 5px;
}

.chart-dimension-selector {
  margin-left: 16px;
}

.period-button, .view-button, .dimension-button {
  padding: 5px 12px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  color: #555;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  text-align: center;
}

.dimension-button {
  min-width: 80px;
}

.period-button:hover, .view-button:hover, .dimension-button:hover {
  background-color: #f5f5f5;
  border-color: #d0d0d0;
}

.period-button.active, .view-button.active, .dimension-button.active {
  background-color: #4c6ef5;
  color: white;
  border-color: #4c6ef5;
  box-shadow: 0 2px 4px rgba(76, 110, 245, 0.3);
}

/* Chart Summary */
.chart-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.chart-summary-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.chart-summary-label {
  font-size: 13px;
  color: #666;
}

.chart-summary-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chart-summary-trend {
  font-size: 12px;
  font-weight: 500;
}

.chart-summary-trend.positive {
  color: #4caf50;
}

.chart-summary-trend.negative {
  color: #f44336;
}

.chart-summary-value.profit {
  color: #ff0000;
  font-weight: bold;
}

/* Revenue Profit Chart */
.revenue-profit-chart {
  margin-top: 20px;
}

/* Modern Table Styles */
.report-table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.modern-table th,
.modern-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.modern-table th {
  background-color: #f9f9f9;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

.modern-table tbody tr:hover {
  background-color: #f5f5f5;
}

.modern-table .center-align {
  text-align: center;
}

.modern-table .right-align {
  text-align: right;
}

.modern-table .profit-positive {
  color: #4caf50;
  font-weight: 500;
}

.modern-table .profit-negative {
  color: #f44336;
  font-weight: 500;
}

.modern-table tfoot {
  font-weight: 600;
  background-color: #f9f9f9;
}

.modern-table tfoot td {
  border-top: 2px solid #ddd;
}

/* Ranking Table */
.ranking-table-container {
  overflow-x: auto;
}

.ranking-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.ranking-table th,
.ranking-table td {
  padding: 10px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

/* Thêm chiều rộng cố định cho cột tỷ lệ */
.ranking-table th:last-child,
.ranking-table td:last-child {
  width: 75px; /* Giảm chiều rộng xuống một nửa */
}

.ranking-table th {
  font-weight: 600;
  color: #333;
}

.ranking-table tbody tr:nth-child(1) {
  background-color: rgba(255, 215, 0, 0.1);
}

.ranking-table tbody tr:nth-child(2) {
  background-color: rgba(192, 192, 192, 0.1);
}

.ranking-table tbody tr:nth-child(3) {
  background-color: rgba(205, 127, 50, 0.1);
}

/* No Data Message */
.no-data-message {
  text-align: center;
  padding: 40px 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.no-data-message p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* Status Distribution Table */
.status-distribution-table {
  margin-top: 20px;
  max-height: 300px;
  overflow-y: auto;
}

/* Stacked Bar Chart */
.recharts-default-tooltip {
  background-color: rgba(255, 255, 255, 0.95) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: none !important;
  padding: 10px 15px !important;
}

.recharts-tooltip-label {
  font-weight: bold !important;
  margin-bottom: 5px !important;
}

.recharts-tooltip-item-name,
.recharts-tooltip-item-value {
  color: #333 !important;
}

.status-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.status-table th,
.status-table td {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
}

.status-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

.status-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  display: inline-block;
  margin-right: 8px;
  vertical-align: middle;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Responsive styles */
@media (max-width: 768px) {
  .report-filter-group {
    flex-direction: column;
  }

  .report-filter-item {
    width: 100%;
  }

  .reports-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .report-tabs {
    flex-wrap: wrap;
  }

  .report-status-count {
    flex-wrap: wrap;
  }
}
