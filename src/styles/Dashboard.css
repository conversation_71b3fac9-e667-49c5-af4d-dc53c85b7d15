.dashboard-container {
  padding: 20px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
  margin: 0 0 10px;
  font-size: 16px;
  color: #666;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px;
}

.stat-trend {
  font-size: 14px;
  margin: 0;
}

.stat-trend.positive {
  color: #4caf50;
}

.stat-trend.negative {
  color: #f44336;
}

.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin: 0 0 20px;
  font-size: 18px;
  color: #333;
}

.chart-content {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Pie Chart Styles */
.pie-chart-container {
  position: relative;
}

.pie-chart-legend {
  list-style: none;
  margin: 0;
  padding: 0;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  max-height: 100%;
  overflow-y: auto;
  min-width: 150px;
  border: 1px solid #f0f0f0;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 6px;
  border: 1px solid transparent;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-item:hover {
  background-color: #f9f9f9;
  border-color: #eaeaea;
  transform: translateX(-2px);
}

.legend-marker {
  width: 14px;
  height: 14px;
  border-radius: 4px;
  margin-right: 10px;
  flex-shrink: 0;
}

.legend-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.legend-text {
  font-weight: 500;
  white-space: nowrap;
  flex: 1;
}

/* Remove legend-value styles */
.legend-content,
.legend-value {
  display: none;
}

/* Custom Tooltip Styles */
.custom-tooltip {
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-tooltip .label {
  margin: 0;
  font-size: 12px;
  color: #333;
  white-space: nowrap;
}

.line-tooltip {
  min-width: 150px;
}

.tooltip-date {
  margin: 0 0 8px;
  font-size: 12px;
  font-weight: 500;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.tooltip-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tooltip-item {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.tooltip-marker {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.tooltip-label {
  color: #666;
  margin-right: 6px;
}

.tooltip-value {
  font-weight: 500;
  color: #333;
}

/* Recharts Custom Styles */
.recharts-default-legend {
  margin-bottom: 10px !important;
}

.recharts-legend-item {
  margin-right: 15px !important;
}

.recharts-cartesian-axis-tick-value {
  font-size: 11px;
}

.recharts-reference-line-label text {
  font-size: 11px;
  fill: #ff0000;
}

.recharts-brush-texts {
  font-size: 10px;
}

/* Recent Tickets Table */
.recent-tickets {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.recent-tickets h3 {
  margin: 0 0 20px;
  font-size: 18px;
  color: #333;
}

.tickets-table {
  width: 100%;
  border-collapse: collapse;
}

.tickets-table th,
.tickets-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.tickets-table th {
  font-weight: 500;
  color: #666;
  background: #f9f9f9;
}

.tickets-table tr.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s;
}

.tickets-table tr.clickable-row:hover {
  background-color: #f5f5f5;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.new {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-badge.pending {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-badge.completed {
  background-color: #e8f5e9;
  color: #388e3c;
} 