.sidebar {
  width: 260px;
  background-color: #fff;
  height: 100vh;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: sticky;
  top: 0;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.logo {
  font-size: 20px;
  color: #4a6cf7;
  margin: 0;
}

.user-info {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details h3 {
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

.user-details p {
  font-size: 14px;
  color: #888;
  margin: 5px 0 0;
}

.sidebar-menu {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.sidebar-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  padding: 12px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s;
  border-left: 3px solid transparent;
}

.sidebar-menu li:hover {
  background-color: #f5f7fb;
}

.sidebar-menu li.active {
  background-color: #f0f4ff;
  border-left-color: #4a6cf7;
}

.menu-icon {
  font-size: 20px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
}

.sidebar-menu li.active .menu-icon {
  color: #4a6cf7;
}

.menu-label {
  font-size: 15px;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  padding: 10px;
  width: 100%;
  text-align: left;
  cursor: pointer;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.logout-btn:hover {
  background-color: #f5f7fb;
}

.logout-icon {
  font-size: 18px;
  color: #555;
}

.logout-btn:hover .logout-icon {
  color: #4a6cf7;
}

.user-avatar-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: #4a6cf7;
  background-color: #f0f4ff;
  padding: 5px;
} 