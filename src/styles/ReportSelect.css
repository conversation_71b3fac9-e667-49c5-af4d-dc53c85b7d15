.report-select-container {
  padding: 20px;
  min-height: calc(100vh - 60px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.report-select-header {
  margin-bottom: 30px;
  text-align: center;
}

.report-select-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.report-select-header p {
  font-size: 16px;
  color: #666;
}

.report-select-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.report-select-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 100%;
}

.report-select-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.report-select-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 100%);
  transform: skewX(-15deg) translateX(70px);
  transition: transform 0.5s;
}

.report-select-card:hover::after {
  transform: skewX(-15deg) translateX(170px);
}

.report-card-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: #e3f2fd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  font-size: 30px;
  color: #2196f3;
}

.report-card-content {
  flex: 1;
}

.report-card-content h3 {
  margin: 0 0 10px;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.report-card-content p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* Responsive styles */
@media (max-width: 768px) {
  .report-select-cards {
    grid-template-columns: 1fr;
  }
  
  .report-select-card {
    padding: 20px;
  }
}
