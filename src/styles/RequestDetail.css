/* TicketChiTiet.css */
.ticket-detail-container {
  padding: 20px;
}

.ticket-detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #4a90e2;
  cursor: pointer;
  font-size: 16px;
  margin-right: 20px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.back-button:hover {
  background-color: rgba(74, 144, 226, 0.1);
}

.back-button svg {
  margin-right: 8px;
}

.ticket-detail-title {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.ticket-detail-title h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.ticket-detail-title .ticket-code {
  color: #666;
  font-size: 14px;
  margin-top: 4px;
}

.ticket-status {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
}

.ticket-status.initial {
  background-color: #f2eddf;
  color: #856404;
}

.ticket-status.approved {
  background-color: #e8f5e9;
  color: #155724;
}

.ticket-status.delivered {
  background-color: #e3f2fd;
  color: #004085;
}

.ticket-status.customerpaid {
  background-color: #f8d7da;
  color: #721c24;
}

.ticket-status.supplierpaid {
  background-color: #e7c2e5;
  color: #721c24;
}

.ticket-status.completed {
  background-color: #d1e7dd;
  color: #0f5132;
}

.ticket-detail-content {
  display: flex;
  flex-direction: column;
  
  gap: 20px;
}

.ticket-detail-row {
  display: flex;
  gap: 20px;
}

.ticket-detail-section {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: all 0.3s ease;
}

.ticket-detail-section:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  /* transform: translateY(-2px); */
}

.ticket-detail-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
}

.ticket-detail-section h3 svg {
  margin-right: 8px;
  color: #4a90e2;
}

.detail-item {
  margin-bottom: 12px;
  align-items: center;
}

.detail-label {
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
  font-size: 14px;
}

.detail-value {
  color: #333;
  font-size: 15px;
}

/* Table styles */
.price-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  /* overflow: hidden; */
}

.price-table th {
  background-color: #f5f7fb;
  padding: 12px 10px;
  text-align: left;
  font-weight: 500;
  color: #555;
  border-bottom: 1px solid #e0e0e0;
  text-align: left;
}

.price-table td {
  padding: 12px 5px;
  border-bottom: 1px solid #e0e0e0;
}

.price-table td:nth-child(2),
.price-table td:nth-child(4) {
  text-align: left;
}

.price-table tr:last-child td {
  border-bottom: none;
}

.price-table tr:hover {
  background-color: #f9f9f9;
}

.price-table td:nth-child(1),
.price-table th:nth-child(1) {
  width: 40%;
}

.price-table td:nth-child(2),
.price-table th:nth-child(2) {
  width: 22%;
}

.price-table td:nth-child(3),
.price-table th:nth-child(3) {
  width: 6%;
}

.price-table td:nth-child(3),
.price-table th:nth-child(3) {
  width: 22%;
}

/* Timeline styles */
.timeline-container {
  margin-top: 15px;
}

.timeline-item {
  display: flex;
  position: relative;
  margin-bottom: 20px;
}

.timeline-item.current .timeline-icon {
  background-color: #4a90e2;
  color: white;
  box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.2);
}

.timeline-item.current .timeline-status {
  color: #4a90e2;
  font-weight: 600;
}

.timeline-item:before {
  content: '';
  position: absolute;
  left: 15px;
  top: 30px;
  bottom: -20px;
  width: 2px;
  background-color: #eee;
}

.timeline-item:last-child:before {
  display: none;
}

.timeline-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f5f7fb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  position: relative;
  z-index: 2;
  color: #4a90e2;
  transition: all 0.3s ease;
}

.timeline-content {
  flex: 1;
}

.timeline-status {
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-meta {
  display: flex;
  font-size: 13px;
  color: #666;
}

.timeline-user {
  margin-right: 15px;
}

.timeline-date {
  color: #888;
}

/* Activity log styles */
.activity-item {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.activity-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f5f7fb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #4a90e2;
}

.activity-content {
  flex: 1;
}

.activity-type {
  font-weight: 500;
  margin-bottom: 4px;
}

.activity-value {
  margin-bottom: 4px;
  color: #333;
}

.activity-meta {
  display: flex;
  font-size: 13px;
  color: #666;
}

.activity-user {
  margin-right: 15px;
}

.activity-date {
  color: #888;
}

/* Status update button */
.update-status-button {
  margin-left: 15px;
  background: none;
  border: 1px solid #4a90e2;
  border-radius: 4px;
  color: #4a90e2;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.update-status-button:hover {
  background-color: #4a90e2;
  color: white;
}

.update-status-button svg {
  margin-right: 5px;
}

/* Status modal */
.status-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.status-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  padding: 25px;
  width: 500px;
  max-width: 90%;
}

.status-modal h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.status-current {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-options {
  margin-bottom: 25px;
}

.status-options h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
}

.status-option {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.status-option:hover:not(.disabled) {
  background-color: #f5f7fb;
}

.status-option.selected {
  background-color: #e3f2fd;
  border: 1px solid #4a90e2;
}

.status-option.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.status-option-icon {
  margin-right: 12px;
  color: #4a90e2;
}

.status-option-label {
  font-weight: 500;
}

.status-option-message {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
  font-style: italic;
}

.status-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn-cancel {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #f5f5f5;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  color: #666;
  font-weight: 500;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
}

.btn-confirm {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #4a90e2;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  color: white;
  font-weight: 500;
}

.btn-confirm:hover:not(:disabled) {
  background-color: #3a7bc8;
}

.btn-confirm:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  /* animation: spin 1s ease-in-out infinite; */
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 768px) {
  .ticket-detail-row {
    flex-direction: column;
  }

  .ticket-detail-section {
    margin-bottom: 20px;
  }

  .ticket-detail-header {
    flex-wrap: wrap;
  }

  .update-status-button {
    margin-top: 10px;
    margin-left: auto;
  }
}
