.create-request-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.create-request-header {
  text-align: center;
  margin-bottom: 40px;
}

.create-request-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.create-request-header p {
  font-size: 16px;
  color: #666;
}

.request-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.request-type-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
}

.request-type-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.request-type-icon {
  padding: 32px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.icon-container {
  background-color: #fff;
  width: 84px;
  height: 84px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.request-type-content {
  padding: 24px;
  flex-grow: 1;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.request-type-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.request-type-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* Dark mode styles */
[data-theme="dark"] .request-type-card {
  background-color: #2d333b;
  border-color: #444c56;
}

[data-theme="dark"] .request-type-content {
  background-color: #22272e;
  border-color: #444c56;
}

[data-theme="dark"] .request-type-content h3 {
  color: #e6edf3;
}

[data-theme="dark"] .request-type-content p {
  color: #adbac7;
}

[data-theme="dark"] .create-request-header h2 {
  color: #e6edf3;
}

[data-theme="dark"] .create-request-header p {
  color: #adbac7;
}

[data-theme="dark"] .icon-container {
  background-color: #22272e;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .request-type-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
  
  .request-type-icon {
    padding: 24px 0;
  }
  
  .icon-container {
    width: 72px;
    height: 72px;
  }
  
  .request-type-content {
    padding: 16px;
  }
}

/* Animation for card hover */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
  }
}

.request-type-card:active {
  transform: scale(0.98);
} 