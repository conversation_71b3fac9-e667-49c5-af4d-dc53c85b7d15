.request-page-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 0 0 15px;
  border-bottom: 1px solid #eee;
}

.request-filters {
  display: flex;
  gap: 15px;
  align-items: center;
}

.request-filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.request-filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
  white-space: nowrap;
}

.request-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
  min-width: 120px;
}

.request-search {
  position: relative;
  align-content: center;
  margin-left: auto;
}

.request-search-container {
  position: relative;
}

.request-search-input {
  padding: 8px 12px 8px 35px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 14px;
  width: 220px;
  transition: all 0.3s;
  outline: none;
}

.request-search-input:focus {
  width: 260px;
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 16px;
}

.request-actions {
  display: flex;
  gap: 10px;
  margin-left: 10px;
}

.request-btn-refresh, .request-btn-view-mode, .request-btn-clear {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #555;
  padding: 5px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.request-btn-refresh:hover, .request-btn-view-mode:hover, .request-btn-clear:hover {
  background-color: #f5f5f5;
}

.request-btn-view-mode, .request-btn-clear {
  border: 1px solid #ddd;
  padding: 6px 12px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.request-btn-view-mode:hover {
  background-color: #f0f2f5;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.request-status-count {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  margin-top: 15px;
  padding-bottom: 5px;
  width: 100%;
}

.request-status-btn {
  padding: 8px 16px;
  border-radius: 20px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.request-status-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Default style for the All button */
.request-status-btn.btn-all {
  background-color: #f0f2f5;
  color: #333;
}

.request-status-btn.btn-all.active {
  background-color: #4c6ef5;
  color: white;
}
/* Initial button */
.request-status-btn.btn-initial {
  background-color: #f2eddf;
  color: #856404;
}

.request-status-btn.btn-initial.active {
  background-color: #f5d784;
  color: white;
}

/* Pending button */
.request-status-btn.btn-pending {
  background-color: #fff8e1;
  color: #856404;
}

.request-status-btn.btn-pending.active {
  background-color: #ffc107;
  color: white;
}

/* Approved button */
.request-status-btn.btn-approved {
  background-color: #e8f5e9;
  color: #155724;
}

.request-status-btn.btn-approved.active {
  background-color: #4caf50;
  color: white;
}

/* In Progress button */
.request-status-btn.btn-progress {
  background-color: #e3f2fd;
  color: #004085;
}

.request-status-btn.btn-progress.active {
  background-color: #2196f3;
  color: white;
}

/* Done button */
.request-status-btn.btn-done {
  background-color: #d1e7dd;
  color: #0f5132;
}

.request-status-btn.btn-done.active {
  background-color: #198754;
  color: white;
}

/* Rejected button */
.request-status-btn.btn-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.request-status-btn.btn-rejected.active {
  background-color: #dc3545;
  color: white;
}

/* Customer Paid button */
.request-status-btn.btn-customerpaid {
  background-color: #f8d7da;
  color: #721c24;
}

.request-status-btn.btn-customerpaid.active {
  background-color: #e96772;
  color: white;
}

/* Supplier Paid button */
.request-status-btn.btn-supplierpaid {
  background-color: #e7c2e5;
  color: #721c24;
}

.request-status-btn.btn-supplierpaid.active {
  background-color: #e87ee3;
  color: white;
}

/* Add more custom styles as needed */

.request-empty {
  text-align: center;
  padding: 40px;
  background-color: #f9f9f9;
  border-radius: 8px;
  color: #666;
}

.request-grid-container {
  display: flex;
  flex-direction: column;
  margin-top: 15px;
}

.request-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.request-item {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 16px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.request-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.request-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.request-item-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  word-break: break-word;
}

.request-item-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.request-status-initial {
  background-color: #f2eddf;
  color: #856404;
}

.request-status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.request-status-approved {
  background-color: #d4edda;
  color: #155724;
}

.request-status-rejected {
  background-color: #dc3545;
  color: #721c24;
}

.request-status-progress {
  background-color: #cce5ff;
  color: #004085;
}

.request-status-done {
  background-color: #d1e7dd;
  color: #0f5132;
}

.request-item-info {
  margin-bottom: 12px;
  font-size: 14px;
}

.request-item-info p {
  margin: 5px 0;
}

.request-item-label {
  font-weight: 500;
  color: #666;
}

.request-item-footer {
  display: flex;
  justify-content: flex-end;
  font-size: 12px;
  color: #888;
}

@media (max-width: 900px) {
  .request-page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .request-filters {
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 15px;
  }

  .request-search {
    width: 100%;
    margin-left: 0;
    margin-bottom: 15px;
  }

  .request-search-input,
  .request-search-input:focus {
    width: 100%;
  }

  .request-actions {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .request-list {
    grid-template-columns: 1fr;
  }

  .request-filter-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .request-select {
    width: 100%;
  }

  .request-status-count {
    flex-wrap: wrap;
  }
}

/* Styles cho bảng và sắp xếp */
.tickets-table th {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.sort-icon {
  margin-left: 5px;
  font-size: 14px;
  vertical-align: middle;
}

.sort-inactive {
  opacity: 0.3;
}

/* Styles cho action buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s;
  color: #555;
}

.btn-icon.view:hover {
  background-color: #e3f2fd;
  color: #2196f3;
}

.btn-icon.edit:hover {
  background-color: #e8f5e9;
  color: #4caf50;
}

.btn-icon.delete:hover {
  background-color: #ffebee;
  color: #f44336;
}

/* Styles cho pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 5px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  background-color: white;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  min-width: 40px;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f7fb;
}

.pagination-btn.active {
  background-color: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Ticket details styles */
.ticket-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ticket-detail-row {
  display: flex;
  align-items: flex-start;
}

.ticket-detail-label {
  width: 120px;
  font-weight: 500;
  color: #555;
}

.ticket-detail-value {
  flex: 1;
}

.detail-input,
.detail-select,
.detail-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.detail-textarea {
  min-height: 100px;
  resize: vertical;
}

/* Delete confirmation */
.delete-confirmation {
  text-align: center;
  padding: 20px 0;
}

.delete-confirmation p {
  margin-bottom: 15px;
}

/* Thêm vào cuối file */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

/* Table Styles */
.request-table-container {
  overflow-x: auto;
  margin-top: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding-bottom: 20px;
}

.tickets-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.tickets-table th,
.tickets-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.tickets-table th {
  background-color: #f5f7fb;
  font-weight: 500;
  color: #555;
  position: sticky;
  top: 0;
  z-index: 10;
}

.sortable-header {
  cursor: pointer;
  user-select: none;
  position: relative;
  padding-right: 25px !important;
  transition: background-color 0.2s;
}

.sortable-header:hover {
  background-color: #e9ecf3;
}

.sort-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  display: inline-flex;
  align-items: center;
  color: #4a6cf7;
}

.tickets-table tr:hover {
  background-color: #f9f9f9;
}

.tickets-table tr:last-child td {
  border-bottom: none;
}

/* Table controls */
.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 15px;
  background-color: #f9f9f9;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #e0e0e0;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 10px;
}

.items-per-page label {
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

.items-per-page-select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin: 20px 0 10px;
}

.pagination-btn {
  min-width: 36px;
  height: 36px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f0f2f5;
  border-color: #ccc;
}

.pagination-btn.active {
  background-color: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  font-size: 14px;
  color: #666;
}

.pagination-info {
  text-align: center;
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  padding: 0 15px;
}

/* Tickets Filters */
.tickets-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

/* Dark mode support */
[data-theme="dark"] .tickets-table th {
  background-color: #22272e;
  color: #adbac7;
}

[data-theme="dark"] .tickets-table th,
[data-theme="dark"] .tickets-table td {
  border-color: #444c56;
}

[data-theme="dark"] .tickets-table tr:hover {
  background-color: #2d3339;
}

[data-theme="dark"] .tickets-filters {
  border-color: #444c56;
}

[data-theme="dark"] .filter-group label {
  color: #adbac7;
}

[data-theme="dark"] .filter-group select {
  background-color: #2d333b;
  border-color: #444c56;
  color: #adbac7;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tickets-filters {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-group {
    width: 100%;
  }

  .filter-group select {
    width: 100%;
  }

  .table-controls {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .pagination {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .tickets-table th,
  .tickets-table td {
    padding: 10px 8px;
    font-size: 13px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .pagination-btn {
    min-width: 32px;
    height: 32px;
    padding: 0 8px;
    font-size: 12px;
  }

  .pagination-ellipsis {
    min-width: 20px;
  }

  .items-per-page {
    width: 100%;
    justify-content: space-between;
  }
}

/* Loading and Error States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  width: 100%;
}

/* .loading-spinner {
  animation: spin 1s linear infinite;
  font-size: 32px;
  color: #4a6cf7;
  margin-bottom: 16px;
} */

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  width: 100%;
  text-align: center;
  padding: 0 20px;
}

.error-icon {
  font-size: 32px;
  color: #f44336;
  margin-bottom: 16px;
}

.btn-retry {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-retry:hover {
  background-color: #3a5ce5;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

/* Match status badge colors with button colors */
.status-badge.initial {
  background-color: #f2eddf;
  color: #721c24;
}

.status-badge.approved {
  background-color: #e8f5e9;
  color: #155724;
}
.status-badge.delivered {
  background-color: #004085;
  color: #004085;
}

.status-badge.pending,
.status-badge.priced {
  background-color: #fff8e1;
  color: #856404;
}

.status-badge.progress,
.status-badge.delivered {
  background-color: #e3f2fd;
  color: #004085;
}

.status-badge.rejected{
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge.customerpaid{
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge.supplierpaid {
  background-color: #e7c2e5;
  color: #721c24;
}

.status-badge.done,
.status-badge.completed {
  background-color: #e8f5e9;
  color: #0f5132;
}

/* Dark mode support for new elements */
[data-theme="dark"] .loading-spinner {
  color: #6d8eff;
}

[data-theme="dark"] .btn-retry {
  background-color: #6d8eff;
}

[data-theme="dark"] .btn-retry:hover {
  background-color: #5a7aff;
}