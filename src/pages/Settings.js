import React, { useState, useEffect } from 'react';
import { FiSave, FiRotateCw, FiAlertCircle, FiCheckCircle, FiPlus, FiTrash2, FiMoon, FiSun, FiChevronDown, FiChevronUp, FiUser, FiUserCheck } from 'react-icons/fi';
import { useTheme } from '../context/ThemeContext';
import { translations } from '../config/translations';
import '../styles/Settings.css';

const Settings = () => {
  const { theme, language, toggleTheme, changeLanguage } = useTheme();
  const t = translations[language].settings;

  const [settings, setSettings] = useState({
    // UI Settings
    itemsPerPage: 10,

    // Notification Settings
    emailNotifications: true,
    userCreationNotify: true,
    userModificationNotify: true,

    // Connection Settings
    connections: [
      { id: 1, name: 'DataQuest', type: 'platform', url: 'https://dataquest.example.com' },
      { id: 2, name: 'WLP', type: 'platform', url: 'https://wlp.example.com' },
      { id: 3, name: 'ADS', type: 'platform', url: 'https://ads.example.com' },
      { id: 4, name: 'SBV', type: 'platform', url: 'https://sbv.example.com' },
      { id: 5, name: 'Ecommerce', type: 'platform', url: 'https://ecommerce.example.com' }
    ],

    // Approver & Executor Settings
    approvers: {
      'access-platform': {
        whitelist: { approvers: ['user1', 'user2'], executors: ['admin1'] },
        funnel: { approvers: ['user3'], executors: ['admin2'] },
        zdslab: { approvers: ['user1', 'user4'], executors: ['admin1'] },
        'ads-report': { approvers: ['user2', 'user5'], executors: ['admin3'] },
        'sbv-report': { approvers: ['user3', 'user6'], executors: ['admin2'] },
        'campaign-config': { approvers: ['user1', 'user7'], executors: ['admin1'] }
      },
      'adhoc-data': {
        default: { approvers: ['user8', 'user9'], executors: ['admin4'] }
      },
      'access-database': {
        default: { approvers: ['user10', 'user11'], executors: ['admin5'] }
      },
      'access-report': {
        default: { approvers: ['user12', 'user13'], executors: ['admin6'] }
      }
    },

    // Filter Settings
    filters: {
      dataquest: {
        status: ['active', 'inactive', 'pending'],
        roles: ['admin', 'user', 'viewer'],
        departments: ['IT', 'Sales', 'Marketing']
      },
      wlp: {
        status: ['active', 'inactive'],
        roles: ['admin', 'user'],
        departments: ['Finance', 'Operations']
      },
      ads: {
        status: ['active', 'inactive', 'suspended'],
        roles: ['admin', 'user', 'editor'],
        departments: ['Content', 'Design']
      },
      sbv: {
        status: ['active', 'inactive'],
        roles: ['admin', 'user'],
        departments: ['Banking', 'Customer Service']
      },
      ecommerce: {
        status: ['active', 'inactive', 'trial'],
        roles: ['admin', 'user', 'manager'],
        departments: ['Sales', 'Support', 'Warehouse']
      }
    }
  });

  const [newConnection, setNewConnection] = useState({ name: '', type: 'platform', url: '' });
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [collapsedSections, setCollapsedSections] = useState({
    ui: false,
    notifications: false,
    connections: false,
    approvers: false,
    filters: false
  });

  // New state for adding approvers and executors
  const [newApprover, setNewApprover] = useState('');
  const [newExecutor, setNewExecutor] = useState('');
  const [selectedRequestType, setSelectedRequestType] = useState('');
  const [selectedPlatform, setSelectedPlatform] = useState('');

  // Toggle section collapse state
  const toggleSection = (section) => {
    setCollapsedSections({
      ...collapsedSections,
      [section]: !collapsedSections[section]
    });
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings({
      ...settings,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    setSettings({
      ...settings,
      [name]: parseInt(value, 10) || 0
    });
  };

  const handleNewConnectionChange = (e) => {
    const { name, value } = e.target;
    setNewConnection({
      ...newConnection,
      [name]: value
    });
  };

  const addConnection = () => {
    if (!newConnection.name || !newConnection.url) {
      setErrorMessage(language === 'en' ? 'Please fill in all connection details' : 'Vui lòng điền đầy đủ thông tin kết nối');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    const newId = Math.max(...settings.connections.map(c => c.id), 0) + 1;
    const updatedConnections = [...settings.connections, { ...newConnection, id: newId }];

    setSettings({
      ...settings,
      connections: updatedConnections,
      filters: {
        ...settings.filters,
        [newConnection.name.toLowerCase()]: {
          status: ['active', 'inactive'],
          roles: ['admin', 'user'],
          departments: ['Default']
        }
      }
    });

    setNewConnection({ name: '', type: 'platform', url: '' });
    setSuccessMessage(language === 'en' ? 'Connection added successfully' : 'Đã thêm kết nối thành công');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  const removeConnection = (id) => {
    const connection = settings.connections.find(c => c.id === id);
    const updatedConnections = settings.connections.filter(c => c.id !== id);

    setSettings({
      ...settings,
      connections: updatedConnections,
      filters: Object.fromEntries(
        Object.entries(settings.filters).filter(([key]) => key !== connection.name.toLowerCase())
      )
    });

    setSuccessMessage(language === 'en' ? 'Connection removed successfully' : 'Đã xóa kết nối thành công');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  const addFilterOption = (platform, filterType, value) => {
    if (!value) return;

    setSettings({
      ...settings,
      filters: {
        ...settings.filters,
        [platform]: {
          ...settings.filters[platform],
          [filterType]: [...settings.filters[platform][filterType], value]
        }
      }
    });
  };

  const removeFilterOption = (platform, filterType, value) => {
    setSettings({
      ...settings,
      filters: {
        ...settings.filters,
        [platform]: {
          ...settings.filters[platform],
          [filterType]: settings.filters[platform][filterType].filter(v => v !== value)
        }
      }
    });
  };

  // Add approver to a request type and platform
  const addApprover = (requestType, platform, user) => {
    if (!user) return;

    // Create deep copy of current approvers structure
    const updatedApprovers = JSON.parse(JSON.stringify(settings.approvers));

    // Check if request type exists
    if (!updatedApprovers[requestType]) {
      updatedApprovers[requestType] = {};
    }

    // Check if platform exists for this request type
    if (!updatedApprovers[requestType][platform]) {
      updatedApprovers[requestType][platform] = { approvers: [], executors: [] };
    }

    // Add user to approvers if not already present
    if (!updatedApprovers[requestType][platform].approvers.includes(user)) {
      updatedApprovers[requestType][platform].approvers.push(user);

      setSettings({
        ...settings,
        approvers: updatedApprovers
      });

      setSuccessMessage(language === 'en' ? 'Approver added successfully' : 'Đã thêm người duyệt thành công');
      setTimeout(() => setSuccessMessage(''), 3000);
    }
  };

  // Remove approver from a request type and platform
  const removeApprover = (requestType, platform, user) => {
    // Create deep copy of current approvers structure
    const updatedApprovers = JSON.parse(JSON.stringify(settings.approvers));

    // Check if the path exists
    if (updatedApprovers[requestType] &&
        updatedApprovers[requestType][platform] &&
        updatedApprovers[requestType][platform].approvers) {

      // Filter out the user
      updatedApprovers[requestType][platform].approvers =
        updatedApprovers[requestType][platform].approvers.filter(u => u !== user);

      setSettings({
        ...settings,
        approvers: updatedApprovers
      });

      setSuccessMessage(language === 'en' ? 'Approver removed successfully' : 'Đã xóa người duyệt thành công');
      setTimeout(() => setSuccessMessage(''), 3000);
    }
  };

  // Add executor to a request type and platform
  const addExecutor = (requestType, platform, user) => {
    if (!user) return;

    // Create deep copy of current approvers structure
    const updatedApprovers = JSON.parse(JSON.stringify(settings.approvers));

    // Check if request type exists
    if (!updatedApprovers[requestType]) {
      updatedApprovers[requestType] = {};
    }

    // Check if platform exists for this request type
    if (!updatedApprovers[requestType][platform]) {
      updatedApprovers[requestType][platform] = { approvers: [], executors: [] };
    }

    // Add user to executors if not already present
    if (!updatedApprovers[requestType][platform].executors.includes(user)) {
      updatedApprovers[requestType][platform].executors.push(user);

      setSettings({
        ...settings,
        approvers: updatedApprovers
      });

      setSuccessMessage(language === 'en' ? 'Executor added successfully' : 'Đã thêm người thực thi thành công');
      setTimeout(() => setSuccessMessage(''), 3000);
    }
  };

  // Remove executor from a request type and platform
  const removeExecutor = (requestType, platform, user) => {
    // Create deep copy of current approvers structure
    const updatedApprovers = JSON.parse(JSON.stringify(settings.approvers));

    // Check if the path exists
    if (updatedApprovers[requestType] &&
        updatedApprovers[requestType][platform] &&
        updatedApprovers[requestType][platform].executors) {

      // Filter out the user
      updatedApprovers[requestType][platform].executors =
        updatedApprovers[requestType][platform].executors.filter(u => u !== user);

      setSettings({
        ...settings,
        approvers: updatedApprovers
      });

      setSuccessMessage(language === 'en' ? 'Executor removed successfully' : 'Đã xóa người thực thi thành công');
      setTimeout(() => setSuccessMessage(''), 3000);
    }
  };

  const saveSettings = () => {
    try {
      console.log('Saving settings:', settings);
      localStorage.setItem('appSettings', JSON.stringify(settings));

      setSuccessMessage(language === 'en' ? 'Settings saved successfully!' : 'Đã lưu cài đặt thành công!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      setErrorMessage(language === 'en' ? 'Error saving settings. Please try again.' : 'Lỗi khi lưu cài đặt. Vui lòng thử lại.');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  const resetSettings = () => {
    setSettings({
      itemsPerPage: 10,
      emailNotifications: true,
      userCreationNotify: true,
      userModificationNotify: true,
      connections: [
        { id: 1, name: 'DataQuest', type: 'platform', url: 'https://dataquest.example.com' },
        { id: 2, name: 'WLP', type: 'platform', url: 'https://wlp.example.com' },
        { id: 3, name: 'ADS', type: 'platform', url: 'https://ads.example.com' },
        { id: 4, name: 'SBV', type: 'platform', url: 'https://sbv.example.com' },
        { id: 5, name: 'Ecommerce', type: 'platform', url: 'https://ecommerce.example.com' }
      ],
      approvers: {
        'access-platform': {
          whitelist: { approvers: ['user1', 'user2'], executors: ['admin1'] },
          funnel: { approvers: ['user3'], executors: ['admin2'] },
          zdslab: { approvers: ['user1', 'user4'], executors: ['admin1'] },
          'ads-report': { approvers: ['user2', 'user5'], executors: ['admin3'] },
          'sbv-report': { approvers: ['user3', 'user6'], executors: ['admin2'] },
          'campaign-config': { approvers: ['user1', 'user7'], executors: ['admin1'] }
        },
        'adhoc-data': {
          default: { approvers: ['user8', 'user9'], executors: ['admin4'] }
        },
        'access-database': {
          default: { approvers: ['user10', 'user11'], executors: ['admin5'] }
        },
        'access-report': {
          default: { approvers: ['user12', 'user13'], executors: ['admin6'] }
        }
      },
      filters: {
        dataquest: {
          status: ['active', 'inactive', 'pending'],
          roles: ['admin', 'user', 'viewer'],
          departments: ['IT', 'Sales', 'Marketing']
        },
        wlp: {
          status: ['active', 'inactive'],
          roles: ['admin', 'user'],
          departments: ['Finance', 'Operations']
        },
        ads: {
          status: ['active', 'inactive', 'suspended'],
          roles: ['admin', 'user', 'editor'],
          departments: ['Content', 'Design']
        },
        sbv: {
          status: ['active', 'inactive'],
          roles: ['admin', 'user'],
          departments: ['Banking', 'Customer Service']
        },
        ecommerce: {
          status: ['active', 'inactive', 'trial'],
          roles: ['admin', 'user', 'manager'],
          departments: ['Sales', 'Support', 'Warehouse']
        }
      }
    });

    // Reset state for new approvers and executors
    setNewApprover('');
    setNewExecutor('');
    setSelectedRequestType('');
    setSelectedPlatform('');

    setSuccessMessage(language === 'en' ? 'Settings reset to default!' : 'Đã khôi phục cài đặt về mặc định!');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  return (
    <div className="settings-container">
      <div className="settings-header">
        <h2>{t.title}</h2>
        <div className="settings-actions">
          <button className="btn-reset" onClick={resetSettings}>
            <FiRotateCw /> {t.actions.reset}
          </button>
          <button className="btn-save" onClick={saveSettings}>
            <FiSave /> {t.actions.save}
          </button>
        </div>
      </div>

      {successMessage && (
        <div className="success-message">
          <FiCheckCircle />
          <span>{successMessage}</span>
        </div>
      )}

      {errorMessage && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{errorMessage}</span>
        </div>
      )}

      <div className="settings-content">
        {/* UI Settings */}
        <div className="settings-section">
          <div className="section-header" onClick={() => toggleSection('ui')}>
            <h3>{t.ui.title}</h3>
            <button className="toggle-section-btn">
              {collapsedSections.ui ? <FiChevronDown /> : <FiChevronUp />}
            </button>
          </div>
          {!collapsedSections.ui && (
            <div className="settings-grid">
              <div className="form-group">
                <label>{t.ui.theme}</label>
                <div className="setting-theme-toggle">
                  <button
                    className={`setting-theme-btn ${theme === 'light' ? 'active' : ''}`}
                    onClick={() => toggleTheme()}
                  >
                    <FiSun /> {t.ui.light}
                  </button>
                  <button
                    className={`setting-theme-btn ${theme === 'dark' ? 'active' : ''}`}
                    onClick={() => toggleTheme()}
                  >
                    <FiMoon /> {t.ui.dark}
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label>{t.ui.language}</label>
                <select
                  value={language}
                  onChange={(e) => changeLanguage(e.target.value)}
                >
                  <option value="en">English</option>
                  <option value="vi">Tiếng Việt</option>
                </select>
              </div>

              <div className="form-group">
                <label>{t.ui.itemsPerPage}</label>
                <select
                  name="itemsPerPage"
                  value={settings.itemsPerPage}
                  onChange={handleNumberChange}
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
          )}
        </div>

        {/* Notification Settings */}
        <div className="settings-section">
          <div className="section-header" onClick={() => toggleSection('notifications')}>
            <h3>{t.notifications.title}</h3>
            <button className="toggle-section-btn">
              {collapsedSections.notifications ? <FiChevronDown /> : <FiChevronUp />}
            </button>
          </div>
          {!collapsedSections.notifications && (
            <div className="settings-grid">
              <div className="form-group toggle-group">
                <label>{t.notifications.email}</label>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    name="emailNotifications"
                    checked={settings.emailNotifications}
                    onChange={handleChange}
                  />
                  <span className="toggle-slider"></span>
                </label>
              </div>

              <div className="form-group toggle-group">
                <label>{t.notifications.userCreation}</label>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    name="userCreationNotify"
                    checked={settings.userCreationNotify}
                    onChange={handleChange}
                    disabled={!settings.emailNotifications}
                  />
                  <span className="toggle-slider"></span>
                </label>
              </div>

              <div className="form-group toggle-group">
                <label>{t.notifications.userModification}</label>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    name="userModificationNotify"
                    checked={settings.userModificationNotify}
                    onChange={handleChange}
                    disabled={!settings.emailNotifications}
                  />
                  <span className="toggle-slider"></span>
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Connection Settings */}
        <div className="settings-section">
          <div className="section-header" onClick={() => toggleSection('connections')}>
            <h3>{t.connections.title}</h3>
            <button className="toggle-section-btn">
              {collapsedSections.connections ? <FiChevronDown /> : <FiChevronUp />}
            </button>
          </div>
          {!collapsedSections.connections && (
            <>
              <div className="connections-list">
                {settings.connections.map(connection => (
                  <div key={connection.id} className="connection-item">
                    <div className="connection-info">
                      <span className="connection-name">{connection.name}</span>
                      <span className="connection-type">{connection.type}</span>
                      <span className="connection-url">{connection.url}</span>
                    </div>
                    <button
                      className="btn-delete"
                      onClick={() => removeConnection(connection.id)}
                    >
                      <FiTrash2 />
                    </button>
                  </div>
                ))}
              </div>

              <div className="add-connection">
                <h4>{t.connections.addNew}</h4>
                <div className="connection-form">
                  <input
                    type="text"
                    name="name"
                    placeholder={t.connections.name}
                    value={newConnection.name}
                    onChange={handleNewConnectionChange}
                  />
                  <select
                    name="type"
                    value={newConnection.type}
                    onChange={handleNewConnectionChange}
                  >
                    <option value="platform">Platform</option>
                    <option value="datasource">Datasource</option>
                  </select>
                  <input
                    type="text"
                    name="url"
                    placeholder={t.connections.url}
                    value={newConnection.url}
                    onChange={handleNewConnectionChange}
                  />
                  <button className="btn-add" onClick={addConnection}>
                    <FiPlus /> {t.connections.add}
                  </button>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Approver & Executor Settings */}
        <div className="settings-section">
          <div className="section-header" onClick={() => toggleSection('approvers')}>
            <h3>{t.approvers.title}</h3>
            <button className="toggle-section-btn">
              {collapsedSections.approvers ? <FiChevronDown /> : <FiChevronUp />}
            </button>
          </div>
          {!collapsedSections.approvers && (
            <div className="approvers-container">
              {Object.entries(settings.approvers).map(([requestType, platforms]) => (
                <div key={requestType} className="request-type-section">
                  <h4>{t.approvers[requestType.replace(/-/g, '')] || requestType.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                  {Object.entries(platforms).map(([platform, config]) => (
                    <div key={platform} className="platform-config">
                      <h5>{platform.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h5>
                      <div className="config-group">
                        <div className="config-item">
                          <label>{t.approvers.approvers}</label>
                          <div className="user-list">
                            {config.approvers.map(user => (
                              <div key={user} className="user-item">
                                <span>{user}</span>
                                <button
                                  className="btn-remove-option"
                                  onClick={() => removeApprover(requestType, platform, user)}
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                          </div>
                          <div className="add-option">
                            <input
                              type="text"
                              placeholder={t.approvers.addApprover}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && e.target.value.trim()) {
                                  addApprover(requestType, platform, e.target.value.trim());
                                  e.target.value = '';
                                }
                              }}
                            />
                          </div>
                        </div>
                        <div className="config-item">
                          <label>{t.approvers.executors}</label>
                          <div className="user-list">
                            {config.executors.map(user => (
                              <div key={user} className="user-item">
                                <span>{user}</span>
                                <button
                                  className="btn-remove-option"
                                  onClick={() => removeExecutor(requestType, platform, user)}
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                          </div>
                          <div className="add-option">
                            <input
                              type="text"
                              placeholder={t.approvers.addExecutor}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && e.target.value.trim()) {
                                  addExecutor(requestType, platform, e.target.value.trim());
                                  e.target.value = '';
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Filter Settings */}
        <div className="settings-section">
          <div className="section-header" onClick={() => toggleSection('filters')}>
            <h3>{t.filters.title}</h3>
            <button className="toggle-section-btn">
              {collapsedSections.filters ? <FiChevronDown /> : <FiChevronUp />}
            </button>
          </div>
          {!collapsedSections.filters && (
            <div className="filters-container">
              {Object.entries(settings.filters).map(([platform, filters]) => (
                <div key={platform} className="platform-filters">
                  <h4>{platform}</h4>
                  {Object.entries(filters).map(([filterType, options]) => (
                    <div key={filterType} className="filter-group">
                      <h5>{t.filters[filterType]}</h5>
                      <div className="filter-options">
                        {options.map(option => (
                          <div key={option} className="filter-option">
                            <span>{option}</span>
                            <button
                              className="btn-remove-option"
                              onClick={() => removeFilterOption(platform, filterType, option)}
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                      <div className="add-option">
                        <input
                          type="text"
                          placeholder={`${t.filters.addNew} ${t.filters[filterType]}`}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              addFilterOption(platform, filterType, e.target.value);
                              e.target.value = '';
                            }
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;