import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { FiArrowLeft, FiChevronDown, FiChevronUp, FiEye, FiEyeOff } from "react-icons/fi";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import "../styles/UserDetail.css";

const UserDetail = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [userData, setUserData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expandedSections, setExpandedSections] = useState({});
  const [showPasswords, setShowPasswords] = useState({});

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const searchParams = new URLSearchParams(location.search);
        const username = searchParams.get("username");
        const currentPlatform = localStorage.getItem("currentPlatform") || "dataquest";
        
        if (!username) {
          setError("Username is required");
          setLoading(false);
          return;
        }

        const response = await apiService.get(ENDPOINTS.USERS.GET_ALL_PLATFORM(username));
        
        if (response && Array.isArray(response)) {
          setUserData(response);
          
          // Initialize sections state - only current platform is expanded
          const initialExpandedState = {};
          response.forEach((platform, index) => {
            initialExpandedState[index] = platform.platform === currentPlatform;
          });
          setExpandedSections(initialExpandedState);
        } else {
          setError("No user data found");
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        setError("Unable to load user data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [location.search]);

  const toggleSection = (index) => {
    setExpandedSections(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const formatCellValue = (key, value) => {
    if (value === null || value === undefined) return "";

    // Format status
    if (key === "status") {
      return (
        <span className={`status-badge ${value}`}>
          {value === "active" ? "Active" : value === "inactive" ? "Inactive" : value}
        </span>
      );
    }

    // Format dates
    if (key.includes("date") || key.includes("created") || key.includes("updated")) {
      return formatDate(value);
    }

    // Format boolean values
    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }

    // Format arrays
    if (Array.isArray(value)) {
      return (
        <div className="array-values-container">
          {value.map((item, index) => (
            <span key={index} className="array-value-item">
              {item}
            </span>
          ))}
        </div>
      );
    }

    // Handle password fields
    if (key === "hashed_password") {
      return (
        <div className="password-display">
          <span>{showPasswords[key] ? value : "********"}</span>
          <button 
            className="password-toggle-btn"
            onClick={() => setShowPasswords(prev => ({
              ...prev,
              [key]: !prev[key]
            }))}
            title={showPasswords[key] ? "Hide password" : "Show password"}
          >
            {showPasswords[key] ? "Hide" : "Show"}
          </button>
        </div>
      );
    }

    return value;
  };

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error-message">{error}</div>
        <button className="btn-back" onClick={() => navigate(-1)}>
          <FiArrowLeft /> Back
        </button>
      </div>
    );
  }

  return (
    <div className="user-detail-container">
      <div className="user-detail-header">
        <button className="btn-back" onClick={() => navigate(-1)}>
          <FiArrowLeft /> Back
        </button>
        <h2>User Details</h2>
      </div>

      <div className="user-detail-content">
        {userData.map((platformData, index) => (
          <div key={index} className="platform-section">
            <div 
              className="platform-title-bar" 
              onClick={() => toggleSection(index)}
            >
              <h3 className="platform-title">
                {platformData.platform}
              </h3>
              <span className="toggle-icon">
                {expandedSections[index] ? <FiChevronUp /> : <FiChevronDown />}
              </span>
            </div>
            
            {expandedSections[index] && (
              <div className="user-info-table">
                <table>
                  <tbody>
                    {Object.entries(platformData.users || {}).map(([key, value]) => (
                      <tr key={key}>
                        <td className="info-label">{key.replace(/_/g, " ")}:</td>
                        <td className="info-value">{formatCellValue(key, value)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default UserDetail; 