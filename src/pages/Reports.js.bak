import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, FiFilter, FiCalendar, FiUser, FiBarChart2,
  FiTruck, FiDollarSign, FiPieChart, FiUsers, FiRefreshCw, FiList
} from 'react-icons/fi';
import {
  BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell, ComposedChart
} from 'recharts';
import apiService from '../services/api.service';
import { ENDPOINTS } from '../config/api.config';
import '../styles/Reports.css';

const Reports = () => {
  // State cho main tabs (overview/customer/supplier)
  const [mainTab, setMainTab] = useState('overview'); // 'overview', 'customer', 'supplier'

  // State cho form lọc
  const [dateRange, setDateRange] = useState(() => {
    // Tính ngày đầu tiên của tháng hiện tại
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

    // Định dạng ngày tháng theo YYYY-MM-DD
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    return {
      startDate: formatDate(firstDay),
      endDate: formatDate(today)
    };
  });

  // State cho các bộ lọc
  const [selectedCustomerId, setSelectedCustomerId] = useState('');
  const [selectedSupplierId, setSelectedSupplierId] = useState('');
  const [selectedSaleStaffId, setSelectedSaleStaffId] = useState('');
  const [selectedPricingStaffId, setSelectedPricingStaffId] = useState('');
  const [selectedDispatchStaffId, setSelectedDispatchStaffId] = useState('');
  const [selectedCompanyId, setSelectedCompanyId] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // State for report view type
  const [reportViewType, setReportViewType] = useState('summary'); // 'summary', 'detail', 'trend'

  // State for revenue chart time period
  const [revenuePeriod, setRevenuePeriod] = useState('month'); // 'day', 'month', 'quarter'

  // State for performance chart
  const [performancePeriod, setPerformancePeriod] = useState('month'); // 'month', 'quarter'

  // State for filter visibility
  const [showFilters, setShowFilters] = useState(true);

  // State for status counts (needed for filtering)
  // eslint-disable-next-line no-unused-vars
  const [statusCounts, setStatusCounts] = useState({});

  // State cho danh sách trạng thái
  const [statuses] = useState([
    { id: 'initial', name: 'Khởi tạo' },
    { id: 'approved', name: 'Đã duyệt' },
    { id: 'delivered', name: 'Đã giao hàng' },
    { id: 'customerpaid', name: 'Đã thu tiền khách hàng' },
    { id: 'supplierpaid', name: 'Đã trả tiền nhà cung cấp' },
    { id: 'completed', name: 'Hoàn thành' },
    { id: 'processing', name: 'Đang xử lý' },
    { id: 'cancelled', name: 'Đã hủy' }
  ]);
  const [companies, setCompanies] = useState([]);
  const [isCompanyLoading, setIsCompanyLoading] = useState(false);

  // State cho dữ liệu danh sách
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [staffs, setStaffs] = useState([]);

  // State cho trạng thái loading
  const [isCustomerLoading, setIsCustomerLoading] = useState(false);
  const [isSupplierLoading, setIsSupplierLoading] = useState(false);
  const [isStaffLoading, setIsStaffLoading] = useState(false);

  // State cho dữ liệu báo cáo
  const [customerReportData, setCustomerReportData] = useState([]);
  const [supplierReportData, setSupplierReportData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasAppliedFilters, setHasAppliedFilters] = useState(false);

  // State cho phân bố trạng thái vé
  const [statusDistribution, setStatusDistribution] = useState({});

  // State cho dữ liệu gốc từ API
  const [rawReportData, setRawReportData] = useState([]);

  // Fetch danh sách khách hàng, nhà cung cấp, nhân viên và công ty
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch khách hàng
        setIsCustomerLoading(true);
        const customerResponse = await apiService.get(ENDPOINTS.CUSTOMERS.GET_BASIC);

        if (customerResponse && customerResponse.customers) {
          setCustomers(customerResponse.customers);
        } else if (customerResponse && Array.isArray(customerResponse)) {
          setCustomers(customerResponse);
        } else if (customerResponse && customerResponse.data) {
          setCustomers(customerResponse.data);
        } else {
          console.error('Unexpected customer response structure:', customerResponse);
        }
        setIsCustomerLoading(false);

        // Fetch nhà cung cấp
        setIsSupplierLoading(true);
        const supplierResponse = await apiService.get(ENDPOINTS.SUPPLIERS.GET_BASIC);

        if (supplierResponse && supplierResponse.suppliers) {
          setSuppliers(supplierResponse.suppliers);
        } else if (supplierResponse && Array.isArray(supplierResponse)) {
          setSuppliers(supplierResponse);
        } else if (supplierResponse && supplierResponse.data) {
          setSuppliers(supplierResponse.data);
        } else {
          console.error('Unexpected supplier response structure:', supplierResponse);
        }
        setIsSupplierLoading(false);

        // Fetch nhân viên
        setIsStaffLoading(true);
        const staffResponse = await apiService.get(ENDPOINTS.USERS.GET_BASIC);

        if (staffResponse && staffResponse.users) {
          setStaffs(staffResponse.users);
        } else if (staffResponse && Array.isArray(staffResponse)) {
          setStaffs(staffResponse);
        } else if (staffResponse && staffResponse.data) {
          setStaffs(staffResponse.data);
        } else {
          console.error('Unexpected staff response structure:', staffResponse);
        }
        setIsStaffLoading(false);

        // Fetch công ty
        setIsCompanyLoading(true);
        const companyResponse = await apiService.get(ENDPOINTS.COMPANY.GET_BASIC);

        if (companyResponse && companyResponse.companies) {
          setCompanies(companyResponse.companies);
        } else if (companyResponse && Array.isArray(companyResponse)) {
          setCompanies(companyResponse);
        } else if (companyResponse && companyResponse.data) {
          setCompanies(companyResponse.data);
        } else {
          console.error('Unexpected company response structure:', companyResponse);
        }
        setIsCompanyLoading(false);

      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Không thể tải dữ liệu. Vui lòng thử lại sau.');
        setIsCustomerLoading(false);
        setIsSupplierLoading(false);
        setIsStaffLoading(false);
        setIsCompanyLoading(false);
      }
    };

    fetchData();
  }, []);

  // Hàm xử lý khi thay đổi form lọc
  const handleDateChange = (e) => {
    const { name, value } = e.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCustomerChange = (e) => {
    setSelectedCustomerId(e.target.value);
  };

  const handleSupplierChange = (e) => {
    setSelectedSupplierId(e.target.value);
  };

  const handleSaleStaffChange = (e) => {
    setSelectedSaleStaffId(e.target.value);
  };

  const handlePricingStaffChange = (e) => {
    setSelectedPricingStaffId(e.target.value);
  };

  const handleDispatchStaffChange = (e) => {
    setSelectedDispatchStaffId(e.target.value);
  };

  const handleCompanyChange = (e) => {
    setSelectedCompanyId(e.target.value);
  };

  // Status is now handled by the status count cards

  // Hàm lấy dữ liệu báo cáo từ API
  const fetchReportData = async () => {
    setIsLoading(true);
    setError(null);

    // Initialize status counts
    const initialStatusCounts = {};
    const initialStatusDistribution = {};
    statuses.forEach(status => {
      initialStatusCounts[status.id] = 0;
      initialStatusDistribution[status.id] = 0;
    });
    setStatusCounts(initialStatusCounts);
    setStatusDistribution(initialStatusDistribution);

    try {
      // Lấy company_id từ localStorage
      let company_id = null;
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const user = JSON.parse(userStr);
          company_id = user.company_id;
        }
      } catch (err) {
        console.error('Error getting company_id from localStorage:', err);
      }

      // Chuẩn bị tham số cho API
      const params = {
        start_date: dateRange.startDate,
        end_date: dateRange.endDate,
        company_id: selectedCompanyId || company_id
      };

      // Thêm các tham số lọc nếu được chọn
      if (selectedCustomerId) {
        params.customer_id = selectedCustomerId;
      }

      if (selectedSupplierId) {
        params.supplier_id = selectedSupplierId;
      }

      if (selectedSaleStaffId) {
        params.sale_staff_id = selectedSaleStaffId;
      }

      if (selectedPricingStaffId) {
        params.pricing_staff_id = selectedPricingStaffId;
      }

      if (selectedDispatchStaffId) {
        params.dispatch_staff_id = selectedDispatchStaffId;
      }

      if (selectedStatus) {
        params.status = selectedStatus;
      }

      // Gọi API
      const response = await apiService.get(ENDPOINTS.REPORT.GET(params));

      // Kiểm tra nếu response là mảng hoặc có thuộc tính data là mảng
      const responseData = Array.isArray(response) ? response : (response.data || []);

      // Lưu trữ dữ liệu gốc từ API để sử dụng cho biểu đồ
      setRawReportData(responseData);

      if (responseData.length > 0) {
        // Chuyển đổi dữ liệu từ API sang định dạng hiển thị
        const formattedData = responseData.map(item => {
          // Tính toán số lượng vé (nếu có)
          const totalTickets = 1; // Mỗi dòng là một vé

          // Tính toán trạng thái vé
          const isCompleted = item.current_status === 'completed' || item.current_status === 'supplierpaid';
          const completedTickets = isCompleted ? 1 : 0;
          const pendingTickets = isCompleted ? 0 : 1;

          // Tính toán status counts
          const status = item.current_status || 'initial';
          initialStatusCounts[status] = (initialStatusCounts[status] || 0) + 1;
          initialStatusDistribution[status] = (initialStatusDistribution[status] || 0) + 1;

          // Tính toán doanh thu, chi phí và lợi nhuận
          const totalRevenue = item.total_receive_after_tax || 0;
          const totalCost = item.total_pay_after_tax || 0;
          const profit = totalRevenue - totalCost;

          return {
            customerId: item.customer_id,
            customerName: item.customer_name,
            supplierId: item.supplier_id,
            supplierName: item.supplier_name,
            totalTickets,
            completedTickets,
            pendingTickets,
            totalRevenue,
            totalCost,
            profit,
            request_date: item.request_date,
            created_at: item.created_at,
            current_status: status
          };
        });

        // Nhóm dữ liệu theo khách hàng
        const customerMap = new Map();
        const supplierMap = new Map();

        formattedData.forEach(item => {
          // Xử lý dữ liệu khách hàng
          if (item.customerId) {
            if (customerMap.has(item.customerId)) {
              const existingData = customerMap.get(item.customerId);
              customerMap.set(item.customerId, {
                ...existingData,
                totalTickets: existingData.totalTickets + item.totalTickets,
                completedTickets: existingData.completedTickets + item.completedTickets,
                pendingTickets: existingData.pendingTickets + item.pendingTickets,
                totalRevenue: existingData.totalRevenue + item.totalRevenue,
                totalCost: existingData.totalCost + item.totalCost,
                profit: existingData.profit + item.profit
              });
            } else {
              customerMap.set(item.customerId, {
                customerId: item.customerId,
                customerName: item.customerName,
                totalTickets: item.totalTickets,
                completedTickets: item.completedTickets,
                pendingTickets: item.pendingTickets,
                totalRevenue: item.totalRevenue,
                totalCost: item.totalCost,
                profit: item.profit
              });
            }
          }

          // Xử lý dữ liệu nhà cung cấp
          if (item.supplierId) {
            if (supplierMap.has(item.supplierId)) {
              const existingData = supplierMap.get(item.supplierId);
              supplierMap.set(item.supplierId, {
                ...existingData,
                totalTickets: existingData.totalTickets + item.totalTickets,
                completedTickets: existingData.completedTickets + item.completedTickets,
                pendingTickets: existingData.pendingTickets + item.pendingTickets,
                totalRevenue: existingData.totalRevenue + item.totalRevenue,
                totalCost: existingData.totalCost + item.totalCost,
                profit: existingData.profit + item.profit
              });
            } else {
              supplierMap.set(item.supplierId, {
                supplierId: item.supplierId,
                supplierName: item.supplierName,
                totalTickets: item.totalTickets,
                completedTickets: item.completedTickets,
                pendingTickets: item.pendingTickets,
                totalRevenue: item.totalRevenue,
                totalCost: item.totalCost,
                profit: item.profit
              });
            }
          }
        });

        // Chuyển Map thành mảng
        const customerData = Array.from(customerMap.values());
        const supplierData = Array.from(supplierMap.values());

        setCustomerReportData(customerData);
        setSupplierReportData(supplierData);
        setStatusCounts(initialStatusCounts);
        setStatusDistribution(initialStatusDistribution);
      } else {
        // Nếu API trả về dữ liệu không đúng định dạng, sử dụng dữ liệu mẫu
        console.warn('API response format is unexpected, using mock data instead');
        generateMockReportData();
      }
    } catch (err) {
      console.error('Error fetching report data:', err);
      setError('Không thể tải dữ liệu báo cáo. Vui lòng thử lại sau.');
      // Sử dụng dữ liệu mẫu nếu API gặp lỗi
      generateMockReportData();
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm tạo dữ liệu mẫu (sử dụng khi API gặp lỗi hoặc chưa sẵn sàng)
  const generateMockReportData = () => {
    try {
      // Reset status counts
      const mockStatusCounts = {};
      statuses.forEach(status => {
        mockStatusCounts[status.id] = 0;
      });
      // Dữ liệu mẫu cho khách hàng
      const mockCustomerData = [];
      // Dữ liệu mẫu cho nhà cung cấp
      const mockSupplierData = [];

      // Tạo dữ liệu mẫu cho khách hàng
      if (selectedCustomerId) {
        // Nếu chọn khách hàng cụ thể
        const customer = customers.find(c => c.id.toString() === selectedCustomerId);
        if (customer) {
          mockCustomerData.push({
            customerId: customer.id,
            customerName: customer.customer_name,
            totalTickets: Math.floor(Math.random() * 50) + 1,
            completedTickets: Math.floor(Math.random() * 40),
            pendingTickets: Math.floor(Math.random() * 10),
            totalRevenue: Math.floor(Math.random() * 1000000000) + 100000000,
            totalCost: Math.floor(Math.random() * 800000000) + 50000000,
            profit: Math.floor(Math.random() * 200000000) + 50000000
          });
        }
      } else {
        // Nếu chọn tất cả khách hàng, lấy 10 khách hàng đầu tiên để tạo dữ liệu mẫu
        const sampleCustomers = customers.slice(0, 10);

        sampleCustomers.forEach(customer => {
          const totalTickets = Math.floor(Math.random() * 50) + 1;
          const completedTickets = Math.floor(Math.random() * totalTickets);
          const pendingTickets = totalTickets - completedTickets;
          const totalRevenue = Math.floor(Math.random() * 1000000000) + 100000000;
          const totalCost = Math.floor(Math.random() * 800000000) + 50000000;

          mockCustomerData.push({
            customerId: customer.id,
            customerName: customer.customer_name,
            totalTickets,
            completedTickets,
            pendingTickets,
            totalRevenue,
            totalCost,
            profit: totalRevenue - totalCost
          });
        });
      }

      // Tạo dữ liệu mẫu cho nhà cung cấp
      if (selectedSupplierId) {
        // Nếu chọn nhà cung cấp cụ thể
        const supplier = suppliers.find(s => s.id.toString() === selectedSupplierId);
        if (supplier) {
          mockSupplierData.push({
            supplierId: supplier.id,
            supplierName: supplier.supplier_name,
            totalTickets: Math.floor(Math.random() * 50) + 1,
            completedTickets: Math.floor(Math.random() * 40),
            pendingTickets: Math.floor(Math.random() * 10),
            totalRevenue: Math.floor(Math.random() * 500000000) + 50000000,
            totalCost: Math.floor(Math.random() * 800000000) + 100000000,
            profit: Math.floor(Math.random() * 100000000) - 50000000 // Có thể âm hoặc dương
          });
        }
      } else {
        // Nếu chọn tất cả nhà cung cấp, lấy 10 nhà cung cấp đầu tiên để tạo dữ liệu mẫu
        const sampleSuppliers = suppliers.slice(0, 10);

        sampleSuppliers.forEach(supplier => {
          const totalTickets = Math.floor(Math.random() * 50) + 1;
          const completedTickets = Math.floor(Math.random() * totalTickets);
          const pendingTickets = totalTickets - completedTickets;
          const totalCost = Math.floor(Math.random() * 800000000) + 100000000;
          const totalRevenue = Math.floor(Math.random() * 500000000) + 50000000;

          mockSupplierData.push({
            supplierId: supplier.id,
            supplierName: supplier.supplier_name,
            totalTickets,
            completedTickets,
            pendingTickets,
            totalRevenue,
            totalCost,
            profit: totalRevenue - totalCost // Thường là âm vì chi phí > doanh thu
          });
        });
      }

      // Update status counts for mock data
      const mockStatusDistribution = { ...mockStatusCounts };
      mockCustomerData.forEach(customer => {
        mockStatusCounts['initial'] = (mockStatusCounts['initial'] || 0) + Math.floor(customer.pendingTickets / 2);
        mockStatusCounts['confirmed'] = (mockStatusCounts['confirmed'] || 0) + Math.floor(customer.pendingTickets / 2);
        mockStatusCounts['completed'] = (mockStatusCounts['completed'] || 0) + customer.completedTickets;

        // Also update status distribution
        mockStatusDistribution['initial'] = (mockStatusDistribution['initial'] || 0) + Math.floor(customer.pendingTickets / 2);
        mockStatusDistribution['confirmed'] = (mockStatusDistribution['confirmed'] || 0) + Math.floor(customer.pendingTickets / 2);
        mockStatusDistribution['completed'] = (mockStatusDistribution['completed'] || 0) + customer.completedTickets;
      });

      setCustomerReportData(mockCustomerData);
      setSupplierReportData(mockSupplierData);
      setStatusCounts(mockStatusCounts);
      setStatusDistribution(mockStatusDistribution);
    } catch (err) {
      console.error('Error generating mock report data:', err);
      setError('Không thể tạo dữ liệu báo cáo. Vui lòng thử lại sau.');
    }
  };

  // Hàm xử lý khi submit form
  const handleSubmit = (e) => {
    e.preventDefault();
    // Reset status counts before fetching new data
    setStatusCounts({});
    setStatusDistribution({});
    setHasAppliedFilters(true);
    fetchReportData();
  };

  // Hàm định dạng số tiền
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
  };

  // Hàm xuất báo cáo ra Excel
  const exportToExcel = () => {
    alert('Chức năng xuất Excel sẽ được triển khai sau.');
  };



  // Dữ liệu cho biểu đồ timeline khách hàng
  const getCustomerTimelineChartData = () => {
    if (!customerReportData.length) return [];

    // Giả lập dữ liệu theo thời gian cho mỗi khách hàng
    // Trong thực tế, dữ liệu này sẽ được lấy từ API
    const startDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);
    const dayDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

    // Tạo một mảng các ngày trong khoảng thời gian
    const timelineData = [];

    // Nếu khoảng thời gian quá dài, chia thành các điểm dữ liệu theo tuần hoặc tháng
    const interval = dayDiff > 60 ? 30 : (dayDiff > 14 ? 7 : 1); // Tháng, tuần hoặc ngày

    for (let i = 0; i <= dayDiff; i += interval) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dataPoint = {
        date: currentDate.toISOString().split('T')[0],
      };

      // Thêm dữ liệu cho mỗi khách hàng
      customerReportData.forEach(customer => {
        // Tạo dữ liệu ngẫu nhiên cho biểu đồ timeline
        // Trong thực tế, dữ liệu này sẽ được lấy từ API
        const randomFactor = 0.8 + Math.random() * 0.4; // Biến động 80-120%
        dataPoint[`customer_${customer.customerId}`] = Math.round(customer.totalRevenue * randomFactor / (dayDiff / interval));
      });

      timelineData.push(dataPoint);
    }

    return timelineData;
  };

  // Hàm tạo dữ liệu cho biểu đồ so sánh hiệu suất
  const getPerformanceComparisonData = () => {
    // Tính toán tổng doanh thu hiện tại và kỳ trước
    const currentRevenue = customerReportData.reduce((sum, item) => sum + item.totalRevenue, 0);
    const previousRevenue = currentRevenue * 0.85; // Giả định doanh thu kỳ trước là 85% hiện tại

    // Tính toán tổng chi phí hiện tại và kỳ trước
    const currentCost = supplierReportData.reduce((sum, item) => sum + item.totalCost, 0);
    const previousCost = currentCost * 0.9; // Giả định chi phí kỳ trước là 90% hiện tại

    // Tính toán lợi nhuận
    const currentProfit = currentRevenue - currentCost;
    const previousProfit = previousRevenue - previousCost;

    // Tính toán số lượng vé
    const currentTickets = customerReportData.reduce((sum, item) => sum + item.totalTickets, 0);
    const previousTickets = currentTickets * 0.8; // Giả định số vé kỳ trước là 80% hiện tại

    // Tính toán tỷ lệ hoàn thành
    const currentCompleted = customerReportData.reduce((sum, item) => sum + item.completedTickets, 0);
    const previousCompleted = previousTickets * 0.75; // Giả định số vé hoàn thành kỳ trước là 75% số vé kỳ trước

    const currentCompletionRate = currentTickets > 0 ? (currentCompleted / currentTickets) * 100 : 0;
    const previousCompletionRate = previousTickets > 0 ? (previousCompleted / previousTickets) * 100 : 0;

    // Xác định nhãn kỳ dựa trên performancePeriod
    const periodLabel = performancePeriod === 'month' ? 'Tháng' : 'Quý';

    return [
      {
        name: 'Doanh thu',
        current: currentRevenue,
        previous: previousRevenue,
        isCurrency: true,
        periodLabel
      },
      {
        name: 'Chi phí',
        current: currentCost,
        previous: previousCost,
        isCurrency: true,
        periodLabel
      },
      {
        name: 'Lợi nhuận',
        current: currentProfit,
        previous: previousProfit,
        isCurrency: true,
        periodLabel
      },
      {
        name: 'Số lượng vé',
        current: currentTickets,
        previous: previousTickets,
        isCount: true,
        periodLabel
      },
      {
        name: 'Tỷ lệ hoàn thành',
        current: currentCompletionRate,
        previous: previousCompletionRate,
        isPercentage: true,
        periodLabel
      }
    ];
  };

  // Dữ liệu cho biểu đồ cột chồng theo trạng thái
  const getRevenueByStatusChartData = () => {
    if (!rawReportData.length) return [];

    // Sử dụng dữ liệu thực từ API
    const startDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);
    const dayDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

    // Xác định khoảng thời gian phù hợp dựa trên period được chọn
    let interval = 1; // Mặc định là ngày
    let dateFormat = { day: '2-digit', month: '2-digit' };
    let groupByFn;

    switch (revenuePeriod) {
      case 'day':
        interval = 1; // Ngày
        dateFormat = { day: '2-digit', month: '2-digit' };
        groupByFn = (date) => date.toLocaleDateString('vi-VN', dateFormat);
        break;
      case 'month':
        interval = 30; // Tháng
        dateFormat = { month: '2-digit', year: 'numeric' };
        groupByFn = (date) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          return `${String(month + 1).padStart(2, '0')}/${year}`;
        };
        break;
      case 'quarter':
        interval = 90; // Quý
        dateFormat = { year: 'numeric' };
        groupByFn = (date) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          const quarter = Math.floor(month / 3) + 1;
          return `Q${quarter}/${year}`;
        };
        break;
      default:
        // Mặc định là tháng
        interval = 30;
        dateFormat = { month: '2-digit', year: 'numeric' };
        groupByFn = (date) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          return `${String(month + 1).padStart(2, '0')}/${year}`;
        };
    }

    // Sẽ tạo một mảng các điểm thời gian từ dữ liệu thực

    // Lấy danh sách các trạng thái có trong dữ liệu
    const activeStatuses = Object.keys(statusDistribution).filter(status => statusDistribution[status] > 0);

    // Tạo một map để nhóm dữ liệu theo thời gian và trạng thái
    const dateStatusMap = new Map();

    // Xử lý dữ liệu thực từ API
    rawReportData.forEach(item => {
      // Lấy ngày tạo hoặc ngày yêu cầu
      const itemDate = item.request_date ? new Date(item.request_date) : new Date(item.created_at);

      // Bỏ qua các mục không có ngày hợp lệ hoặc nằm ngoài khoảng thời gian
      if (isNaN(itemDate.getTime()) || itemDate < startDate || itemDate > endDate) return;

      // Sử dụng hàm nhóm theo thời gian đã định nghĩa
      const formattedDate = groupByFn(itemDate);

      // Lấy trạng thái, doanh thu và chi phí
      const status = item.current_status || 'initial';
      const revenue = item.total_receive_after_tax || 0;
      const cost = item.total_pay_after_tax || 0;
      const profit = revenue - cost;

      // Tạo hoặc cập nhật dữ liệu cho ngày và trạng thái
      if (!dateStatusMap.has(formattedDate)) {
        const newDataPoint = {
          date: formattedDate,
          profit: 0,
          totalCost: 0
        };
        activeStatuses.forEach(s => newDataPoint[s] = 0);
        dateStatusMap.set(formattedDate, newDataPoint);
      }

      const dataPoint = dateStatusMap.get(formattedDate);
      dataPoint[status] = (dataPoint[status] || 0) + revenue;
      dataPoint.profit = (dataPoint.profit || 0) + profit;
      dataPoint.totalCost = (dataPoint.totalCost || 0) + cost;
    });

    // Chuyển map thành mảng và sắp xếp theo ngày
    const sortedData = Array.from(dateStatusMap.values()).sort((a, b) => {
      const dateA = a.date.split('/').reverse().join('');
      const dateB = b.date.split('/').reverse().join('');
      return dateA.localeCompare(dateB);
    });

    // Tính tổng cho mỗi điểm dữ liệu
    sortedData.forEach(dataPoint => {
      let total = 0;
      activeStatuses.forEach(status => {
        total += dataPoint[status] || 0;
      });
      dataPoint.total = total;
    });

    return sortedData;
  };

  // Dữ liệu cho biểu đồ timeline nhà cung cấp
  const getSupplierTimelineChartData = () => {
    if (!supplierReportData.length) return [];

    // Giả lập dữ liệu theo thời gian cho mỗi nhà cung cấp
    const startDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);
    const dayDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

    // Tạo một mảng các ngày trong khoảng thời gian
    const timelineData = [];

    // Nếu khoảng thời gian quá dài, chia thành các điểm dữ liệu theo tuần hoặc tháng
    const interval = dayDiff > 60 ? 30 : (dayDiff > 14 ? 7 : 1); // Tháng, tuần hoặc ngày

    for (let i = 0; i <= dayDiff; i += interval) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dataPoint = {
        date: currentDate.toISOString().split('T')[0],
      };

      // Thêm dữ liệu cho mỗi nhà cung cấp
      supplierReportData.forEach(supplier => {
        // Tạo dữ liệu ngẫu nhiên cho biểu đồ timeline
        const randomFactor = 0.8 + Math.random() * 0.4; // Biến động 80-120%
        dataPoint[`supplier_${supplier.supplierId}`] = Math.round(supplier.totalCost * randomFactor / (dayDiff / interval));
      });

      timelineData.push(dataPoint);
    }

    return timelineData;
  };

  // Dữ liệu cho biểu đồ cột khách hàng
  const getCustomerBarChartData = () => {
    if (!customerReportData.length) return [];

    return customerReportData.map(item => ({
      name: item.customerName,
      revenue: item.totalRevenue,
      cost: item.totalCost,
      profit: item.profit
    }));
  };

  // Dữ liệu cho biểu đồ cột nhà cung cấp
  const getSupplierBarChartData = () => {
    if (!supplierReportData.length) return [];

    return supplierReportData.map(item => ({
      name: item.supplierName,
      revenue: item.totalRevenue,
      cost: item.totalCost,
      profit: item.profit
    }));
  };

  // Màu cho biểu đồ tròn và trạng thái
  const getStatusColor = (status) => {
    // Map status to appropriate color to match button colors in Requests.js
    switch (status?.toLowerCase()) {
      case 'initial':
        return '#f5d784'; // Active initial button color
      case 'approved':
        return '#4caf50'; // Active approved button color
      case 'delivered':
      case 'progress':
        return '#2196f3'; // Active progress button color
      case 'customerpaid':
        return '#e96772'; // Active customerpaid button color
      case 'supplierpaid':
        return '#e87ee3'; // Active supplierpaid button color
      case 'completed':
        return '#198754'; // Active done button color
      case 'rejected':
      case 'cancelled':
        return '#dc3545'; // Active rejected button color
      case 'priced':
      case 'confirmed':
      case 'processing':
        return '#ffc107'; // Active pending button color
      default:
        return '#4c6ef5'; // Default color
    }
  };

  // Fallback colors for additional statuses if needed
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#8DD1E1', '#A4DE6C', '#D0ED57'];

  return (
    <div className="reports-container">
      {/* Main Report Type Tabs */}
      <div className="main-tabs">
          <button
            className={`main-tab-button ${mainTab === 'overview' ? 'active' : ''}`}
            onClick={() => setMainTab('overview')}
          >
            <FiPieChart /> Tổng quan
          </button>
          <button
            className={`main-tab-button ${mainTab === 'customer' ? 'active' : ''}`}
            onClick={() => setMainTab('customer')}
          >
            <FiUser /> Báo cáo khách hàng
          </button>
          <button
            className={`main-tab-button ${mainTab === 'supplier' ? 'active' : ''}`}
            onClick={() => setMainTab('supplier')}
          >
            <FiTruck /> Báo cáo nhà cung cấp
          </button>
        </div>

      <div className={`report-filter-container ${showFilters ? 'expanded' : 'collapsed'}`}>
        <form onSubmit={handleSubmit}>
          <div className="report-filter-group compact">
            <div className="report-filter-item others-filter">
              <label><FiFilter /> Công ty:</label>
              {isCompanyLoading ? (
                <div className="select-loading">Đang tải danh sách công ty...</div>
              ) : (
                <select
                  value={selectedCompanyId}
                  onChange={handleCompanyChange}
                >
                  <option value="">Tất cả công ty</option>
                  {companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.company_name}
                    </option>
                  ))}
                </select>
              )}
            </div>
            <div className="report-filter-item date-filter">
              <label><FiCalendar /> Từ ngày:</label>
              <input
                type="date"
                name="startDate"
                value={dateRange.startDate}
                onChange={handleDateChange}
                required
              />
            </div>
            <div className="report-filter-item date-filter">
              <label><FiCalendar /> Đến ngày:</label>
              <input
                type="date"
                name="endDate"
                value={dateRange.endDate}
                onChange={handleDateChange}
                required
              />
            </div>

            {/* Only show additional filters for customer and supplier tabs */}
            {mainTab !== 'overview' && (
              <>
                {mainTab === 'customer' && (
                  <div className="report-filter-item others-filter">
                    <label><FiUser /> Khách hàng:</label>
                    {isCustomerLoading ? (
                      <div className="select-loading">Đang tải danh sách khách hàng...</div>
                    ) : (
                      <select
                        value={selectedCustomerId}
                        onChange={handleCustomerChange}
                      >
                        <option value="">Tất cả khách hàng</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.customer_name}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                )}
                {mainTab === 'supplier' && (
                  <div className="report-filter-item others-filter">
                    <label><FiUser /> Nhà cung cấp:</label>
                    {isSupplierLoading ? (
                      <div className="select-loading">Đang tải danh sách nhà cung cấp...</div>
                    ) : (
                      <select
                        value={selectedSupplierId}
                        onChange={handleSupplierChange}
                      >
                        <option value="">Tất cả nhà cung cấp</option>
                        {suppliers.map(supplier => (
                          <option key={supplier.id} value={supplier.id}>
                            {supplier.supplier_name}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                )}
              </>
            )}
          </div>

          {mainTab !== 'overview' && (
            <div className="report-filter-group compact">
              <div className="report-filter-item others-filter">
                <label><FiUser /> NV bán hàng:</label>
                {isStaffLoading ? (
                  <div className="select-loading">Đang tải danh sách nhân viên...</div>
                ) : (
                  <select
                    value={selectedSaleStaffId}
                    onChange={handleSaleStaffChange}
                  >
                    <option value="">Tất cả NV bán hàng</option>
                    {staffs.map(staff => (
                      <option key={staff.id} value={staff.id}>
                        {staff.display_name || staff.name || staff.username}
                      </option>
                    ))}
                  </select>
                )}
              </div>
              <div className="report-filter-item others-filter">
                <label><FiUser /> NV định giá:</label>
                {isStaffLoading ? (
                  <div className="select-loading">Đang tải danh sách nhân viên...</div>
                ) : (
                  <select
                    value={selectedPricingStaffId}
                    onChange={handlePricingStaffChange}
                  >
                    <option value="">Tất cả NV định giá</option>
                    {staffs.map(staff => (
                      <option key={staff.id} value={staff.id}>
                        {staff.display_name || staff.name || staff.username}
                      </option>
                    ))}
                  </select>
                )}
              </div>
              <div className="report-filter-item others-filter">
                <label><FiUser /> NV điều phối:</label>
                {isStaffLoading ? (
                  <div className="select-loading">Đang tải danh sách nhân viên...</div>
                ) : (
                  <select
                    value={selectedDispatchStaffId}
                    onChange={handleDispatchStaffChange}
                  >
                    <option value="">Tất cả NV điều phối</option>
                    {staffs.map(staff => (
                      <option key={staff.id} value={staff.id}>
                        {staff.display_name || staff.name || staff.username}
                      </option>
                    ))}
                  </select>
                )}
              </div>
            </div>
          )}

          <div className="report-filter-item report-filter-actions">
            <button type="submit" className="report-btn-filter">
              <FiFilter /> Lọc
            </button>
            <button type="button" className="report-btn-filter report-btn-clear" onClick={() => {
              setSelectedCustomerId('');
              setSelectedSupplierId('');
              setSelectedSaleStaffId('');
              setSelectedPricingStaffId('');
              setSelectedDispatchStaffId('');
              setSelectedStatus('');
            }}>
              <FiFilter /> Xóa lọc
            </button>
          </div>
        </form>
      </div>

      {/* Report Tabs */}

      {error && <div className="error-message">{error}</div>}

      {isLoading ? (
        <div className="loading-container">
          <div className="report-loading-spinner"></div>
        </div>
      ) : (mainTab === 'overview' && !hasAppliedFilters) ? (
        <div className="no-data-message">
          <p>Chưa có dữ liệu báo cáo. Vui lòng chọn điều kiện lọc và nhấn nút "Lọc".</p>
        </div>
      ) : (mainTab === 'overview' && hasAppliedFilters) || (mainTab === 'customer' ? customerReportData : supplierReportData).length > 0 ? (
        <div className="report-content-container">
          {/* Report Content based on selected view type */}
          {reportViewType === 'summary' && (
            <div className="report-summary-view">
              {mainTab === 'overview' ? (
                <>
                  {/* 1. SUMMARY GROUP - Tổng doanh thu, lợi nhuận, chi phí */}
                  <div className="report-group">
                    <h2 className="report-group-title">
                      <FiDollarSign /> Tổng quan tài chính
                    </h2>
                    <div className="report-group-content cards-grid">
                      <div className="summary-card">
                        <div className="summary-icon">
                          <FiDollarSign />
                        </div>
                        <div className="summary-content">
                          <h3>Tổng doanh thu</h3>
                          <p className="summary-number">{formatCurrency(customerReportData.reduce((sum, item) => sum + item.totalRevenue, 0))}</p>
                          <p className="summary-trend positive">+8% so với tháng trước</p>
                        </div>
                      </div>

                      <div className="summary-card">
                        <div className="summary-icon">
                          <FiDollarSign />
                        </div>
                        <div className="summary-content">
                          <h3>Tổng chi phí</h3>
                          <p className="summary-number">{formatCurrency(supplierReportData.reduce((sum, item) => sum + item.totalCost, 0))}</p>
                          <p className="summary-trend negative">+5% so với tháng trước</p>
                        </div>
                      </div>

                      <div className="summary-card">
                        <div className="summary-icon">
                          <FiPieChart />
                        </div>
                        <div className="summary-content">
                          <h3>Tổng lợi nhuận</h3>
                          <p className="summary-number">{formatCurrency(customerReportData.reduce((sum, item) => sum + item.profit, 0))}</p>
                          <p className={`summary-trend ${customerReportData.reduce((sum, item) => sum + item.profit, 0) >= 0 ? 'positive' : 'negative'}`}>
                            {customerReportData.reduce((sum, item) => sum + item.profit, 0) >= 0 ? '+15%' : '-3%'} so với tháng trước
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 2. TICKETS GROUP - Tổng số tickets theo từng trạng thái */}
                  <div className="report-group">
                    <h2 className="report-group-title">
                      <FiTruck /> Thông tin vé
                    </h2>
                    <div className="report-group-content">
                      <div className="dashboard-card">
                        <div className="dashboard-card-header">
                          <h3>
                            <FiPieChart /> Phân bố trạng thái vé
                          </h3>
                        </div>
                        <div className="dashboard-card-body">
                          <div className="chart-content">
                            <ResponsiveContainer width="100%" height={300}>
                              <PieChart>
                                <Pie
                                  data={Object.entries(statusDistribution).map(([statusId, count]) => {
                                    const statusObj = statuses.find(s => s.id === statusId) || { name: statusId };
                                    return {
                                      name: statusObj.name,
                                      value: count,
                                      statusId: statusId
                                    };
                                  }).filter(item => item.value > 0)}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={true}
                                  outerRadius={100}
                                  innerRadius={40}
                                  fill="#8884d8"
                                  dataKey="value"
                                  nameKey="name"
                                  paddingAngle={2}
                                  label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                                >
                                  {Object.entries(statusDistribution)
                                    .filter(([_, count]) => count > 0)
                                    .map(([statusId, _]) => (
                                      <Cell key={`cell-${statusId}`} fill={getStatusColor(statusId)} />
                                    ))}
                                </Pie>
                                <Tooltip
                                  formatter={(value, _, props) => {
                                    const total = Object.values(statusDistribution).reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return [
                                      `${value} vé (${percentage}%)`,
                                      props.payload.name
                                    ];
                                  }}
                                  contentStyle={{
                                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                    borderRadius: '8px',
                                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                                    border: 'none',
                                    padding: '10px 15px'
                                  }}
                                  itemStyle={{ color: '#333' }}
                                />
                                <Legend
                                  layout="horizontal"
                                  verticalAlign="bottom"
                                  align="center"
                                  iconType="circle"
                                  iconSize={10}
                                  wrapperStyle={{
                                    paddingTop: '20px',
                                    fontSize: '12px'
                                  }}
                                />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      </div>

                      <div className="summary-cards cards-grid">
                        <div className="summary-card">
                          <div className="summary-icon">
                            <FiTruck />
                          </div>
                          <div className="summary-content">
                            <h3>Tổng số vé</h3>
                            <p className="summary-number">{customerReportData.reduce((sum, item) => sum + item.totalTickets, 0)}</p>
                            <p className="summary-trend positive">+12% so với tháng trước</p>
                          </div>
                        </div>

                        <div className="summary-card">
                          <div className="summary-icon">
                            <FiUsers />
                          </div>
                          <div className="summary-content">
                            <h3>Vé hoàn thành</h3>
                            <p className="summary-number">{customerReportData.reduce((sum, item) => sum + item.completedTickets, 0)}</p>
                            <p className="summary-trend positive">+10% so với tháng trước</p>
                          </div>
                        </div>

                        <div className="summary-card">
                          <div className="summary-icon">
                            <FiUsers />
                          </div>
                          <div className="summary-content">
                            <h3>Vé đang xử lý</h3>
                            <p className="summary-number">{customerReportData.reduce((sum, item) => sum + item.pendingTickets, 0)}</p>
                            <p className="summary-trend negative">+2% so với tháng trước</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 3. CUSTOMERS GROUP - Tổng số khách hàng, top 10 khách hàng */}
                  <div className="report-group">
                    <h2 className="report-group-title">
                      <FiUsers /> Thông tin khách hàng
                    </h2>
                    <div className="report-group-content">
                      <div className="dashboard-card">
                        <div className="dashboard-card-header">
                          <h3>
                            <FiUsers /> Top 10 khách hàng theo doanh thu
                          </h3>
                        </div>
                        <div className="dashboard-card-body">
                          <div className="ranking-table-container">
                            <table className="ranking-table">
                              <thead>
                                <tr>
                                  <th>Hạng</th>
                                  <th>Khách hàng</th>
                                  <th>Doanh thu</th>
                                  <th>Tỷ lệ</th>
                                </tr>
                              </thead>
                              <tbody>
                                {customerReportData
                                  .sort((a, b) => b.totalRevenue - a.totalRevenue)
                                  .slice(0, 10)
                                  .map((item, index) => {
                                    const total = customerReportData.reduce((sum, i) => sum + i.totalRevenue, 0);
                                    const percentage = ((item.totalRevenue / total) * 100).toFixed(1);

                                    return (
                                      <tr key={index}>
                                        <td className="rank-cell">{index + 1}</td>
                                        <td>{item.customerName}</td>
                                        <td>{formatCurrency(item.totalRevenue)}</td>
                                        <td>
                                          <div className="percentage-bar-container">
                                            <div
                                              className="percentage-bar"
                                              style={{ width: `${percentage}%` }}
                                            ></div>
                                            <span className="percentage-text">{percentage}%</span>
                                          </div>
                                        </td>
                                      </tr>
                                    );
                                  })}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>

                      <div className="summary-cards cards-grid">
                        <div className="summary-card">
                          <div className="summary-icon">
                            <FiUsers />
                          </div>
                          <div className="summary-content">
                            <h3>Tổng số khách hàng</h3>
                            <p className="summary-number">{customerReportData.length}</p>
                            <p className="summary-trend positive">+5% so với tháng trước</p>
                          </div>
                        </div>

                        <div className="dashboard-card">
                          <div className="dashboard-card-header">
                            <h3>
                              <FiUsers /> Top 5 khách hàng theo lợi nhuận
                            </h3>
                          </div>
                          <div className="dashboard-card-body">
                            <div className="ranking-table-container">
                              <table className="ranking-table">
                                <thead>
                                  <tr>
                                    <th>Hạng</th>
                                    <th>Khách hàng</th>
                                    <th>Lợi nhuận</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {customerReportData
                                    .sort((a, b) => b.profit - a.profit)
                                    .slice(0, 5)
                                    .map((item, index) => (
                                      <tr key={index}>
                                        <td className="rank-cell">{index + 1}</td>
                                        <td>{item.customerName}</td>
                                        <td className={item.profit >= 0 ? 'profit-positive' : 'profit-negative'}>
                                          {formatCurrency(item.profit)}
                                        </td>
                                      </tr>
                                    ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 4. SUPPLIERS GROUP - Tổng số nhà cung cấp, top 10 nhà cung cấp */}
                  <div className="report-group">
                    <h2 className="report-group-title">
                      <FiTruck /> Thông tin nhà cung cấp
                    </h2>
                    <div className="report-group-content">
                      <div className="dashboard-card">
                        <div className="dashboard-card-header">
                          <h3>
                            <FiTruck /> Top 10 nhà cung cấp theo chi phí
                          </h3>
                        </div>
                        <div className="dashboard-card-body">
                          <div className="ranking-table-container">
                            <table className="ranking-table">
                              <thead>
                                <tr>
                                  <th>Hạng</th>
                                  <th>Nhà cung cấp</th>
                                  <th>Chi phí</th>
                                  <th>Tỷ lệ</th>
                                </tr>
                              </thead>
                              <tbody>
                                {supplierReportData
                                  .sort((a, b) => b.totalCost - a.totalCost)
                                  .slice(0, 10)
                                  .map((item, index) => {
                                    const total = supplierReportData.reduce((sum, i) => sum + i.totalCost, 0);
                                    const percentage = ((item.totalCost / total) * 100).toFixed(1);

                                    return (
                                      <tr key={index}>
                                        <td className="rank-cell">{index + 1}</td>
                                        <td>{item.supplierName}</td>
                                        <td>{formatCurrency(item.totalCost)}</td>
                                        <td>
                                          <div className="percentage-bar-container">
                                            <div
                                              className="percentage-bar"
                                              style={{ width: `${percentage}%` }}
                                            ></div>
                                            <span className="percentage-text">{percentage}%</span>
                                          </div>
                                        </td>
                                      </tr>
                                    );
                                  })}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>

                      <div className="summary-cards cards-grid">
                        <div className="summary-card">
                          <div className="summary-icon">
                            <FiTruck />
                          </div>
                          <div className="summary-content">
                            <h3>Tổng số nhà cung cấp</h3>
                            <p className="summary-number">{supplierReportData.length}</p>
                            <p className="summary-trend positive">+3% so với tháng trước</p>
                          </div>
                        </div>

                        <div className="dashboard-card">
                          <div className="dashboard-card-header">
                            <h3>
                              <FiTruck /> Số lượng nhà cung cấp theo công ty
                            </h3>
                          </div>
                          <div className="dashboard-card-body">
                            <div className="chart-content">
                              <ResponsiveContainer width="100%" height={200}>
                                <BarChart
                                  data={companies.map(company => {
                                    const suppliersCount = supplierReportData.filter(s =>
                                      s.companyId === company.id || s.company_id === company.id
                                    ).length;
                                    return {
                                      name: company.company_name,
                                      count: suppliersCount
                                    };
                                  })}
                                  margin={{ top: 10, right: 30, left: 20, bottom: 40 }}
                                >
                                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                  <XAxis dataKey="name" angle={-45} textAnchor="end" height={60} />
                                  <YAxis />
                                  <Tooltip />
                                  <Bar dataKey="count" name="Số nhà cung cấp" fill="#82ca9d" />
                                </BarChart>
                              </ResponsiveContainer>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  {/* Customer/Supplier Summary Cards */}
                  <div className="summary-cards">
                    <div className="summary-card">
                      <div className="summary-icon">
                        <FiUsers />
                      </div>
                      <div className="summary-content">
                        <h3>{mainTab === 'customer' ? 'Tổng số khách hàng' : 'Tổng số nhà cung cấp'}</h3>
                        <p className="summary-number">{(mainTab === 'customer' ? customerReportData : supplierReportData).length}</p>
                        <p className="summary-trend positive">+5% so với tháng trước</p>
                      </div>
                    </div>

                    <div className="summary-card">
                      <div className="summary-icon">
                        <FiTruck />
                      </div>
                      <div className="summary-content">
                        <h3>Tổng số vé</h3>
                        <p className="summary-number">{(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalTickets, 0)}</p>
                        <p className="summary-trend positive">+12% so với tháng trước</p>
                      </div>
                    </div>

                    <div className="summary-card">
                      <div className="summary-icon">
                        <FiDollarSign />
                      </div>
                      <div className="summary-content">
                        <h3>{mainTab === 'customer' ? 'Tổng doanh thu' : 'Tổng chi phí'}</h3>
                        <p className="summary-number">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + (mainTab === 'customer' ? item.totalRevenue : item.totalCost), 0))}</p>
                        <p className="summary-trend positive">+8% so với tháng trước</p>
                      </div>
                    </div>

                    <div className="summary-card">
                      <div className="summary-icon">
                        <FiPieChart />
                      </div>
                      <div className="summary-content">
                        <h3>Tổng lợi nhuận</h3>
                        <p className="summary-number">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0))}</p>
                        <p className={`summary-trend ${(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0) >= 0 ? 'positive' : 'negative'}`}>
                          {(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0) >= 0 ? '+15%' : '-3%'} so với tháng trước
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* No additional content needed here for overview tab since we've already added all the content above */}
              {mainTab !== 'overview' && (
                <>
                  {/* Bảng dữ liệu chi tiết */}
                  <div className="dashboard-card full-width">
                    <div className="dashboard-card-header">
                      <h3>
                        <FiList /> {mainTab === 'customer' ? 'Chi tiết khách hàng' : 'Chi tiết nhà cung cấp'}
                      </h3>
                      <div className="card-actions">
                        <button className="action-button" onClick={exportToExcel}>
                          <FiDownload /> Xuất Excel
                        </button>
                      </div>
                    </div>
                    <div className="dashboard-card-body">
                      <div className="report-table-container">
                        <table className="report-table modern-table">
                          <thead>
                            <tr>
                              <th>{mainTab === 'customer' ? 'Khách hàng' : 'Nhà cung cấp'}</th>
                              <th className="center-align">Tổng số vé</th>
                              <th className="center-align">Vé hoàn thành</th>
                              <th className="center-align">Vé đang xử lý</th>
                              <th className="right-align">Tổng doanh thu</th>
                              <th className="right-align">Tổng chi phí</th>
                              <th className="right-align">Lợi nhuận</th>
                              <th className="center-align">Tỷ lệ hoàn thành</th>
                            </tr>
                          </thead>
                          <tbody>
                            {(mainTab === 'customer' ? customerReportData : supplierReportData).map((item, index) => {
                              const completionRate = item.totalTickets > 0 ? (item.completedTickets / item.totalTickets) * 100 : 0;
                              return (
                                <tr key={index}>
                                  <td>
                                    <div className="entity-name">
                                      <div className="entity-avatar">{(mainTab === 'customer' ? item.customerName : item.supplierName).charAt(0)}</div>
                                      <span>{mainTab === 'customer' ? item.customerName : item.supplierName}</span>
                                    </div>
                                  </td>
                                  <td className="center-align">{item.totalTickets}</td>
                                  <td className="center-align">{item.completedTickets}</td>
                                  <td className="center-align">{item.pendingTickets}</td>
                                  <td className="right-align">{formatCurrency(item.totalRevenue)}</td>
                                  <td className="right-align">{formatCurrency(item.totalCost)}</td>
                                  <td className={`right-align ${item.profit >= 0 ? 'profit-positive' : 'profit-negative'}`}>
                                    {formatCurrency(item.profit)}
                                  </td>
                                  <td className="center-align">
                                    <div className="progress-bar-container">
                                      <div
                                        className="progress-bar"
                                        style={{ width: `${completionRate}%`, backgroundColor: completionRate > 70 ? '#4caf50' : completionRate > 30 ? '#ff9800' : '#f44336' }}
                                      ></div>
                                      <span className="progress-text">{completionRate.toFixed(0)}%</span>
                                    </div>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                          {(mainTab === 'customer' ? customerReportData : supplierReportData).length > 1 && (
                            <tfoot>
                              <tr>
                                <td><strong>Tổng cộng</strong></td>
                                <td className="center-align">{(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalTickets, 0)}</td>
                                <td className="center-align">{(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.completedTickets, 0)}</td>
                                <td className="center-align">{(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.pendingTickets, 0)}</td>
                                <td className="right-align">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalRevenue, 0))}</td>
                                <td className="right-align">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalCost, 0))}</td>
                                <td className={`right-align ${(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0) >= 0 ? 'profit-positive' : 'profit-negative'}`}>
                                  {formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0))}
                                </td>
                                <td className="center-align">
                                  {(() => {
                                    const totalTickets = (mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalTickets, 0);
                                    const completedTickets = (mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.completedTickets, 0);
                                    const completionRate = totalTickets > 0 ? (completedTickets / totalTickets) * 100 : 0;
                                    return (
                                      <div className="progress-bar-container">
                                        <div
                                          className="progress-bar"
                                          style={{ width: `${completionRate}%`, backgroundColor: completionRate > 70 ? '#4caf50' : completionRate > 30 ? '#ff9800' : '#f44336' }}
                                        ></div>
                                        <span className="progress-text">{completionRate.toFixed(0)}%</span>
                                      </div>
                                    );
                                  })()}
                                </td>
                              </tr>
                            </tfoot>
                          )}
                        </table>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Biểu đồ theo thời gian */}
              {/* <div className="dashboard-card revenue-timeline-card">
                <div className="dashboard-card-header">
                  <h3>
                    <FiBarChart2 /> {mainTab === 'customer' ? 'Doanh thu theo thời gian' : 'Chi phí theo thời gian'}
                  </h3>
                  <div className="card-actions">
                    <div className="chart-period-selector">
                      <button className="period-button active">Tuần</button>
                      <button className="period-button">Tháng</button>
                      <button className="period-button">Quý</button>
                    </div>
                  </div>
                </div>
                <div className="dashboard-card-body">
                  <div className="chart-summary">
                    <div className="chart-summary-item">
                      <span className="chart-summary-label">Tổng {mainTab === 'customer' ? 'doanh thu' : 'chi phí'}</span>
                      <span className="chart-summary-value">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + (mainTab === 'customer' ? item.totalRevenue : item.totalCost), 0))}</span>
                      <span className="chart-summary-trend positive">+15% so với kỳ trước</span>
                    </div>
                    <div className="chart-summary-item">
                      <span className="chart-summary-label">Trung bình</span>
                      <span className="chart-summary-value">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + (mainTab === 'customer' ? item.totalRevenue : item.totalCost), 0) / (mainTab === 'customer' ? customerReportData : supplierReportData).length || 1)}</span>
                    </div>
                  </div>
                  <div className="chart-content timeline-chart">
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart
                        data={mainTab === 'customer' ? getCustomerTimelineChartData() : getSupplierTimelineChartData()}
                        margin={{ top: 20, right: 30, left: 20, bottom: 50 }}
                      >
                        <defs>
                          {(mainTab === 'customer' ? customerReportData : supplierReportData).slice(0, 5).map((_, index) => (
                            <linearGradient key={`gradient-${index}`} id={`colorGradient${index}`} x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor={COLORS[index % COLORS.length]} stopOpacity={0.8}/>
                              <stop offset="95%" stopColor={COLORS[index % COLORS.length]} stopOpacity={0.1}/>
                            </linearGradient>
                          ))}
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" vertical={false} />
                        <XAxis
                          dataKey="date"
                          angle={-45}
                          textAnchor="end"
                          height={60}
                          tick={{ fontSize: 12 }}
                          axisLine={{ stroke: '#e0e0e0' }}
                          tickLine={{ stroke: '#e0e0e0' }}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => value >= 1000000 ? `${(value / 1000000).toFixed(0)}M` : value >= 1000 ? `${(value / 1000).toFixed(0)}K` : value}
                        />
                        <Tooltip
                          formatter={(value) => formatCurrency(value)}
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            borderRadius: '8px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                            border: 'none',
                            padding: '10px 15px'
                          }}
                          labelStyle={{ fontWeight: 'bold', marginBottom: '5px' }}
                        />
                        <Legend
                          layout="horizontal"
                          verticalAlign="top"
                          align="center"
                          wrapperStyle={{ paddingBottom: '20px' }}
                          iconType="circle"
                          iconSize={10}
                        />
                        {(mainTab === 'customer' ? customerReportData : supplierReportData).slice(0, 5).map((customer, index) => (
                          <Line
                            key={customer.customerId || customer.supplierId}
                            type="monotone"
                            dataKey={mainTab === 'customer' ? `customer_${customer.customerId}` : `supplier_${customer.supplierId}`}
                            name={mainTab === 'customer' ? customer.customerName : customer.supplierName}
                            stroke={COLORS[index % COLORS.length]}
                            strokeWidth={3}
                            dot={{ stroke: COLORS[index % COLORS.length], strokeWidth: 2, r: 4, fill: 'white' }}
                            activeDot={{ r: 8, stroke: COLORS[index % COLORS.length], strokeWidth: 2, fill: 'white' }}
                            fillOpacity={1}
                            fill={`url(#colorGradient${index})`}
                          />
                        ))}
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div> */}

              {/* Biểu đồ cột */}
              {/* <div className="dashboard-card customer-analysis-card">
                <div className="dashboard-card-header">
                  <h3>
                    <FiBarChart2 /> {mainTab === 'customer' ? 'Phân tích khách hàng' : 'Phân tích nhà cung cấp'}
                  </h3>
                  <div className="card-actions">
                    <div className="chart-view-selector">
                      <button className="view-button active">Top 5</button>
                      <button className="view-button">Top 10</button>
                    </div>
                  </div>
                </div>
                <div className="dashboard-card-body">
                  <div className="chart-content bar-chart">
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart
                        data={mainTab === 'customer' ? getCustomerBarChartData().slice(0, 5) : getSupplierBarChartData().slice(0, 5)}
                        margin={{ top: 20, right: 30, left: 20, bottom: 50 }}
                      >
                        <defs>
                          <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#0088FE" stopOpacity={0.8}/>
                            <stop offset="100%" stopColor="#0088FE" stopOpacity={0.2}/>
                          </linearGradient>
                          <linearGradient id="costGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#FF8042" stopOpacity={0.8}/>
                            <stop offset="100%" stopColor="#FF8042" stopOpacity={0.2}/>
                          </linearGradient>
                          <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#00C49F" stopOpacity={0.8}/>
                            <stop offset="100%" stopColor="#00C49F" stopOpacity={0.2}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" vertical={false} />
                        <XAxis
                          dataKey="name"
                          angle={-45}
                          textAnchor="end"
                          height={60}
                          axisLine={{ stroke: '#e0e0e0' }}
                          tickLine={{ stroke: '#e0e0e0' }}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => value >= 1000000 ? `${(value / 1000000).toFixed(0)}M` : value >= 1000 ? `${(value / 1000).toFixed(0)}K` : value}
                        />
                        <Tooltip
                          formatter={(value) => formatCurrency(value)}
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            borderRadius: '8px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                            border: 'none',
                            padding: '10px 15px'
                          }}
                          cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                        />
                        <Legend
                          iconType="circle"
                          iconSize={10}
                          wrapperStyle={{ paddingTop: '15px' }}
                        />
                        <Bar
                          dataKey="revenue"
                          name="Doanh thu"
                          fill="url(#revenueGradient)"
                          radius={[4, 4, 0, 0]}
                          barSize={20}
                          animationDuration={1500}
                        />
                        <Bar
                          dataKey="cost"
                          name="Chi phí"
                          fill="url(#costGradient)"
                          radius={[4, 4, 0, 0]}
                          barSize={20}
                          animationDuration={1500}
                          animationDelay={300}
                        />
                        <Bar
                          dataKey="profit"
                          name="Lợi nhuận"
                          fill="url(#profitGradient)"
                          radius={[4, 4, 0, 0]}
                          barSize={20}
                          animationDuration={1500}
                          animationDelay={600}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div> */}

              {/* Biểu đồ phân bố trạng thái */}
              <div className="dashboard-card">
                <div className="dashboard-card-header">
                  <h3>
                    <FiPieChart /> Phân bố trạng thái vé
                  </h3>
                </div>
                <div className="dashboard-card-body">
                  <div className="chart-content">
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={Object.entries(statusDistribution).map(([statusId, count]) => {
                            const statusObj = statuses.find(s => s.id === statusId) || { name: statusId };
                            return {
                              name: statusObj.name,
                              value: count,
                              statusId: statusId
                            };
                          }).filter(item => item.value > 0)}
                          cx="50%"
                          cy="50%"
                          labelLine={true}
                          outerRadius={100}
                          innerRadius={40}
                          fill="#8884d8"
                          dataKey="value"
                          nameKey="name"
                          paddingAngle={2}
                          label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                        >
                          {Object.entries(statusDistribution)
                            .filter(([_, count]) => count > 0)
                            .map(([statusId, _]) => (
                              <Cell key={`cell-${statusId}`} fill={getStatusColor(statusId)} />
                            ))}
                        </Pie>
                        <Tooltip
                          formatter={(value, _, props) => {
                            const total = Object.values(statusDistribution).reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return [
                              `${value} vé (${percentage}%)`,
                              props.payload.name
                            ];
                          }}
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            borderRadius: '8px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                            border: 'none',
                            padding: '10px 15px'
                          }}
                          itemStyle={{ color: '#333' }}
                        />
                        <Legend
                          layout="horizontal"
                          verticalAlign="bottom"
                          align="center"
                          iconType="circle"
                          iconSize={10}
                          wrapperStyle={{
                            paddingTop: '20px',
                            fontSize: '12px'
                          }}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="status-distribution-table">
                    <table className="status-table">
                      <thead>
                        <tr>
                          <th>Trạng thái</th>
                          <th className="center-align">Số lượng</th>
                          <th className="center-align">Tỷ lệ</th>
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(statusDistribution)
                          .filter(([_, count]) => count > 0)
                          .sort((a, b) => b[1] - a[1])
                          .map(([statusId, count]) => {
                            const statusObj = statuses.find(s => s.id === statusId) || { name: statusId };
                            const total = Object.values(statusDistribution).reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? (count / total) * 100 : 0;

                            return (
                              <tr key={statusId}>
                                <td>
                                  <div className="status-name">
                                    <div className="status-color" style={{ backgroundColor: getStatusColor(statusId) }}></div>
                                    <span className="status-badge" style={{ backgroundColor: getStatusColor(statusId), color: 'white', padding: '3px 8px' }}>
                                      {statusObj.name}
                                    </span>
                                  </div>
                                </td>
                                <td className="center-align">{count}</td>
                                <td className="center-align">{percentage.toFixed(1)}%</td>
                              </tr>
                            );
                          })}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              {/* Biểu đồ doanh thu theo trạng thái */}
              <div className="dashboard-card">
                <div className="dashboard-card-header">
                  <h3>
                    <FiBarChart2 /> Doanh thu và lợi nhuận theo trạng thái
                  </h3>
                  <div className="card-actions">
                    <div className="chart-period-selector">
                      <button
                        className={`period-button ${revenuePeriod === 'day' ? 'active' : ''}`}
                        onClick={() => setRevenuePeriod('day')}
                      >
                        Ngày
                      </button>
                      <button
                        className={`period-button ${revenuePeriod === 'month' ? 'active' : ''}`}
                        onClick={() => setRevenuePeriod('month')}
                      >
                        Tháng
                      </button>
                      <button
                        className={`period-button ${revenuePeriod === 'quarter' ? 'active' : ''}`}
                        onClick={() => setRevenuePeriod('quarter')}
                      >
                        Quý
                      </button>
                    </div>
                  </div>
                </div>
                <div className="dashboard-card-body">
                  <div className="chart-summary">
                    <div className="chart-summary-item">
                      <span className="chart-summary-label">Tổng doanh thu</span>
                      <span className="chart-summary-value">
                        {formatCurrency(getRevenueByStatusChartData().reduce((sum, item) => {
                          let total = 0;
                          Object.keys(statusDistribution)
                            .filter(status => statusDistribution[status] > 0)
                            .forEach(status => {
                              total += item[status] || 0;
                            });
                          return sum + total;
                        }, 0))}
                      </span>
                    </div>
                    <div className="chart-summary-item">
                      <span className="chart-summary-label">Tổng chi phí</span>
                      <span className="chart-summary-value">
                        {formatCurrency(getRevenueByStatusChartData().reduce((sum, item) => sum + (item.totalCost || 0), 0))}
                      </span>
                    </div>
                    <div className="chart-summary-item">
                      <span className="chart-summary-label">Tổng lợi nhuận</span>
                      <span className="chart-summary-value profit">
                        {formatCurrency(getRevenueByStatusChartData().reduce((sum, item) => sum + (item.profit || 0), 0))}
                      </span>
                    </div>
                  </div>
                  <div className="chart-content">
                    <ResponsiveContainer width="100%" height={400}>
                      <ComposedChart
                        data={getRevenueByStatusChartData()}
                        margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" vertical={false} />
                        <XAxis
                          dataKey="date"
                          angle={revenuePeriod === 'day' ? -45 : 0}
                          textAnchor={revenuePeriod === 'day' ? "end" : "middle"}
                          height={revenuePeriod === 'day' ? 70 : 40}
                          tick={{ fontSize: 12 }}
                          interval={0}
                        />
                        <YAxis
                          yAxisId="left"
                          tickFormatter={(value) => value >= 1000000 ? `${(value / 1000000).toFixed(0)}M` : value >= 1000 ? `${(value / 1000).toFixed(0)}K` : value}
                          axisLine={false}
                          tickLine={false}
                        />
                        <YAxis
                          yAxisId="right"
                          orientation="right"
                          tickFormatter={(value) => value >= 1000000 ? `${(value / 1000000).toFixed(0)}M` : value >= 1000 ? `${(value / 1000).toFixed(0)}K` : value}
                          axisLine={false}
                          tickLine={false}
                        />
                        <Tooltip
                          formatter={(value, name, props) => {
                            // Xử lý riêng cho lợi nhuận
                            if (name === 'Lợi nhuận') {
                              return [formatCurrency(value), name];
                            }

                            // Tìm trạng thái tương ứng với name
                            const statusObj = statuses.find(s => s.id === name);
                            const statusName = statusObj ? statusObj.name : name;

                            // Tính phần trăm
                            const total = props.payload.total || 0;
                            const percentage = total > 0 ? (value / total * 100).toFixed(1) : '0';

                            return [
                              `${formatCurrency(value)} (${percentage}%)`,
                              statusName
                            ];
                          }}
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            borderRadius: '8px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                            border: 'none',
                            padding: '10px 15px'
                          }}
                          itemStyle={{ color: '#333' }}
                          labelStyle={{ fontWeight: 'bold', marginBottom: '5px' }}
                        />
                        <Legend
                          formatter={(value) => {
                            // Xử lý riêng cho lợi nhuận
                            if (value === 'profit') {
                              return 'Lợi nhuận';
                            }

                            // Tìm trạng thái tương ứng với value
                            const statusObj = statuses.find(s => s.id === value);
                            return statusObj ? statusObj.name : value;
                          }}
                          wrapperStyle={{ paddingTop: '20px' }}
                          iconType="circle"
                          iconSize={10}
                        />
                        {Object.keys(statusDistribution)
                          .filter(status => statusDistribution[status] > 0)
                          .map((status) => (
                            <Bar
                              key={status}
                              dataKey={status}
                              stackId="a"
                              fill={getStatusColor(status)}
                              radius={[4, 4, 0, 0]}
                              name={status}
                              yAxisId="left"
                            />
                          ))}
                        {Object.keys(statusDistribution).filter(status => statusDistribution[status] > 0).length === 0 && (
                          <Bar
                            dataKey="noData"
                            fill="#cccccc"
                            name="Không có dữ liệu"
                            yAxisId="left"
                          />
                        )}

                        {/* Line chart for profit */}
                        <Line
                          type="monotone"
                          dataKey="profit"
                          stroke="#ff0000"
                          strokeWidth={3}
                          name="Lợi nhuận"
                          dot={{ stroke: '#ff0000', strokeWidth: 2, r: 4, fill: 'white' }}
                          activeDot={{ r: 8, stroke: '#ff0000', strokeWidth: 2, fill: 'white' }}
                          yAxisId="right"
                        />
                      </ComposedChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>

              {/* Biểu đồ xu hướng theo tháng */}
              {/* <div className="dashboard-card">
                <div className="dashboard-card-header">
                  <h3>
                    <FiBarChart2 /> Xu hướng theo tháng
                  </h3>
                </div>
                <div className="dashboard-card-body">
                  <div className="chart-content">
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart
                        data={[
                          { month: 'T1', value: 4000 },
                          { month: 'T2', value: 3000 },
                          { month: 'T3', value: 2000 },
                          { month: 'T4', value: 2780 },
                          { month: 'T5', value: 1890 },
                          { month: 'T6', value: 2390 },
                          { month: 'T7', value: 3490 },
                          { month: 'T8', value: 4000 },
                          { month: 'T9', value: 5000 },
                          { month: 'T10', value: 4500 },
                          { month: 'T11', value: 5500 },
                          { month: 'T12', value: 6000 }
                        ]}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip formatter={(value) => formatCurrency(value)} />
                        <Bar dataKey="value" name={mainTab === 'customer' ? 'Doanh thu' : 'Chi phí'} fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div> */}

              {/* Biểu đồ so sánh hiệu suất */}
              <div className="dashboard-card">
                <div className="dashboard-card-header">
                  <h3>
                    <FiBarChart2 /> So sánh hiệu suất
                  </h3>
                  <div className="card-actions">
                    {/* Period selector */}
                    <div className="chart-period-selector">
                      <button
                        type="button"
                        className={`period-button ${performancePeriod === 'month' ? 'active' : ''}`}
                        onClick={() => setPerformancePeriod('month')}
                      >
                        Tháng
                      </button>
                      <button
                        type="button"
                        className={`period-button ${performancePeriod === 'quarter' ? 'active' : ''}`}
                        onClick={() => setPerformancePeriod('quarter')}
                      >
                        Quý
                      </button>
                    </div>
                  </div>
                </div>
                <div className="dashboard-card-body">
                  <div className="chart-content">
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart
                        layout="vertical"
                        data={getPerformanceComparisonData()}
                        margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis
                          type="number"
                          tickFormatter={(value) => {
                            // Format large values appropriately
                            return value >= 1000000 ? `${(value / 1000000).toFixed(1)}M` :
                                  value >= 1000 ? `${(value / 1000).toFixed(0)}K` : value;
                          }}
                        />
                        <YAxis dataKey="name" type="category" width={100} />
                        <Tooltip
                          formatter={(value, name, props) => {
                            const dataItem = getPerformanceComparisonData().find(item => item.name === props.payload.name);

                            // Format based on data type
                            if (dataItem && dataItem.isCurrency) {
                              return [formatCurrency(value), name];
                            } else if (dataItem && dataItem.isCount) {
                              return [`${value.toLocaleString('vi-VN')} vé`, name];
                            }

                            return [value, name];
                          }}
                          labelFormatter={(label) => {
                            const dataItem = getPerformanceComparisonData().find(item => item.name === label);
                            return `${label} (${dataItem?.periodLabel || ''})`;
                          }}
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            borderRadius: '8px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                            border: 'none',
                            padding: '10px 15px'
                          }}
                        />
                        <Legend />
                        <Bar dataKey="current" name="Hiện tại" fill="#4c6ef5" radius={[4, 4, 0, 0]} />
                        <Bar dataKey="previous" name="Kỳ trước" fill="#82ca9d" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>

              {/* Bảng xếp hạng */}
              <div className="dashboard-card">
                <div className="dashboard-card-header">
                  <h3>
                    <FiUsers /> {mainTab === 'customer' ? 'Top khách hàng' : 'Top nhà cung cấp'}
                  </h3>
                </div>
                <div className="dashboard-card-body">
                  <div className="ranking-table-container">
                    <table className="ranking-table">
                      <thead>
                        <tr>
                          <th>Hạng</th>
                          <th>{mainTab === 'customer' ? 'Khách hàng' : 'Nhà cung cấp'}</th>
                          <th>{mainTab === 'customer' ? 'Doanh thu' : 'Chi phí'}</th>
                          <th>Tỷ lệ</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(mainTab === 'customer' ? customerReportData : supplierReportData)
                          .sort((a, b) => mainTab === 'customer' ?
                            b.totalRevenue - a.totalRevenue :
                            b.totalCost - a.totalCost)
                          .slice(0, 5)
                          .map((item, index) => {
                            const total = (mainTab === 'customer' ? customerReportData : supplierReportData)
                              .reduce((sum, i) => sum + (mainTab === 'customer' ? i.totalRevenue : i.totalCost), 0);
                            const value = mainTab === 'customer' ? item.totalRevenue : item.totalCost;
                            const percentage = ((value / total) * 100).toFixed(1);

                            return (
                              <tr key={index}>
                                <td className="rank-cell">{index + 1}</td>
                                <td>{mainTab === 'customer' ? item.customerName : item.supplierName}</td>
                                <td>{formatCurrency(value)}</td>
                                <td>
                                  <div className="percentage-bar-container">
                                    <div
                                      className="percentage-bar"
                                      style={{ width: `${percentage}%` }}
                                    ></div>
                                    <span className="percentage-text">{percentage}%</span>
                                  </div>
                                </td>
                              </tr>
                            );
                          })}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
            </div>
          )}
        </div>
      ) : (
        <div className="no-data-message">
          <p>Chưa có dữ liệu báo cáo. Vui lòng chọn điều kiện lọc và nhấn nút "Lọc".</p>
        </div>
      )}
    </div>
  );
};

export default Reports;