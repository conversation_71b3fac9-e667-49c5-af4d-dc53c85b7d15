import React, { useState, useEffect, useRef } from "react";
import {
  FiEdit,
  FiTrash2,
  FiUserPlus,
  FiUserCheck,
  FiChevronUp,
  FiChevronDown,
  FiSearch,
  FiAlertCircle,
  FiEye,
} from "react-icons/fi";
import "../styles/Users.css";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import { useNavigate } from "react-router-dom";

const Users = () => {
  const navigate = useNavigate();
  const usernameInputRef = useRef(null);

  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(() => {
    const savedItemsPerPage = localStorage.getItem("itemsPerPage");
    return savedItemsPerPage ? parseInt(savedItemsPerPage, 10) : 10;
  });
  const [sortConfig, setSortConfig] = useState({
    key: "updated_at",
    direction: "descending",
  });
  const [filters, setFilters] = useState({
    role: "all",
    status: "all",
    company: "all",
    search: "",
  });
  const [selectedUser, setSelectedUser] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState("view"); // 'view', 'edit', 'add', 'delete'
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [modalError, setModalError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [columns, setColumns] = useState([]);
  const [displayColumns, setDisplayColumns] = useState([]);
  const [showPassword, setShowPassword] = useState(false);
  const initialLoadDone = useRef(false);

  // Refs for autocomplete dropdowns
  const managerSearchRef = useRef(null);
  const companySearchRef = useRef(null);

  // State for managers and companies
  const [managers, setManagers] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [managersLoading, setManagersLoading] = useState(false);
  const [companiesLoading, setCompaniesLoading] = useState(false);

  // Role options
  const roleOptions = [
    { id: "all", name: "All" },
    { id: "admin", name: "Admin" },
    { id: "sale", name: "Sale" },
    { id: "accountant", name: "Accountant" },
    { id: "operator", name: "Operator" },
  ];

  // Status options
  const statusOptions = [
    { id: "all", name: "All" },
    { id: "active", name: "Active" },
    { id: "inactive", name: "Inactive" },
    { id: "remove", name: "Removed" },
  ];

  // Fetch users from API
  const fetchUsers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiService.get(ENDPOINTS.USERS.GET_ALL);
      setUsers(response);

      // Extract columns from the first user object
      if (response && response.length > 0) {
        const firstUser = response[0];

        // Extract all columns for the form
        const allColumns = Object.keys(firstUser).map((key) => ({
          key,
          label: formatColumnLabel(key),
        }));

        // Filter out hashed_password for table display only
        const displayColumns = allColumns.filter(col => col.key !== 'hashed_password' && col.key !== 'created_at' && col.key !== 'updated_at');

        // Set both full columns (for form) and display columns (for table)
        setColumns(allColumns);
        setDisplayColumns(displayColumns);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      setError("Unable to load user data. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  // Format column label from key
  const formatColumnLabel = (key) => {
    // Special cases for manager_id and company_id
    if (key === "manager_id") return "Manager";
    if (key === "company_id") return "Company";

    // Convert snake_case or camelCase to Title Case
    return key
      .replace(/_/g, " ")
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  // Fetch managers (users for manager selection)
  const fetchManagers = async () => {
    setManagersLoading(true);
    try {
      const response = await apiService.get(ENDPOINTS.USERS.GET_BASIC);
      if (response && Array.isArray(response)) {
        setManagers(response);
      } else if (response && response.users) {
        setManagers(response.users);
      } else if (response && response.data) {
        setManagers(response.data);
      }
    } catch (error) {
      console.error("Error fetching managers:", error);
    } finally {
      setManagersLoading(false);
    }
  };

  // Get companies from localStorage
  const fetchCompanies = () => {
    setCompaniesLoading(true);
    try {
      const companyInfoStr = localStorage.getItem('companyInfo');
      if (companyInfoStr) {
        const companyInfo = JSON.parse(companyInfoStr);
        if (Array.isArray(companyInfo)) {
          setCompanies(companyInfo);
        } else {
          // If it's a single object, convert to array
          setCompanies([companyInfo]);
        }
      }
    } catch (error) {
      console.error("Error parsing company info:", error);
    } finally {
      setCompaniesLoading(false);
    }
  };

  // Fetch users and other data when component mounts
  useEffect(() => {
    if (!initialLoadDone.current) {
      fetchUsers();
      fetchManagers();
      fetchCompanies();
      initialLoadDone.current = true;
    }
  }, []);

  // Handle filtering and sorting
  useEffect(() => {
    let result = [...users];

    // Apply role filter
    if (filters.role !== "all") {
      result = result.filter((user) => user.role === filters.role);
    }

    // Apply status filter
    if (filters.status !== "all") {
      result = result.filter((user) => user.status === filters.status);
    }

    // Apply company filter
    if (filters.company !== "all") {
      result = result.filter((user) => user.company_id === parseInt(filters.company));
    }

    // Apply search
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      result = result.filter((user) =>
        Object.values(user).some(
          (value) =>
            value &&
            typeof value === "string" &&
            value.toLowerCase().includes(searchLower)
        )
      );
    }

    // Apply sorting
    if (sortConfig.key) {
      result.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? 1 : -1;
        }
        return 0;
      });
    }

    setFilteredUsers(result);
  }, [users, filters, sortConfig]);

  // Handle sorting when clicking on table header
  const requestSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  // Display sort icon
  const getSortIcon = (columnName) => {
    if (sortConfig.key !== columnName) {
      return <span className="sort-icon sort-inactive">⇅</span>;
    }

    return sortConfig.direction === "ascending" ? (
      <FiChevronUp className="sort-icon" />
    ) : (
      <FiChevronDown className="sort-icon" />
    );
  };

  // Handle pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredUsers.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);

  // Handle page navigation
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  const nextPage = () =>
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage((prev) => Math.max(prev - 1, 1));
  const firstPage = () => setCurrentPage(1);
  const lastPage = () => setCurrentPage(totalPages);

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;

    // Handle special sort logic
    if (name === 'sort') {
      if (value === 'newest') {
        setSortConfig({ key: 'updated_at', direction: 'descending' });
      } else if (value === 'oldest') {
        setSortConfig({ key: 'updated_at', direction: 'ascending' });
      }
    }

    setFilters((prev) => ({ ...prev, [name]: value }));
    setCurrentPage(1); // Reset to page 1 when filter changes

    // Update localStorage when platform changes
    if (name === 'platform') {
      localStorage.setItem("currentPlatform", value);
    }
  };

  // Xử lý thay đổi số lượng items mỗi trang
  const handleItemsPerPageChange = (e) => {
    const value = parseInt(e.target.value, 10);
    setItemsPerPage(value);
    localStorage.setItem("itemsPerPage", value.toString());
    // Reset về trang 1 khi thay đổi số lượng items
    setCurrentPage(1);
    localStorage.setItem("currentPage", "1");
  };

  // Handle search
  const handleSearch = (e) => {
    const searchValue = e.target.value.toLowerCase();
    setFilters((prev) => ({ ...prev, search: searchValue }));
    setCurrentPage(1);

    // Filter users based on search value across all columns
    const filtered = users.filter((user) => {
      return Object.values(user).some((value) => {
        // Convert value to string and check if it includes search value
        const stringValue = String(value).toLowerCase();
        return stringValue.includes(searchValue);
      });
    });

    setFilteredUsers(filtered);
  };

  // Handle opening modal
  const openModal = (user, mode) => {
    // Clear any previous error messages
    setModalError(null);

    if (mode === "view") {
      navigate(`/userdetail?username=${user.username}`);
      return;
    }
    if (mode === "add") {
      setSelectedUser({
        id: users.length > 0 ? Math.max(...users.map((u) => u.id)) + 1 : 1000,
        username: "",
        is_admin: false,
        display_name: "",
        status: "active",
        role: "user",
        managerSearch: "", // For manager autocomplete
        companySearch: "", // For company autocomplete
        showManagerDropdown: false,
        showCompanyDropdown: false
      });
    } else {
      // For edit mode, find the manager and company names
      const manager = managers.find(m => m.id === user.manager_id);
      const managerName = manager ? (manager.display_name || manager.username) : "";

      // Get company name from localStorage
      let companyName = "";
      try {
        const companyInfoStr = localStorage.getItem('companyInfo');
        if (companyInfoStr && user.company_id) {
          const companyInfo = JSON.parse(companyInfoStr);
          const company = Array.isArray(companyInfo)
            ? companyInfo.find(c => c.id === user.company_id)
            : (companyInfo.id === user.company_id ? companyInfo : null);

          companyName = company ? company.company_name : "";
        }
      } catch (error) {
        console.error('Error parsing company info:', error);
      }

      setSelectedUser({
        ...user,
        managerSearch: managerName,
        companySearch: companyName,
        showManagerDropdown: false,
        showCompanyDropdown: false
      });
    }
    setModalMode(mode);
    setIsModalOpen(true);
  };

  // Focus username input when modal opens in add mode
  useEffect(() => {
    if (isModalOpen && modalMode === "add" && usernameInputRef.current) {
      usernameInputRef.current.focus();
    }
  }, [isModalOpen, modalMode]);

  // Handle click outside for manager dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (managerSearchRef.current && !managerSearchRef.current.contains(event.target)) {
        setSelectedUser(prev => prev ? { ...prev, showManagerDropdown: false } : prev);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [managerSearchRef]);

  // Handle click outside for company dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (companySearchRef.current && !companySearchRef.current.contains(event.target)) {
        setSelectedUser(prev => prev ? { ...prev, showCompanyDropdown: false } : prev);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [companySearchRef]);

  // Handle closing modal
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
    setModalError(null);
  };

  // Handle deleting user
  const handleDeleteUser = async () => {
    if (selectedUser) {
      try {
        await apiService.delete(ENDPOINTS.USERS.DELETE(selectedUser.id));
        // Update state directly instead of fetching
        setUsers((prevUsers) =>
          prevUsers.filter((user) => user.id !== selectedUser.id)
        );
        closeModal();
      } catch (error) {
        console.error("Error deleting user:", error);
        setError("Unable to delete user. Please try again later.");
      }
    }
  };

  // Validate form fields before submission
  const validateForm = () => {
    const requiredFields = modalMode === "add"
      ? ['username', 'display_name', 'company_id', 'manager_id', 'hashed_password']
      : ['username', 'display_name', 'company_id', 'manager_id', 'role', 'status'];

    const missingFields = [];

    requiredFields.forEach(field => {
      if (!selectedUser[field]) {
        // Use 'Password' instead of 'Hashed password' in error messages
        if (field === 'hashed_password') {
          missingFields.push('Password');
        } else {
          missingFields.push(formatColumnLabel(field));
        }
      }
    });

    if (missingFields.length > 0) {
      setModalError(`Please fill in all required fields: ${missingFields.join(', ')}`);
      return false;
    }

    setModalError(null);
    return true;
  };

  // Handle adding/updating user
  const handleSaveUser = async () => {
    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    try {
      if (modalMode === "add") {
        // Only include required fields for user creation
        const userData = {
          username: selectedUser.username,
          display_name: selectedUser.display_name,
          company_id: selectedUser.company_id,
          manager_id: selectedUser.manager_id,
          password: selectedUser.hashed_password // Use password field instead of hashed_password
        };

        await apiService.post(
          ENDPOINTS.USERS.CREATE,
          userData
        );

        // Hiển thị thông báo thành công
        setSuccessMessage(`User ${userData.username} has been created successfully!`);
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000); // Tự động ẩn thông báo sau 5 giây

        // Refresh user data after creation
        await fetchUsers();
        closeModal();
      } else if (modalMode === "edit") {
        // Only include required fields for user modification
        const userData = {
          username: selectedUser.username,
          display_name: selectedUser.display_name,
          company_id: selectedUser.company_id,
          manager_id: selectedUser.manager_id,
          role: selectedUser.role,
          status: selectedUser.status
        };

        await apiService.post(ENDPOINTS.USERS.UPDATE, userData);

        // Hiển thị thông báo thành công
        setSuccessMessage(`User ${userData.username} has been updated successfully!`);
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000); // Tự động ẩn thông báo sau 5 giây

        // Refresh user data after modification
        await fetchUsers();
        closeModal();
      }
    } catch (error) {
      console.error("Error saving user:", error);

      // Hiển thị lỗi từ API trong modal
      if (error.response && error.response.data) {
        // Nếu API trả về thông báo lỗi cụ thể
        if (error.response.data.detail) {
          setModalError(error.response.data.detail);
        } else if (error.response.data.message) {
          setModalError(error.response.data.message);
        } else if (typeof error.response.data === 'string') {
          setModalError(error.response.data);
        } else {
          setModalError("Không thể lưu thông tin người dùng. Vui lòng thử lại sau.");
        }
      } else if (error.message) {
        // Nếu có thông báo lỗi từ JavaScript
        setModalError(error.message);
      } else {
        // Thông báo lỗi mặc định
        setModalError("Không thể lưu thông tin người dùng. Vui lòng thử lại sau.");
      }
    }
  };

  // Handle user information changes
  const handleUserChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Handle checkbox fields
    if (type === "checkbox") {
      setSelectedUser((prev) => ({ ...prev, [name]: checked }));
    }
    // Handle regular input fields
    else {
      setSelectedUser((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);

    // Format as YYYY-MM-DD HH:MM:SS
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  // Format cell value based on column type
  const formatCellValue = (key, value) => {
    if (value === null || value === undefined) return "";

    // Format status
    if (key === "status") {
      return (
        <span className={`status-badge ${value}`}>
          {value === "active"
            ? "Active"
            : value === "inactive"
            ? "Inactive"
            : value === "remove"
            ? "Removed"
            : value}
        </span>
      );
    }

    // Format dates
    if (
      key.includes("date") ||
      key.includes("created") ||
      key.includes("updated")
    ) {
      return formatDate(value);
    }

    // Format boolean values
    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }

    // Format role
    if (key === "role") {
      return (
        <span className={`role-badge ${value}`}>
          {value === "admin"
            ? "Admin"
            : value === "approval"
            ? "Approval"
            : value === "user"
            ? "User"
            : value}
        </span>
      );
    }

    // Format manager_id to display manager name
    if (key === "manager_id" && value) {
      const manager = users.find(u => u.id === value);
      return manager ? manager.display_name || manager.username : value;
    }

    // Format company_id to display company name
    if (key === "company_id" && value) {
      try {
        const companyInfoStr = localStorage.getItem('companyInfo');
        if (companyInfoStr) {
          const companyInfo = JSON.parse(companyInfoStr);
          const company = Array.isArray(companyInfo)
            ? companyInfo.find(c => c.id === value)
            : (companyInfo.id === value ? companyInfo : null);

          return company ? company.company_name : value;
        }
      } catch (error) {
        console.error('Error parsing company info:', error);
      }
      return value;
    }

    // Default: return as is
    return value;
  };

  // Generate random password
  const generateRandomPassword = () => {
    const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+";
    let password = "";
    for (let i = 0; i < 12; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }
    return password;
  };

  // Apply random password to input
  const applyRandomPassword = () => {
    const password = generateRandomPassword();
    setSelectedUser(prev => ({
      ...prev,
      hashed_password: password // Still using hashed_password field in the UI, but will be sent as password to API
    }));
  };

  return (
    <div className="users-container">
      <div className="users-header">
        <div className="users-filters">
          <div className="filter-group">
            <label>Company:</label>
            <select
              name="company"
              value={filters.company}
              onChange={handleFilterChange}
            >
              <option value="all">All</option>
              {companies.map((company) => (
                <option key={company.id} value={company.id}>
                  {company.company_name}
                </option>
              ))}
            </select>
          </div>
          <div className="filter-group">
            <label>Role:</label>
            <select
              name="role"
              value={filters.role}
              onChange={handleFilterChange}
            >
              {roleOptions.map((role) => (
                <option key={role.id} value={role.id}>
                  {role.name}
                </option>
              ))}
            </select>
          </div>
          <div className="filter-group">
            <label>Status:</label>
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
            >
              {statusOptions.map((status) => (
                <option key={status.id} value={status.id}>
                  {status.name}
                </option>
              ))}
            </select>
          </div>
          <div className="filter-group">
            <label>Sort:</label>
            <select
              name="sort"
              value={filters.sort}
              onChange={handleFilterChange}
            >
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
            </select>
          </div>
          <div className="filter-group">
            <label>Show:</label>
            <select value={itemsPerPage} onChange={handleItemsPerPageChange}>
              <option value="5">5 items</option>
              <option value="10">10 items</option>
              <option value="20">20 items</option>
              <option value="50">50 items</option>
            </select>
          </div>
        </div>
        <div className="users-actions">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search users..."
              value={filters.search}
              onChange={handleSearch}
            />
            <FiSearch className="search-icon" />
          </div>
          <button
            className="btn-primary"
            onClick={() => openModal(null, "add")}
          >
            <FiUserPlus className="btn-icon-left" />
            Add User
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{error}</span>
        </div>
      )}

      {successMessage && (
        <div className="success-message">
          <FiUserCheck />
          <span>{successMessage}</span>
        </div>
      )}

      {isLoading ? (
        <div className="loading-spinner"></div>
      ) : (
        <div className="users-table-container">
          <table className="users-table">
            <thead>
              <tr>
                {displayColumns.map((column) => (
                  <th
                    key={column.key}
                    onClick={() => requestSort(column.key)}
                    className={sortConfig.key === column.key ? "sorted" : ""}
                  >
                    {column.label} {getSortIcon(column.key)}
                  </th>
                ))}
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentItems.map((user) => (
                <tr key={user.id}>
                  {displayColumns.map((column) => (
                    <td key={`${user.id}-${column.key}`}>
                      {formatCellValue(column.key, user[column.key])}
                    </td>
                  ))}
                  <td>
                    <div className="action-buttons">
                      {/* <button
                        className="btn-icon view"
                        onClick={() => openModal(user, "view")}
                        title="View"
                      >
                        <FiEye />
                      </button> */}
                      <button
                        className="btn-icon edit"
                        onClick={() => openModal(user, "edit")}
                        title="Edit"
                      >
                        <FiEdit />
                      </button>
                      {/* <button
                        className="btn-icon delete"
                        onClick={() => openModal(user, "delete")}
                        disabled={true} // tạm thời ko cho xoá user
                        title="Delete"
                      >
                        <FiTrash2 />
                      </button> */}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="pagination-container">
        <div className="pagination-info">
          Showing {indexOfFirstItem + 1} -{" "}
          {Math.min(indexOfLastItem, filteredUsers.length)} of{" "}
          {filteredUsers.length} users
        </div>
        <div className="pagination">
          <button
            className="pagination-btn"
            onClick={firstPage}
            disabled={currentPage === 1}
          >
            «
          </button>
          <button
            className="pagination-btn"
            onClick={prevPage}
            disabled={currentPage === 1}
          >
            ‹
          </button>
          {[...Array(Math.min(5, totalPages))].map((_, index) => {
            let pageNumber;
            if (totalPages <= 5) {
              pageNumber = index + 1;
            } else if (currentPage <= 3) {
              pageNumber = index + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNumber = totalPages - 4 + index;
            } else {
              pageNumber = currentPage - 2 + index;
            }

            return (
              <button
                key={pageNumber}
                className={`pagination-btn ${
                  currentPage === pageNumber ? "active" : ""
                }`}
                onClick={() => paginate(pageNumber)}
              >
                {pageNumber}
              </button>
            );
          })}
          <button
            className="pagination-btn"
            onClick={nextPage}
            disabled={currentPage === totalPages}
          >
            ›
          </button>
          <button
            className="pagination-btn"
            onClick={lastPage}
            disabled={currentPage === totalPages}
          >
            »
          </button>
        </div>
      </div>

      {isModalOpen && selectedUser && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-container" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                {modalMode === "add" && "Add New User"}
                {modalMode === "edit" &&
                  `Edit User: ${
                    selectedUser.display_name || selectedUser.username
                  }`}
                {modalMode === "delete" &&
                  `Delete User: ${
                    selectedUser.display_name || selectedUser.username
                  }`}
              </h3>
              <button className="modal-close" onClick={closeModal}>
                ×
              </button>
            </div>
            <div className="modal-body">
              {modalError && (
                <div className="modal-error-message">
                  <FiAlertCircle className="error-icon" />
                  <span>{modalError}</span>
                </div>
              )}
              {modalMode === "delete" ? (
                <div className="delete-confirmation">
                  <p>Are you sure you want to delete this user?</p>
                  <div className="modal-actions">
                    <button className="btn-secondary" onClick={closeModal}>
                      Cancel
                    </button>
                    <button className="btn-danger" onClick={handleDeleteUser}>
                      Delete
                    </button>
                  </div>
                </div>
              ) : (
                <div className="user-form">
                  {columns.map((column) => {
                    // Skip certain fields that shouldn't be editable
                    if (
                      column.key === "id" ||
                      column.key === "created_at" ||
                      column.key === "updated_at" ||
                      (column.key === "hashed_password" && modalMode === "edit") // Hide password field in edit mode
                    ) {
                      return null;
                    }

                    // Determine input type based on column key
                    let inputType = "text";
                    if (column.key === "email") inputType = "email";
                    if (column.key === "phone") inputType = "tel";
                    if (column.key === "password") inputType = "password";

                    // Special handling for status field
                    if (column.key === "status") {
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:<span className="required-field">*</span></label>
                          <select
                            name={column.key}
                            value={selectedUser[column.key] || ""}
                            onChange={handleUserChange}
                            required
                          >
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="remove">Removed</option>
                          </select>
                        </div>
                      );
                    }

                    // Special handling for role field
                    if (column.key === "role") {
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:<span className="required-field">*</span></label>
                          <select
                            name={column.key}
                            value={selectedUser[column.key] || "user"}
                            onChange={handleUserChange}
                            required
                          >
                            <option value="admin">Admin</option>
                            <option value="approval">Approval</option>
                            <option value="user">User</option>
                          </select>
                        </div>
                      );
                    }

                    // Special handling for manager_id field
                    if (column.key === "manager_id") {
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:<span className="required-field">*</span></label>
                          <div style={{ position: "relative" }} ref={managerSearchRef}>
                            <input
                              type="text"
                              value={selectedUser.managerSearch || ""}
                              onChange={(e) => {
                                const searchValue = e.target.value;
                                setSelectedUser(prev => ({
                                  ...prev,
                                  managerSearch: searchValue,
                                  showManagerDropdown: true
                                }));
                              }}
                              onFocus={() => {
                                setSelectedUser(prev => ({
                                  ...prev,
                                  showManagerDropdown: true
                                }));
                              }}
                              placeholder="Gõ để tìm người quản lý..."
                              style={{
                                // padding: "5px",
                                borderRadius: "4px",
                                border: "1px solid #ddd",
                                width: "100%",
                              }}
                            />
                            {selectedUser.showManagerDropdown && (
                              <div style={{
                                position: "absolute",
                                top: "100%",
                                left: 0,
                                right: 0,
                                maxHeight: "200px",
                                overflowY: "auto",
                                backgroundColor: "white",
                                border: "1px solid #ddd",
                                borderRadius: "4px",
                                zIndex: 10,
                                boxShadow: "0 4px 8px rgba(0,0,0,0.1)"
                              }}>
                                {managers
                                  .filter(manager => {
                                    if (!selectedUser.managerSearch) return true; // Show all when no search text
                                    const managerName = manager.display_name || manager.username || "";
                                    return managerName.toLowerCase().includes(selectedUser.managerSearch.toLowerCase());
                                  })
                                  .map(manager => (
                                    <div
                                      key={manager.id}
                                      onClick={() => {
                                        setSelectedUser(prev => ({
                                          ...prev,
                                          manager_id: manager.id,
                                          managerSearch: manager.display_name || manager.username,
                                          showManagerDropdown: false
                                        }));
                                      }}
                                      style={{
                                        padding: "8px 12px",
                                        cursor: "pointer",
                                        borderBottom: "1px solid #eee",
                                        backgroundColor: selectedUser.manager_id === manager.id ? "#f0f7ff" : "white"
                                      }}
                                      onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#f5f5f5"}
                                      onMouseOut={(e) => e.currentTarget.style.backgroundColor = selectedUser.manager_id === manager.id ? "#f0f7ff" : "white"}
                                    >
                                      {manager.display_name || manager.username}
                                    </div>
                                  ))
                                }
                                {managers.filter(manager => {
                                  if (!selectedUser.managerSearch) return true;
                                  const managerName = manager.display_name || manager.username || "";
                                  return managerName.toLowerCase().includes(selectedUser.managerSearch.toLowerCase());
                                }).length === 0 && (
                                  <div style={{ padding: "8px 12px", color: "#999" }}>
                                    Không tìm thấy người quản lý
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    }

                    // Special handling for company_id field
                    if (column.key === "company_id") {
                      return (
                        <div className="users-form-group" key={column.key}>
                          <label>{column.label}:<span className="required-field">*</span></label>
                          <div style={{ position: "relative" }} ref={companySearchRef}>
                            <input
                              type="text"
                              value={selectedUser.companySearch || ""}
                              onChange={(e) => {
                                const searchValue = e.target.value;
                                setSelectedUser(prev => ({
                                  ...prev,
                                  companySearch: searchValue,
                                  showCompanyDropdown: true
                                }));
                              }}
                              onFocus={() => {
                                setSelectedUser(prev => ({
                                  ...prev,
                                  showCompanyDropdown: true
                                }));
                              }}
                              placeholder="Gõ để tìm công ty..."
                              style={{
                                // padding: "5px",
                                borderRadius: "4px",
                                border: "1px solid #ddd",
                                width: "100%",
                              }}
                            />
                            {selectedUser.showCompanyDropdown && (
                              <div style={{
                                position: "absolute",
                                top: "100%",
                                left: 0,
                                right: 0,
                                maxHeight: "200px",
                                overflowY: "auto",
                                backgroundColor: "white",
                                border: "1px solid #ddd",
                                borderRadius: "4px",
                                zIndex: 10,
                                boxShadow: "0 4px 8px rgba(0,0,0,0.1)"
                              }}>
                                {companies
                                  .filter(company => {
                                    if (!selectedUser.companySearch) return true; // Show all when no search text
                                    const companyName = company.company_name || "";
                                    return companyName.toLowerCase().includes(selectedUser.companySearch.toLowerCase());
                                  })
                                  .map(company => (
                                    <div
                                      key={company.id}
                                      onClick={() => {
                                        setSelectedUser(prev => ({
                                          ...prev,
                                          company_id: company.id,
                                          companySearch: company.company_name,
                                          showCompanyDropdown: false
                                        }));
                                      }}
                                      style={{
                                        padding: "8px 12px",
                                        cursor: "pointer",
                                        borderBottom: "1px solid #eee",
                                        backgroundColor: selectedUser.company_id === company.id ? "#f0f7ff" : "white"
                                      }}
                                      onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#f5f5f5"}
                                      onMouseOut={(e) => e.currentTarget.style.backgroundColor = selectedUser.company_id === company.id ? "#f0f7ff" : "white"}
                                    >
                                      {company.company_name}
                                    </div>
                                  ))
                                }
                                {companies.filter(company => {
                                  if (!selectedUser.companySearch) return true;
                                  const companyName = company.company_name || "";
                                  return companyName.toLowerCase().includes(selectedUser.companySearch.toLowerCase());
                                }).length === 0 && (
                                  <div style={{ padding: "8px 12px", color: "#999" }}>
                                    Không tìm thấy công ty
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    }

                    // Default input field
                    return (
                      <div className="users-form-group" key={column.key}>
                        <label>
                          {column.key === "hashed_password" ? "Password" : column.label}:
                          {(column.key === "username" || column.key === "display_name" ||
                            (column.key === "hashed_password" && modalMode === "add")) &&
                            <span className="required-field">*</span>}
                        </label>
                        {column.key === "hashed_password" ? (
                          <div className="password-input-container">
                            <input
                              type="text"
                              name={column.key}
                              value={selectedUser[column.key] || ""}
                              onChange={handleUserChange}
                              required={modalMode === "add"}
                              aria-required={modalMode === "add"}
                              disabled={modalMode === "edit"} // Disable password field in edit mode
                              className={modalMode === "edit" ? "disabled-field" : ""}
                            />
                            <button
                              type="button"
                              className="password-suggestion-btn"
                              onClick={applyRandomPassword}
                              title="Generate random 12-character password"
                              disabled={(selectedUser[column.key] && selectedUser[column.key] !== "") || modalMode === "edit"}
                            >
                              Suggest
                            </button>
                            {/* <button
                              type="button"
                              className="password-toggle-btn"
                              onClick={() => setShowPassword(!showPassword)}
                              title={showPassword ? "Hide password" : "Show password"}
                            >
                              {showPassword ? "Hide" : "Show"}
                            </button> */}
                          </div>
                        ) : (
                          <input
                            type={inputType}
                            name={column.key}
                            value={selectedUser[column.key] || ""}
                            onChange={handleUserChange}
                            required={["username", "display_name"].includes(column.key)}
                            aria-required={["username", "display_name"].includes(column.key)}
                            disabled={column.key === "username" && modalMode === "edit"}
                            className={column.key === "username" && modalMode === "edit" ? "disabled-field" : ""}
                            ref={column.key === "username" ? usernameInputRef : null}
                          />
                        )}
                      </div>
                    );
                  })}
                  <div className="modal-actions">
                    <button className="btn-secondary" onClick={closeModal}>
                      Cancel
                    </button>
                    <button className="btn-primary" onClick={handleSaveUser}>
                      {modalMode === "add" ? "Add" : "Save Changes"}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Users;
