<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            width: 95%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px 0;
        }
        
        /* Header Styles */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .print-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .print-btn:hover {
            background-color: #2980b9;
        }
        
        .print-btn i {
            margin-right: 8px;
        }
        
        /* Ticket Header */
        .ticket-header {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .ticket-header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .ticket-title {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .ticket-meta {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .ticket-company {
            font-size: 18px;
            font-weight: 600;
            color: #34495e;
            margin-right: 20px;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-approved {
            background-color: #d4edff;
            color: #1a73e8;
        }
        
        .ticket-date {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
        }
        
        .action-btn {
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }
        
        .action-btn i {
            margin-right: 8px;
        }
        
        .approve-btn {
            background-color: #e6f7ee;
            color: #28a745;
        }
        
        .edit-btn {
            background-color: #e7f1ff;
            color: #1a73e8;
        }
        
        .delete-btn {
            background-color: #fce8e6;
            color: #d93025;
        }
        
        /* Main Grid Layout */
        .main-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        @media (min-width: 1024px) {
            .main-grid {
                grid-template-columns: 2fr 1fr;
            }
        }
        
        /* Card Styles */
        .card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
        }
        
        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-title i {
            margin-right: 10px;
            color: #3498db;
        }
        
        .card-edit-btn {
            background: none;
            border: none;
            color: #3498db;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .card-edit-btn i {
            margin-right: 5px;
        }
        
        .card-save-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            display: none;
            align-items: center;
        }
        
        .card-save-btn i {
            margin-right: 5px;
        }
        
        .card-cancel-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            display: none;
            align-items: center;
            margin-right: 10px;
        }
        
        .card-cancel-btn i {
            margin-right: 5px;
        }
        
        /* Info Grid */
        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }
        
        @media (min-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .info-item p:first-child {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .info-item p:last-child {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .info-item input, .info-item textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            display: none;
        }
        
        .info-item textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        /* Table Styles */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        th {
            background-color: #f5f5f5;
            color: #555;
            font-weight: 600;
            font-size: 14px;
        }
        
        tr:hover {
            background-color: #f9f9f9;
        }
        
        .table-total {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        
        /* Notes Section */
        .notes-box {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            color: #555;
        }
        
        .notes-edit {
            display: none;
            width: 100%;
        }
        
        /* Staff Section */
        .staff-item {
            margin-bottom: 15px;
        }
        
        .staff-item p:first-child {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .staff-name {
            display: flex;
            align-items: center;
            font-weight: 500;
        }
        
        .staff-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        .staff-select {
            display: none;
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        /* Timeline Styles */
        .timeline-item {
            position: relative;
            padding-left: 40px;
            padding-bottom: 25px;
        }
        
        .timeline-item:not(:last-child)::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 25px;
            height: 100%;
            width: 2px;
            background-color: #e0e0e0;
        }
        
        .timeline-dot {
            position: absolute;
            left: 0;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            color: #555;
        }
        
        .timeline-dot.completed {
            background-color: #e6f7ee;
            color: #28a745;
        }
        
        .timeline-dot.delivered {
            background-color: #e7f1ff;
            color: #1a73e8;
        }
        
        .timeline-dot.initial {
            background-color: #f5f5f5;
            color: #555;
        }
        
        .timeline-content p:first-child {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .timeline-content p:not(:first-child) {
            color: #7f8c8d;
            font-size: 13px;
        }
        
        /* Activities Section */
        .activity-item {
            border-left: 4px solid #3498db;
            padding-left: 15px;
            padding-top: 10px;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .activity-item.payment {
            border-left-color: #28a745;
        }
        
        .activity-item p:first-child {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .activity-item p:not(:first-child) {
            color: #7f8c8d;
            font-size: 13px;
        }
        
        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .ticket-header-top {
                flex-direction: column;
                gap: 15px;
            }
            
            .action-buttons {
                width: 100%;
                flex-wrap: wrap;
            }
            
            .action-btn {
                flex-grow: 1;
                justify-content: center;
            }
            
            table {
                display: block;
                overflow-x: auto;
            }
        }
        
        /* Edit mode styles */
        .edit-mode .card-save-btn,
        .edit-mode .card-cancel-btn {
            display: flex;
        }
        
        .edit-mode .card-edit-btn {
            display: none;
        }
        
        .edit-mode .info-item input,
        .edit-mode .info-item textarea,
        .edit-mode .staff-select,
        .edit-mode .notes-edit {
            display: block;
        }
        
        .edit-mode .info-item p:last-child,
        .edit-mode .staff-name,
        .edit-mode .notes-box {
            display: none;
        }

        /* Row action buttons */
        .row-actions {
            display: flex;
            gap: 5px;
        }

        .row-btn {
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            border: none;
            display: flex;
            align-items: center;
        }

        .edit-row-btn {
            background-color: #e7f1ff;
            color: #1a73e8;
        }

        .delete-row-btn {
            background-color: #fce8e6;
            color: #d93025;
        }

        .add-row-btn {
            background-color: #e6f7ee;
            color: #28a745;
            margin-bottom: 15px;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: 500;
            display: none;
        }

        .row-btn i {
            margin-right: 5px;
        }

        /* Editable row styles */
        .editable-row td {
            position: relative;
        }

        .editable-row input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .editable-row .row-actions {
            justify-content: flex-end;
        }

        .editable-row .save-row-btn {
            background-color: #28a745;
            color: white;
        }

        .editable-row .cancel-row-btn {
            background-color: #6c757d;
            color: white;
        }

        /* Hide actions column by default */
        th.actions-header,
        td.actions-cell {
            display: none;
        }

        /* Show actions column in edit mode */
        .edit-mode th.actions-header,
        .edit-mode td.actions-cell {
            display: table-cell;
        }

        /* Show row actions only in edit mode */
        .edit-mode .row-actions {
            display: flex;
        }

        .edit-mode .add-row-btn {
            display: flex;
        }

        .row-actions {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div>
                <h1>Ticket Management</h1>
                <p>View and manage ticket details</p>
            </div>
            <button class="print-btn">
                <i class="fas fa-print"></i> Print Ticket
            </button>
        </div>

        <!-- Ticket Header -->
        <div class="ticket-header">
            <div class="ticket-header-top">
                <div>
                    <div class="ticket-meta">
                        <span class="ticket-company">Company: ABC Logistics</span>
                        <span class="status-badge status-approved">Approved</span>
                    </div>
                    <h2 class="ticket-title">Ticket #TKT-2023-00142</h2>
                    <p class="ticket-date">Created: 15 Oct 2023, 09:30 AM</p>
                </div>
                <div class="action-buttons">
                    <button class="action-btn approve-btn">
                        <i class="fas fa-check-circle"></i> Approve
                    </button>
                    <button class="action-btn edit-btn">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="action-btn delete-btn">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="main-grid">
            <!-- Left Column -->
            <div class="left-column">
                <!-- Basic Information -->
                <div class="card" id="basic-info-card">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i> Basic Information
                        <div>
                            <button class="card-cancel-btn">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                            <button class="card-save-btn">
                                <i class="fas fa-save"></i> Save
                            </button>
                            <button class="card-edit-btn">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </div>
                    </h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <p>Request Date</p>
                            <p>15 Oct 2023, 09:30 AM</p>
                            <input type="text" value="15 Oct 2023, 09:30 AM">
                        </div>
                        <div class="info-item">
                            <p>Customer</p>
                            <p>John Smith</p>
                            <input type="text" value="John Smith">
                        </div>
                        <div class="info-item">
                            <p>Phone</p>
                            <p>+****************</p>
                            <input type="text" value="+****************">
                        </div>
                        <div class="info-item">
                            <p>Pickup Address</p>
                            <p>123 Main St, New York, NY 10001</p>
            <input type="text" value="123 Main St, New York, NY 10001">
        </div>
        <div class="info-item" style="grid-column: 1 / -1;">
            <p>Delivery Address</p>
            <p>456 Broadway, Boston, MA 02108</p>
            <input type="text" value="456 Broadway, Boston, MA 02108">
        </div>
    </div>
</div>

<!-- Vehicle Information -->
<div class="card" id="vehicle-info-card">
    <h3 class="card-title">
        <i class="fas fa-truck"></i> Vehicle Information
        <div>
            <button class="card-cancel-btn">
                <i class="fas fa-times"></i> Cancel
            </button>
            <button class="card-save-btn">
                <i class="fas fa-save"></i> Save
            </button>
            <button class="card-edit-btn">
                <i class="fas fa-edit"></i> Edit
            </button>
        </div>
    </h3>
    <table>
        <thead>
            <tr>
                <th>License Plate</th>
                <th>Owner</th>
                <th>Phone</th>
                <th class="actions-header">Actions</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>ABC123</td>
                <td>Michael Johnson</td>
                <td>+****************</td>
                <td class="actions-cell">
                    <div class="row-actions">
                        <button class="row-btn edit-row-btn">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="row-btn delete-row-btn">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </td>
            </tr>
            <tr>
                <td>XYZ789</td>
                <td>Sarah Williams</td>
                <td>+****************</td>
                <td class="actions-cell">
                    <div class="row-actions">
                        <button class="row-btn edit-row-btn">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="row-btn delete-row-btn">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <button class="add-row-btn">
        <i class="fas fa-plus"></i> Add Vehicle
    </button>
    <div class="info-item">
        <p>Vehicle Quantity</p>
        <p>2</p>
        <input type="text" value="2">
    </div>
    <div class="info-item">
        <p>Notes</p>
        <p>Special handling required for fragile items</p>
        <input type="text" value="Special handling required for fragile items">
    </div>
</div>

<!-- Pricing Information -->
<div class="card" id="pricing-info-card">
    <h3 class="card-title">
        <i class="fas fa-dollar-sign"></i> Pricing Information
        <div>
            <button class="card-cancel-btn">
                <i class="fas fa-times"></i> Cancel
            </button>
            <button class="card-save-btn">
                <i class="fas fa-save"></i> Save
            </button>
            <button class="card-edit-btn">
                <i class="fas fa-edit"></i> Edit
            </button>
        </div>
    </h3>
    
    <div style="margin-bottom: 30px;">
        <h4 style="font-size: 18px; font-weight: 500; color: #2c3e50; margin-bottom: 15px;">Purchase Price</h4>
        <button class="add-row-btn add-purchase-row">
            <i class="fas fa-plus"></i> Add Purchase Item
        </button>
        <table class="purchase-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Amount (Before Tax)</th>
                    <th>Tax Rate</th>
                    <th>Amount (After Tax)</th>
                    <th class="actions-header">Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Transportation Fee</td>
                    <td>$500.00</td>
                    <td>10%</td>
                    <td>$550.00</td>
                    <td class="actions-cell">
                        <div class="row-actions">
                            <button class="row-btn edit-row-btn">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="row-btn delete-row-btn">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Handling Fee</td>
                    <td>$75.00</td>
                    <td>10%</td>
                    <td>$82.50</td>
                    <td class="actions-cell">
                        <div class="row-actions">
                            <button class="row-btn edit-row-btn">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="row-btn delete-row-btn">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="table-total">
                    <td>Total</td>
                    <td>$575.00</td>
                    <td></td>
                    <td>$632.50</td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div>
        <h4 style="font-size: 18px; font-weight: 500; color: #2c3e50; margin-bottom: 15px;">Selling Price</h4>
        <button class="add-row-btn add-selling-row">
            <i class="fas fa-plus"></i> Add Selling Item
        </button>
        <table class="selling-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Amount (Before Tax)</th>
                    <th>Tax Rate</th>
                    <th>Amount (After Tax)</th>
                    <th class="actions-header">Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Transportation Fee</td>
                    <td>$750.00</td>
                    <td>10%</td>
                    <td>$825.00</td>
                    <td class="actions-cell">
                        <div class="row-actions">
                            <button class="row-btn edit-row-btn">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="row-btn delete-row-btn">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Handling Fee</td>
                    <td>$100.00</td>
                    <td>10%</td>
                    <td>$110.00</td>
                    <td class="actions-cell">
                        <div class="row-actions">
                            <button class="row-btn edit-row-btn">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="row-btn delete-row-btn">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="table-total">
                    <td>Total</td>
                    <td>$850.00</td>
                    <td></td>
                    <td>$935.00</td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Additional Notes -->
<div class="card" id="notes-card">
    <h3 class="card-title">
        <i class="fas fa-sticky-note"></i> Additional Notes
        <div>
            <button class="card-cancel-btn">
                <i class="fas fa-times"></i> Cancel
            </button>
            <button class="card-save-btn">
                <i class="fas fa-save"></i> Save
            </button>
            <button class="card-edit-btn">
                <i class="fas fa-edit"></i> Edit
            </button>
        </div>
    </h3>
    <div class="notes-box">
        <p>Customer requested delivery between 9 AM - 5 PM on weekdays only. Special care needed for antique furniture pieces included in shipment.</p>
    </div>
    <textarea class="notes-edit">Customer requested delivery between 9 AM - 5 PM on weekdays only. Special care needed for antique furniture pieces included in shipment.</textarea>
</div>
            </div>

            <!-- Right Column -->
            <div class="right-column">
                <!-- Responsible Staff -->
                <div class="card" id="staff-card">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i> Responsible Staff
                        <div>
                            <button class="card-cancel-btn">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                            <button class="card-save-btn">
                                <i class="fas fa-save"></i> Save
                            </button>
                            <button class="card-edit-btn">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </div>
                    </h3>
                    <div>
                        <div class="staff-item">
                            <p>Pricing Staff</p>
                            <p class="staff-name">
                                <img src="https://randomuser.me/api/portraits/women/44.jpg" class="staff-avatar">
                                Lisa Wong
                            </p>
                            <select class="staff-select">
                                <option value="Lisa Wong">Lisa Wong</option>
                                <option value="John Doe">John Doe</option>
                                <option value="Jane Smith">Jane Smith</option>
                            </select>
                        </div>
                        <div class="staff-item">
                            <p>Dispatch Staff</p>
                            <p class="staff-name">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" class="staff-avatar">
                                Robert Chen
                            </p>
                            <select class="staff-select">
                                <option value="Robert Chen">Robert Chen</option>
                                <option value="Mike Johnson">Mike Johnson</option>
                                <option value="Sarah Williams">Sarah Williams</option>
                            </select>
                        </div>
                        <div class="staff-item">
                            <p>Sales Staff</p>
                            <p class="staff-name">
                                <img src="https://randomuser.me/api/portraits/women/68.jpg" class="staff-avatar">
                                Maria Garcia
                            </p>
                            <select class="staff-select">
                                <option value="Maria Garcia">Maria Garcia</option>
                                <option value="David Brown">David Brown</option>
                                <option value="Emily Davis">Emily Davis</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Status Timeline -->
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i> Status Timeline
                    </h3>
                    <div>
                        <div class="timeline-item">
                            <div class="timeline-dot completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Completed</p>
                                <p>18 Oct 2023, 11:20 AM</p>
                                <p>Changed by: Admin User</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Customer Paid</p>
                                <p>17 Oct 2023, 03:45 PM</p>
                                <p>Changed by: Maria Garcia</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Supplier Paid</p>
                                <p>17 Oct 2023, 10:15 AM</p>
                                <p>Changed by: Lisa Wong</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot delivered">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Delivered</p>
                                <p>16 Oct 2023, 02:30 PM</p>
                                <p>Changed by: Robert Chen</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot delivered">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Approved</p>
                                <p>15 Oct 2023, 11:15 AM</p>
                                <p>Changed by: Admin User</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot initial">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Initial</p>
                                <p>15 Oct 2023, 09:30 AM</p>
                                <p>Created by: Maria Garcia</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-bell"></i> Recent Activities
                    </h3>
                    <div>
                        <div class="activity-item">
                            <p>Status changed to Completed</p>
                            <p>18 Oct 2023, 11:20 AM</p>
                            <p>By: Admin User</p>
                        </div>
                        <div class="activity-item payment">
                            <p>Payment received from customer</p>
                            <p>17 Oct 2023, 03:45 PM</p>
                            <p>By: Maria Garcia</p>
                        </div>
                        <div class="activity-item payment">
                            <p>Payment sent to supplier</p>
                            <p>17 Oct 2023, 10:15 AM</p>
                            <p>By: Lisa Wong</p>
                        </div>
                        <div class="activity-item">
                            <p>Delivery confirmation added</p>
                            <p>16 Oct 2023, 02:30 PM</p>
                            <p>By: Robert Chen</p>
                        </div>
                        <div class="activity-item">
                            <p>Ticket approved</p>
                            <p>15 Oct 2023, 11:15 AM</p>
                            <p>By: Admin User</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Print button functionality
            const printBtn = document.querySelector('.print-btn');
            printBtn.addEventListener('click', function() {
                window.print();
            });
            
            // Action buttons functionality
            const actionButtons = document.querySelectorAll('.action-btn');
            actionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    alert(`Action "${action}" would be performed here`);
                });
            });
            
            // Add hover effect to table rows
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f0f8ff';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });
            
            // Edit functionality for each card
            const cards = document.querySelectorAll('.card');
            
            cards.forEach(card => {
                const editBtn = card.querySelector('.card-edit-btn');
                const saveBtn = card.querySelector('.card-save-btn');
                const cancelBtn = card.querySelector('.card-cancel-btn');
                
                if (editBtn && saveBtn && cancelBtn) {
                    editBtn.addEventListener('click', function() {
                        card.classList.add('edit-mode');
                        
                        // Show all row actions in this card
                        const rowActions = card.querySelectorAll('.row-actions');
                        rowActions.forEach(action => {
                            action.style.display = 'flex';
                        });
                        
                        // Show add row buttons in this card
                        const addRowBtns = card.querySelectorAll('.add-row-btn');
                        addRowBtns.forEach(btn => {
                            btn.style.display = 'flex';
                        });
                    });
                    
                    saveBtn.addEventListener('click', function() {
                        // Save the changes
                        if (card.id === 'basic-info-card') {
                            const inputs = card.querySelectorAll('input');
                            const paragraphs = card.querySelectorAll('.info-item p:last-child');
                            
                            inputs.forEach((input, index) => {
                                paragraphs[index].textContent = input.value;
                            });
                        } 
                        else if (card.id === 'vehicle-info-card') {
                            const inputs = card.querySelectorAll('input');
                            const paragraphs = card.querySelectorAll('.info-item p:last-child');
                            
                            inputs.forEach((input, index) => {
                                paragraphs[index].textContent = input.value;
                            });
                        } 
                        else if (card.id === 'notes-card') {
                            const textarea = card.querySelector('.notes-edit');
                            const noteContent = card.querySelector('.notes-box p');
                            noteContent.textContent = textarea.value;
                        } 
                        else if (card.id === 'staff-card') {
                            const selects = card.querySelectorAll('.staff-select');
                            const staffNames = card.querySelectorAll('.staff-name');
                            
                            selects.forEach((select, index) => {
                                const selectedOption = select.options[select.selectedIndex].text;
                                staffNames[index].textContent = selectedOption;
                                
                                // Update avatar based on selection (simplified example)
                                const avatar = staffNames[index].querySelector('.staff-avatar');
                                if (selectedOption.includes('Lisa')) {
                                    avatar.src = "https://randomuser.me/api/portraits/women/44.jpg";
                                } else if (selectedOption.includes('Robert')) {
                                    avatar.src = "https://randomuser.me/api/portraits/men/32.jpg";
                                } else if (selectedOption.includes('Maria')) {
                                    avatar.src = "https://randomuser.me/api/portraits/women/68.jpg";
                                } else {
                                    // Default avatar
                                    avatar.src = "https://randomuser.me/api/portraits/women/1.jpg";
                                }
                            });
                        }
                        
                        card.classList.remove('edit-mode');
                        
                        // Hide all row actions in this card
                        const rowActions = card.querySelectorAll('.row-actions');
                        rowActions.forEach(action => {
                            action.style.display = 'none';
                        });
                        
                        // Hide add row buttons in this card
                        const addRowBtns = card.querySelectorAll('.add-row-btn');
                        addRowBtns.forEach(btn => {
                            btn.style.display = 'none';
                        });
                        
                        alert('Changes saved successfully!');
                    });
                    
                    cancelBtn.addEventListener('click', function() {
                        // Revert changes
                        if (card.id === 'basic-info-card') {
                            const inputs = card.querySelectorAll('input');
                            const paragraphs = card.querySelectorAll('.info-item p:last-child');
                            
                            inputs.forEach((input, index) => {
                                input.value = paragraphs[index].textContent;
                            });
                        } 
                        else if (card.id === 'vehicle-info-card') {
                            const inputs = card.querySelectorAll('input');
                            const paragraphs = card.querySelectorAll('.info-item p:last-child');
                            
                            inputs.forEach((input, index) => {
                                input.value = paragraphs[index].textContent;
                            });
                        } 
                        else if (card.id === 'notes-card') {
                            const textarea = card.querySelector('.notes-edit');
                            const noteContent = card.querySelector('.notes-box p');
                            textarea.value = noteContent.textContent;
                        } 
                        else if (card.id === 'staff-card') {
                            const selects = card.querySelectorAll('.staff-select');
                            const staffNames = card.querySelectorAll('.staff-name');
                            
                            selects.forEach((select, index) => {
                                select.value = staffNames[index].textContent.trim();
                            });
                        }
                        
                        card.classList.remove('edit-mode');
                        
                        // Hide all row actions in this card
                        const rowActions = card.querySelectorAll('.row-actions');
                        rowActions.forEach(action => {
                            action.style.display = 'none';
                        });
                        
                        // Hide add row buttons in this card
                        const addRowBtns = card.querySelectorAll('.add-row-btn');
                        addRowBtns.forEach(btn => {
                            btn.style.display = 'none';
                        });
                    });
                }
            });

            // Row editing functionality for pricing tables
            function setupRowEditing(tableClass) {
                const table = document.querySelector(tableClass);
                if (!table) return;

                // Edit row
                table.addEventListener('click', function(e) {
                    if (e.target.closest('.edit-row-btn')) {
                        const row = e.target.closest('tr');
                        if (row.classList.contains('table-total')) return;
                        
                        const cells = row.querySelectorAll('td:not(:last-child)');
                        const originalValues = [];
                        
                        cells.forEach((cell, index) => {
                            originalValues.push(cell.textContent);
                            const input = document.createElement('input');
                            input.value = cell.textContent;
                            cell.innerHTML = '';
                            cell.appendChild(input);
                        });
                        
                        // Replace action buttons with save/cancel
                        const actionsCell = row.querySelector('td:last-child');
                        actionsCell.innerHTML = `
                            <div class="row-actions">
                                <button class="row-btn save-row-btn">
                                    <i class="fas fa-save"></i> Save
                                </button>
                                <button class="row-btn cancel-row-btn">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                            </div>
                        `;
                        
                        row.classList.add('editable-row');
                        
                        // Save button handler
                        actionsCell.querySelector('.save-row-btn').addEventListener('click', function() {
                            cells.forEach((cell, index) => {
                                const input = cell.querySelector('input');
                                cell.textContent = input.value;
                            });
                            
                            // Restore action buttons
                            actionsCell.innerHTML = `
                                <div class="row-actions">
                                    <button class="row-btn edit-row-btn">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="row-btn delete-row-btn">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            `;
                            
                            row.classList.remove('editable-row');
                            updateTotals(tableClass);
                        });
                        
                        // Cancel button handler
                        actionsCell.querySelector('.cancel-row-btn').addEventListener('click', function() {
                            cells.forEach((cell, index) => {
                                cell.textContent = originalValues[index];
                            });
                            
                            // Restore action buttons
                            actionsCell.innerHTML = `
                                <div class="row-actions">
                                    <button class="row-btn edit-row-btn">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="row-btn delete-row-btn">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            `;
                            
                            row.classList.remove('editable-row');
                        });
                    }
                    
                    // Delete row
                    if (e.target.closest('.delete-row-btn')) {
                        const row = e.target.closest('tr');
                        if (row.classList.contains('table-total')) return;
                        
                        if (confirm('Are you sure you want to delete this row?')) {
                            row.remove();
                            updateTotals(tableClass);
                        }
                    }
                });
            }

            // Add new row functionality
            function setupAddRow(buttonClass, tableClass) {
                const addButton = document.querySelector(buttonClass);
                if (!addButton) return;
                
                addButton.addEventListener('click', function() {
                    const table = document.querySelector(tableClass);
                    const tbody = table.querySelector('tbody');
                    const rows = tbody.querySelectorAll('tr');
                    const lastRow = rows[rows.length - 2]; // Get the row before the total row
                    
                    // Create new row
                    const newRow = document.createElement('tr');
                    newRow.innerHTML = `
                        <td>New Item</td>
                        <td>$0.00</td>
                        <td>0%</td>
                        <td>$0.00</td>
                        <td class="actions-cell">
                            <div class="row-actions">
                                <button class="row-btn edit-row-btn">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="row-btn delete-row-btn">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </td>
                    `;
                    
                    // Insert before the total row
                    tbody.insertBefore(newRow, tbody.querySelector('.table-total'));
                    
                    // Trigger edit mode for the new row
                    newRow.querySelector('.edit-row-btn').click();
                    
                    // Focus on the first input
                    setTimeout(() => {
                        newRow.querySelector('td:first-child input').focus();
                    }, 0);
                });
            }

            // Update totals for a table
            function updateTotals(tableClass) {
                const table = document.querySelector(tableClass);
                if (!table) return;
                
                const rows = table.querySelectorAll('tbody tr:not(.table-total)');
                let beforeTaxTotal = 0;
                let afterTaxTotal = 0;
                
                rows.forEach(row => {
                    if (row.classList.contains('editable-row')) return;
                    
                    const beforeTaxCell = row.querySelector('td:nth-child(2)');
                    const afterTaxCell = row.querySelector('td:nth-child(4)');
                    
                    const beforeTax = parseFloat(beforeTaxCell.textContent.replace(/[^0-9.-]+/g,"")) || 0;
                    const afterTax = parseFloat(afterTaxCell.textContent.replace(/[^0-9.-]+/g,"")) || 0;
                    
                    beforeTaxTotal += beforeTax;
                    afterTaxTotal += afterTax;
                });
                
                const totalRow = table.querySelector('.table-total');
                if (totalRow) {
                    totalRow.querySelector('td:nth-child(2)').textContent = `$${beforeTaxTotal.toFixed(2)}`;
                    totalRow.querySelector('td:nth-child(4)').textContent = `$${afterTaxTotal.toFixed(2)}`;
                }
            }

            // Initialize row editing for all tables
            setupRowEditing('.purchase-table');
            setupRowEditing('.selling-table');
            
            // Initialize add row buttons
            setupAddRow('.add-purchase-row', '.purchase-table');
            setupAddRow('.add-selling-row', '.selling-table');
            
            // Also setup for vehicle table if needed
            setupRowEditing('#vehicle-info-card table');
        });
    </script>
</body>
</html>