<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bump Diary</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .camera-preview {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 16px;
            display: none;
        }
        
        .timeline-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ec4899;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }
        
        .tab-active {
            color: #ec4899;
            border-bottom: 3px solid #ec4899;
        }
        
        .ai-loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ec4899;
            margin: 0 4px;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen pb-20 font-sans">
    <!-- Header -->
    <header class="bg-white p-4 shadow-sm sticky top-0 z-10">
        <div class="flex justify-between items-center">
            <h1 class="text-xl font-bold text-pink-500">Bump Diary</h1>
            <button class="p-2 rounded-full text-gray-600">
                <i class="fas fa-ellipsis-vertical"></i>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-4">
        <!-- Navigation Tabs -->
        <div class="flex justify-around border-b mb-4">
            <button class="tab-active px-4 py-2 font-medium text-sm" data-tab="home">Today</button>
            <button class="px-4 py-2 font-medium text-sm text-gray-600" data-tab="timeline">Timeline</button>
            <button class="px-4 py-2 font-medium text-sm text-gray-600" data-tab="milestones">Milestones</button>
        </div>
        
        <!-- Home Tab -->
        <div id="home" class="tab-content">
            <!-- Pregnancy Progress -->
            <section class="mb-6 bg-white p-4 rounded-xl shadow-sm">
                <div class="flex justify-between items-center mb-2">
                    <h2 class="text-md font-semibold text-gray-800">Week 12 of 40</h2>
                    <span class="text-xs font-medium text-pink-500 bg-pink-50 px-2 py-1 rounded-full">30%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div class="bg-pink-500 h-2 rounded-full" style="width: 30%"></div>
                </div>
                <p class="text-xs text-gray-500">Due date: June 15, 2024</p>
            </section>

            <!-- Baby Info -->
            <section class="mb-6 bg-white p-4 rounded-xl shadow-sm text-center">
                <div class="w-24 h-24 bg-pink-100 rounded-full mx-auto mb-3 flex items-center justify-center">
                    <i class="fas fa-baby text-pink-500 text-3xl"></i>
                </div>
                <h3 class="text-md font-semibold text-gray-800">Size of a plum</h3>
                <p class="text-xs text-gray-500 mt-1">About 5cm long</p>
            </section>

            <!-- Camera Section -->
            <section class="mb-6">
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <h2 class="text-md font-semibold text-gray-800 mb-3">Today's Bump</h2>
                    <div class="relative mb-3">
                        <video id="cameraView" class="camera-preview" autoplay></video>
                        <canvas id="photoCanvas" class="camera-preview"></canvas>
                        <img id="photoResult" class="camera-preview" src="" alt="Your photo">
                        <div id="cameraPlaceholder" class="w-full h-48 bg-gray-100 rounded-xl flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-camera text-2xl text-gray-400 mb-2"></i>
                                <p class="text-xs text-gray-500">Tap to add today's bump photo</p>
                            </div>
                        </div>
                    </div>
                    <button id="takePhotoBtn" class="w-full bg-pink-500 hover:bg-pink-600 text-white py-2 rounded-lg text-sm font-medium">
                        Take Photo
                    </button>
                    <div id="photoActions" class="hidden mt-2 flex justify-between">
                        <button id="retakePhotoBtn" class="w-1/2 mr-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 rounded-lg text-sm font-medium">
                            Retake
                        </button>
                        <button id="uploadPhotoBtn" class="w-1/2 ml-1 bg-pink-500 hover:bg-pink-600 text-white py-2 rounded-lg text-sm font-medium">
                            Use Photo
                        </button>
                    </div>
                </div>
            </section>

            <!-- Journal Entry -->
            <section class="mb-6 bg-white p-4 rounded-xl shadow-sm">
                <h2 class="text-md font-semibold text-gray-800 mb-3">How are you feeling?</h2>
                <div class="mb-3">
                    <input type="text" id="entryTitle" placeholder="Title (optional)" class="w-full p-3 border border-gray-200 rounded-lg text-sm focus:border-pink-300 focus:ring-1 focus:ring-pink-200">
                </div>
                <div class="mb-3">
                    <textarea id="entryContent" rows="4" placeholder="Write about your day..." class="w-full p-3 border border-gray-200 rounded-lg text-sm focus:border-pink-300 focus:ring-1 focus:ring-pink-200"></textarea>
                </div>
                <div class="flex justify-between">
                    <button id="aiHelpBtn" class="bg-white border border-gray-200 hover:bg-gray-50 text-gray-700 px-3 py-2 rounded-lg text-sm font-medium flex items-center">
                        <span id="aiIcon" class="mr-1"><i class="fas fa-magic text-pink-500"></i></span> AI Help
                    </button>
                    <button id="saveEntryBtn" class="bg-pink-500 hover:bg-pink-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        Save
                    </button>
                </div>
            </section>
        </div>
        
        <!-- Timeline Tab -->
        <div id="timeline" class="tab-content hidden">
            <h2 class="text-md font-semibold text-gray-800 mb-4">Your Pregnancy Timeline</h2>
            <div id="timelineContainer" class="space-y-4">
                <!-- Sample timeline items -->
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <div class="flex items-start">
                        <div class="timeline-dot mr-3 mt-1">
                            <i class="fas fa-heart text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-center mb-1">
                                <h3 class="text-sm font-medium text-gray-800">First Ultrasound</h3>
                                <span class="text-xs text-gray-500">Week 8</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-2">Saw the baby's heartbeat for the first time!</p>
                            <div class="w-full h-32 bg-gray-100 rounded-lg"></div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-xl shadow-sm">
                    <div class="flex items-start">
                        <div class="timeline-dot mr-3 mt-1">
                            <i class="fas fa-baby text-xs"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-center mb-1">
                                <h3 class="text-sm font-medium text-gray-800">First Movements</h3>
                                <span class="text-xs text-gray-500">Week 18</span>
                            </div>
                            <p class="text-xs text-gray-600">Felt the baby move for the first time today!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Milestones Tab -->
        <div id="milestones" class="tab-content hidden">
            <h2 class="text-md font-semibold text-gray-800 mb-4">Pregnancy Milestones</h2>
            <div id="milestonesContainer" class="grid grid-cols-2 gap-3">
                <!-- Sample milestones -->
                <div class="bg-white p-3 rounded-xl shadow-sm">
                    <div class="w-10 h-10 bg-pink-100 rounded-full mb-2 flex items-center justify-center">
                        <i class="fas fa-heartbeat text-pink-500"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800 mb-1">First Trimester</h3>
                    <p class="text-xs text-gray-500 mb-2">Weeks 1-12</p>
                    <span class="text-xs text-pink-500 bg-pink-50 px-2 py-1 rounded-full">Completed</span>
                </div>
                
                <div class="bg-white p-3 rounded-xl shadow-sm">
                    <div class="w-10 h-10 bg-blue-100 rounded-full mb-2 flex items-center justify-center">
                        <i class="fas fa-baby text-blue-500"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800 mb-1">Second Trimester</h3>
                    <p class="text-xs text-gray-500 mb-2">Weeks 13-26</p>
                    <span class="text-xs text-blue-500 bg-blue-50 px-2 py-1 rounded-full">Current</span>
                </div>
                
                <div class="bg-white p-3 rounded-xl shadow-sm">
                    <div class="w-10 h-10 bg-gray-100 rounded-full mb-2 flex items-center justify-center">
                        <i class="fas fa-birthday-cake text-gray-500"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800 mb-1">Third Trimester</h3>
                    <p class="text-xs text-gray-500 mb-2">Weeks 27-40</p>
                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Upcoming</span>
                </div>
                
                <div class="bg-white p-3 rounded-xl shadow-sm">
                    <div class="w-10 h-10 bg-purple-100 rounded-full mb-2 flex items-center justify-center">
                        <i class="fas fa-stethoscope text-purple-500"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800 mb-1">Anatomy Scan</h3>
                    <p class="text-xs text-gray-500 mb-2">Week 20</p>
                    <span class="text-xs text-purple-500 bg-purple-50 px-2 py-1 rounded-full">Coming soon</span>
                </div>
            </div>
        </div>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg p-2 flex justify-around border-t">
        <button class="p-2 text-pink-500 rounded-full">
            <i class="fas fa-home text-xl"></i>
        </button>
        <button class="p-2 text-gray-500 rounded-full">
            <i class="fas fa-calendar text-xl"></i>
        </button>
        <button class="p-2 text-gray-500 rounded-full">
            <i class="fas fa-images text-xl"></i>
        </button>
        <button class="p-2 text-gray-500 rounded-full">
            <i class="fas fa-user text-xl"></i>
        </button>
    </nav>

    <!-- AI Modal -->
    <div id="aiModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20 hidden">
        <div class="bg-white rounded-xl p-5 w-11/12 max-w-md">
            <h3 class="text-md font-semibold mb-3">AI Journal Helper</h3>
            <div id="aiThinking" class="mb-4">
                <div class="flex items-center text-sm text-gray-600">
                    <span class="ai-loading"></span>
                    <span class="ai-loading"></span>
                    <span class="ai-loading"></span>
                    <span class="ml-2">Generating suggestions...</span>
                </div>
            </div>
            <div id="aiResult" class="hidden bg-gray-50 p-3 rounded-lg mb-4 text-sm">
                <!-- AI content will appear here -->
            </div>
            <div class="flex justify-end space-x-2">
                <button id="cancelAiBtn" class="px-4 py-2 text-sm rounded-lg border border-gray-300">Cancel</button>
                <button id="useAiBtn" class="px-4 py-2 bg-pink-500 text-white text-sm rounded-lg hidden">Use This</button>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        const tabs = document.querySelectorAll('[data-tab]');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Update tabs
                tabs.forEach(t => {
                    t.classList.remove('tab-active');
                    t.classList.add('text-gray-600');
                });
                tab.classList.add('tab-active');
                tab.classList.remove('text-gray-600');
                
                // Update content
                const tabId = tab.getAttribute('data-tab');
                tabContents.forEach(c => c.classList.add('hidden'));
                document.getElementById(tabId).classList.remove('hidden');
            });
        });
        
        // Camera functionality
        const cameraView = document.getElementById('cameraView');
        const photoCanvas = document.getElementById('photoCanvas');
        const photoResult = document.getElementById('photoResult');
        const cameraPlaceholder = document.getElementById('cameraPlaceholder');
        const takePhotoBtn = document.getElementById('takePhotoBtn');
        const photoActions = document.getElementById('photoActions');
        const retakePhotoBtn = document.getElementById('retakePhotoBtn');
        const uploadPhotoBtn = document.getElementById('uploadPhotoBtn');
        const aiHelpBtn = document.getElementById('aiHelpBtn');
        const saveEntryBtn = document.getElementById('saveEntryBtn');
        const aiModal = document.getElementById('aiModal');
        const aiThinking = document.getElementById('aiThinking');
        const aiResult = document.getElementById('aiResult');
        const cancelAiBtn = document.getElementById('cancelAiBtn');
        const useAiBtn = document.getElementById('useAiBtn');
        const entryContent = document.getElementById('entryContent');
        const entryTitle = document.getElementById('entryTitle');
        
        let stream = null;
        
        // Start camera when placeholder is clicked
        cameraPlaceholder.addEventListener('click', startCamera);
        
        async function startCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { facingMode: 'user' }, 
                    audio: false 
                });
                cameraView.srcObject = stream;
                cameraView.style.display = 'block';
                cameraPlaceholder.style.display = 'none';
                takePhotoBtn.classList.remove('hidden');
            } catch (err) {
                console.error("Camera error: ", err);
                alert("Could not access the camera. Please check permissions.");
            }
        }
        
        takePhotoBtn.addEventListener('click', () => {
            // Take photo
            const context = photoCanvas.getContext('2d');
            photoCanvas.width = cameraView.videoWidth;
            photoCanvas.height = cameraView.videoHeight;
            context.drawImage(cameraView, 0, 0, photoCanvas.width, photoCanvas.height);
            
            // Hide camera view, show photo
            cameraView.style.display = 'none';
            photoCanvas.style.display = 'block';
            
            // Show actions
            takePhotoBtn.classList.add('hidden');
            photoActions.classList.remove('hidden');
            
            // Stop camera stream
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
        });
        
        retakePhotoBtn.addEventListener('click', () => {
            // Hide photo, show camera
            photoCanvas.style.display = 'none';
            photoResult.style.display = 'none';
            cameraView.style.display = 'block';
            cameraPlaceholder.style.display = 'none';
            
            // Show take photo button, hide others
            takePhotoBtn.classList.remove('hidden');
            photoActions.classList.add('hidden');
            
            // Restart camera
            startCamera();
        });
        
        uploadPhotoBtn.addEventListener('click', () => {
            // Convert canvas to image
            const imageData = photoCanvas.toDataURL('image/png');
            photoResult.src = imageData;
            
            // Hide canvas, show image
            photoCanvas.style.display = 'none';
            photoResult.style.display = 'block';
            cameraPlaceholder.style.display = 'none';
            
            // Hide actions
            photoActions.classList.add('hidden');
            
            // Auto-generate title
            entryTitle.value = `Week 12 Bump Photo`;
        });
        
        // AI Help functionality
        aiHelpBtn.addEventListener('click', () => {
            aiModal.classList.remove('hidden');
            aiThinking.classList.remove('hidden');
            aiResult.classList.add('hidden');
            useAiBtn.classList.add('hidden');
            
            // Simulate AI thinking (in a real app, this would call an API)
            setTimeout(() => {
                const prompts = [
                    "Today I felt the first little flutters that might be baby moving! It was like tiny bubbles popping in my belly. I can't wait to feel more distinct movements as the weeks go by.",
                    "Had some morning sickness today, but seeing the baby's progress on the pregnancy app made it all worth it. Only 28 more weeks to go until we meet our little one!",
                    "We started decorating the nursery today. Choosing colors and imagining our baby in this space made everything feel so real. I can't believe we'll be parents soon!"
                ];
                
                const randomPrompt = prompts[Math.floor(Math.random() * prompts.length)];
                aiResult.innerHTML = `<p class="text-gray-700">${randomPrompt}</p>`;
                aiThinking.classList.add('hidden');
                aiResult.classList.remove('hidden');
                useAiBtn.classList.remove('hidden');
            }, 2000);
        });
        
        useAiBtn.addEventListener('click', () => {
            entryContent.value = aiResult.textContent;
            aiModal.classList.add('hidden');
        });
        
        cancelAiBtn.addEventListener('click', () => {
            aiModal.classList.add('hidden');
        });
        
        // Save journal entry
        saveEntryBtn.addEventListener('click', () => {
            const content = entryContent.value;
            
            if (!content) {
                alert('Please write something about your day');
                return;
            }
            
            const title = entryTitle.value || "Today's Update";
            
            // In a real app, you would save to a database here
            alert('Entry saved successfully!');
            
            // Clear form
            entryTitle.value = '';
            entryContent.value = '';
            
            // Reset camera if used
            if (photoCanvas.style.display === 'block' || photoResult.style.display === 'block') {
                photoCanvas.style.display = 'none';
                photoResult.style.display = 'none';
                cameraPlaceholder.style.display = 'flex';
                takePhotoBtn.classList.add('hidden');
            }
        });
    </script>
</body>
</html>