<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            width: 95%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px 0;
        }
        
        /* Header Styles */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .print-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .print-btn:hover {
            background-color: #2980b9;
        }
        
        .print-btn i {
            margin-right: 8px;
        }
        
        /* Ticket Header */
        .ticket-header {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .ticket-header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .ticket-title {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .ticket-meta {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .ticket-company {
            font-size: 18px;
            font-weight: 600;
            color: #34495e;
            margin-right: 20px;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-approved {
            background-color: #d4edff;
            color: #1a73e8;
        }
        
        .ticket-date {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
        }
        
        .action-btn {
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }
        
        .action-btn i {
            margin-right: 8px;
        }
        
        .approve-btn {
            background-color: #e6f7ee;
            color: #28a745;
        }
        
        .edit-btn {
            background-color: #e7f1ff;
            color: #1a73e8;
        }
        
        .delete-btn {
            background-color: #fce8e6;
            color: #d93025;
        }
        
        /* Main Grid Layout */
        .main-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        @media (min-width: 1024px) {
            .main-grid {
                grid-template-columns: 2fr 1fr;
            }
        }
        
        /* Card Styles */
        .card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .card-title i {
            margin-right: 10px;
            color: #3498db;
        }
        
        /* Info Grid */
        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }
        
        @media (min-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .info-item p:first-child {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .info-item p:last-child {
            font-weight: 500;
            color: #2c3e50;
        }
        
        /* Table Styles */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        th {
            background-color: #f5f5f5;
            color: #555;
            font-weight: 600;
            font-size: 14px;
        }
        
        tr:hover {
            background-color: #f9f9f9;
        }
        
        .table-total {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        
        /* Notes Section */
        .notes-box {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            color: #555;
        }
        
        /* Staff Section */
        .staff-item {
            margin-bottom: 15px;
        }
        
        .staff-item p:first-child {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .staff-name {
            display: flex;
            align-items: center;
            font-weight: 500;
        }
        
        .staff-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        /* Timeline Styles */
        .timeline-item {
            position: relative;
            padding-left: 40px;
            padding-bottom: 25px;
        }
        
        .timeline-item:not(:last-child)::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 25px;
            height: 100%;
            width: 2px;
            background-color: #e0e0e0;
        }
        
        .timeline-dot {
            position: absolute;
            left: 0;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            color: #555;
        }
        
        .timeline-dot.completed {
            background-color: #e6f7ee;
            color: #28a745;
        }
        
        .timeline-dot.delivered {
            background-color: #e7f1ff;
            color: #1a73e8;
        }
        
        .timeline-dot.initial {
            background-color: #f5f5f5;
            color: #555;
        }
        
        .timeline-content p:first-child {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .timeline-content p:not(:first-child) {
            color: #7f8c8d;
            font-size: 13px;
        }
        
        /* Activities Section */
        .activity-item {
            border-left: 4px solid #3498db;
            padding-left: 15px;
            padding-top: 10px;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .activity-item.payment {
            border-left-color: #28a745;
        }
        
        .activity-item p:first-child {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .activity-item p:not(:first-child) {
            color: #7f8c8d;
            font-size: 13px;
        }
        
        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .ticket-header-top {
                flex-direction: column;
                gap: 15px;
            }
            
            .action-buttons {
                width: 100%;
                flex-wrap: wrap;
            }
            
            .action-btn {
                flex-grow: 1;
                justify-content: center;
            }
            
            table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div>
                <h1>Ticket Management</h1>
                <p>View and manage ticket details</p>
            </div>
            <button class="print-btn">
                <i class="fas fa-print"></i> Print Ticket
            </button>
        </div>

        <!-- Ticket Header -->
        <div class="ticket-header">
            <div class="ticket-header-top">
                <div>
                    <div class="ticket-meta">
                        <span class="ticket-company">Company: ABC Logistics</span>
                        <span class="status-badge status-approved">Approved</span>
                    </div>
                    <h2 class="ticket-title">Ticket #TKT-2023-00142</h2>
                    <p class="ticket-date">Created: 15 Oct 2023, 09:30 AM</p>
                </div>
                <div class="action-buttons">
                    <button class="action-btn approve-btn">
                        <i class="fas fa-check-circle"></i> Approve
                    </button>
                    <button class="action-btn edit-btn">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="action-btn delete-btn">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="main-grid">
            <!-- Left Column -->
            <div class="left-column">
                <!-- Basic Information -->
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i> Basic Information
                    </h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <p>Request Date</p>
                            <p>15 Oct 2023, 09:30 AM</p>
                        </div>
                        <div class="info-item">
                            <p>Customer</p>
                            <p>John Smith</p>
                        </div>
                        <div class="info-item">
                            <p>Phone</p>
                            <p>+****************</p>
                        </div>
                        <div class="info-item">
                            <p>Pickup Address</p>
                            <p>123 Main St, New York, NY 10001</p>
                        </div>
                        <div class="info-item" style="grid-column: 1 / -1;">
                            <p>Delivery Address</p>
                            <p>456 Broadway, Boston, MA 02108</p>
                        </div>
                    </div>
                </div>

                <!-- Vehicle Information -->
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-truck"></i> Vehicle Information
                    </h3>
                    <table>
                        <thead>
                            <tr>
                                <th>License Plate</th>
                                <th>Owner</th>
                                <th>Phone</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>ABC123</td>
                                <td>Michael Johnson</td>
                                <td>+****************</td>
                            </tr>
                            <tr>
                                <td>XYZ789</td>
                                <td>Sarah Williams</td>
                                <td>+****************</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="info-item">
                        <p>Vehicle Quantity</p>
                        <p>2</p>
                    </div>
                    <div class="info-item">
                        <p>Notes</p>
                        <p>Special handling required for fragile items</p>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-dollar-sign"></i> Pricing Information
                    </h3>
                    
                    <div style="margin-bottom: 30px;">
                        <h4 style="font-size: 18px; font-weight: 500; color: #2c3e50; margin-bottom: 15px;">Purchase Price</h4>
                        <table>
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>Amount (Before Tax)</th>
                                    <th>Tax Rate</th>
                                    <th>Amount (After Tax)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Transportation Fee</td>
                                    <td>$500.00</td>
                                    <td>10%</td>
                                    <td>$550.00</td>
                                </tr>
                                <tr>
                                    <td>Handling Fee</td>
                                    <td>$75.00</td>
                                    <td>10%</td>
                                    <td>$82.50</td>
                                </tr>
                                <tr class="table-total">
                                    <td>Total</td>
                                    <td>$575.00</td>
                                    <td></td>
                                    <td>$632.50</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div>
                        <h4 style="font-size: 18px; font-weight: 500; color: #2c3e50; margin-bottom: 15px;">Selling Price</h4>
                        <table>
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>Amount (Before Tax)</th>
                                    <th>Tax Rate</th>
                                    <th>Amount (After Tax)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Transportation Fee</td>
                                    <td>$750.00</td>
                                    <td>10%</td>
                                    <td>$825.00</td>
                                </tr>
                                <tr>
                                    <td>Handling Fee</td>
                                    <td>$100.00</td>
                                    <td>10%</td>
                                    <td>$110.00</td>
                                </tr>
                                <tr class="table-total">
                                    <td>Total</td>
                                    <td>$850.00</td>
                                    <td></td>
                                    <td>$935.00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Additional Notes -->
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-sticky-note"></i> Additional Notes
                    </h3>
                    <div class="notes-box">
                        <p>Customer requested delivery between 9 AM - 5 PM on weekdays only. Special care needed for antique furniture pieces included in shipment.</p>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="right-column">
                <!-- Responsible Staff -->
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i> Responsible Staff
                    </h3>
                    <div>
                        <div class="staff-item">
                            <p>Pricing Staff</p>
                            <p class="staff-name">
                                <img src="https://randomuser.me/api/portraits/women/44.jpg" class="staff-avatar">
                                Lisa Wong
                            </p>
                        </div>
                        <div class="staff-item">
                            <p>Dispatch Staff</p>
                            <p class="staff-name">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" class="staff-avatar">
                                Robert Chen
                            </p>
                        </div>
                        <div class="staff-item">
                            <p>Sales Staff</p>
                            <p class="staff-name">
                                <img src="https://randomuser.me/api/portraits/women/68.jpg" class="staff-avatar">
                                Maria Garcia
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Status Timeline -->
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-history"></i> Status Timeline
                    </h3>
                    <div>
                        <div class="timeline-item">
                            <div class="timeline-dot completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Completed</p>
                                <p>18 Oct 2023, 11:20 AM</p>
                                <p>Changed by: Admin User</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Customer Paid</p>
                                <p>17 Oct 2023, 03:45 PM</p>
                                <p>Changed by: Maria Garcia</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Supplier Paid</p>
                                <p>17 Oct 2023, 10:15 AM</p>
                                <p>Changed by: Lisa Wong</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot delivered">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Delivered</p>
                                <p>16 Oct 2023, 02:30 PM</p>
                                <p>Changed by: Robert Chen</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot delivered">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Approved</p>
                                <p>15 Oct 2023, 11:15 AM</p>
                                <p>Changed by: Admin User</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot initial">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="timeline-content">
                                <p>Initial</p>
                                <p>15 Oct 2023, 09:30 AM</p>
                                <p>Created by: Maria Garcia</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="card">
                    <h3 class="card-title">
                        <i class="fas fa-bell"></i> Recent Activities
                    </h3>
                    <div>
                        <div class="activity-item">
                            <p>Status changed to Completed</p>
                            <p>18 Oct 2023, 11:20 AM</p>
                            <p>By: Admin User</p>
                        </div>
                        <div class="activity-item payment">
                            <p>Payment received from customer</p>
                            <p>17 Oct 2023, 03:45 PM</p>
                            <p>By: Maria Garcia</p>
                        </div>
                        <div class="activity-item payment">
                            <p>Payment sent to supplier</p>
                            <p>17 Oct 2023, 10:15 AM</p>
                            <p>By: Lisa Wong</p>
                        </div>
                        <div class="activity-item">
                            <p>Delivery confirmation added</p>
                            <p>16 Oct 2023, 02:30 PM</p>
                            <p>By: Robert Chen</p>
                        </div>
                        <div class="activity-item">
                            <p>Ticket approved</p>
                            <p>15 Oct 2023, 11:15 AM</p>
                            <p>By: Admin User</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Basic interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Print button functionality
            const printBtn = document.querySelector('.print-btn');
            printBtn.addEventListener('click', function() {
                window.print();
            });
            
            // Action buttons functionality
            const actionButtons = document.querySelectorAll('.action-btn');
            actionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    alert(`Action "${action}" would be performed here`);
                });
            });
            
            // Add hover effect to table rows
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f0f8ff';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });
        });
    </script>
</body>
</html>