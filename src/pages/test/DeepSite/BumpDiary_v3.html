<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bump Diary</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .timeline-container {
            position: relative;
            padding-left: 2.5rem;
        }
        
        .timeline-line {
            position: absolute;
            left: 1.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #f472b6, #8b5cf6);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .timeline-dot {
            position: absolute;
            left: -1.75rem;
            top: 0.25rem;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 1;
        }
        
        .milestone-dot {
            width: 3rem;
            height: 3rem;
            left: -2rem;
            font-size: 1rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .daily-card {
            background: white;
            border-radius: 1rem;
            padding: 1rem;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            border-left: 3px solid #8b5cf6;
        }
        
        .milestone-card {
            background: white;
            border-radius: 1rem;
            padding: 1.25rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: 2px solid #f472b6;
            position: relative;
            overflow: hidden;
        }
        
        .milestone-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #f472b6;
            color: white;
            padding: 0.25rem 0.75rem;
            border-bottom-left-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .progress-bar {
            height: 0.5rem;
            border-radius: 1rem;
            background: #f3f4f6;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 1rem;
            background: linear-gradient(to right, #f472b6, #8b5cf6);
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }
        
        .modal-overlay.active {
            opacity: 1;
            pointer-events: all;
        }
        
        .modal-content {
            background: white;
            border-radius: 1rem;
            width: 90%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            transform: translateY(20px);
            transition: transform 0.3s ease;
        }
        
        .modal-overlay.active .modal-content {
            transform: translateY(0);
        }
        
        .add-entry-btn {
            position: fixed;
            bottom: 5rem;
            right: 1.5rem;
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            background: linear-gradient(to bottom right, #f472b6, #8b5cf6);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10;
        }
        
        .week-header {
            background: linear-gradient(to right, #f472b6, #8b5cf6);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }
        
        .week-header::before {
            content: "";
            position: absolute;
            top: -10px;
            right: -10px;
            width: 3rem;
            height: 3rem;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }
        
        .photo-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .photo-thumbnail {
            aspect-ratio: 1;
            object-fit: cover;
            border-radius: 0.5rem;
            background: #f3f4f6;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen pb-20 font-sans">
    <!-- Header -->
    <header class="bg-white p-4 shadow-sm sticky top-0 z-10">
        <div class="flex justify-between items-center">
            <h1 class="text-xl font-bold text-pink-500">Bump Diary</h1>
            <button class="p-2 rounded-full text-gray-600">
                <i class="fas fa-ellipsis-vertical"></i>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-4">
        <!-- Navigation Tabs -->
        <div class="flex justify-around border-b mb-4">
            <button class="px-4 py-2 font-medium text-sm text-gray-600" data-tab="home">Today</button>
            <button class="tab-active px-4 py-2 font-medium text-sm text-pink-500" data-tab="timeline">Timeline</button>
        </div>
        
        <!-- Timeline Tab -->
        <div id="timeline" class="tab-content">
            <div class="timeline-container">
                <div class="timeline-line"></div>
                
                <!-- Week 12 Header -->
                <div class="week-header">
                    <div class="flex justify-between items-center">
                        <h3 class="font-medium">Week 12</h3>
                        <span class="text-xs opacity-90">June 10-16, 2024</span>
                    </div>
                    <div class="flex items-center mt-1">
                        <div class="progress-bar flex-1 mr-2">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                        <span class="text-xs font-medium">3/4 days</span>
                    </div>
                </div>
                
                <!-- Daily Entry 1 -->
                <div class="timeline-item" data-id="day-1">
                    <div class="timeline-dot bg-purple-500">
                        <i class="fas fa-pencil-alt"></i>
                    </div>
                    <div class="daily-card">
                        <div class="flex justify-between items-center mb-1">
                            <h3 class="text-sm font-medium text-gray-800">Day 84</h3>
                            <span class="text-xs text-gray-500">June 12</span>
                        </div>
                        <p class="text-xs text-gray-600 mb-2">Felt the first little flutters today! It was like tiny bubbles popping in my belly. So excited to feel more movements soon.</p>
                        <div class="photo-grid">
                            <img src="https://images.unsplash.com/photo-1517183140429-ece6e5d55d5a?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" class="photo-thumbnail">
                            <div class="photo-thumbnail flex items-center justify-center text-gray-400">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Daily Entry 2 -->
                <div class="timeline-item" data-id="day-2">
                    <div class="timeline-dot bg-purple-500">
                        <i class="fas fa-pencil-alt"></i>
                    </div>
                    <div class="daily-card">
                        <div class="flex justify-between items-center mb-1">
                            <h3 class="text-sm font-medium text-gray-800">Day 83</h3>
                            <span class="text-xs text-gray-500">June 11</span>
                        </div>
                        <p class="text-xs text-gray-600 mb-2">Had some morning sickness but feeling better now. Ate some crackers and ginger tea helped.</p>
                    </div>
                </div>
                
                <!-- Milestone 1 -->
                <div class="timeline-item" data-id="milestone-1">
                    <div class="timeline-dot bg-pink-500 milestone-dot">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="milestone-card">
                        <div class="milestone-badge">Milestone</div>
                        <div class="flex justify-between items-center mb-1">
                            <h3 class="text-sm font-semibold text-gray-800">First Trimester Complete!</h3>
                            <span class="text-xs text-gray-500">Week 12</span>
                        </div>
                        <p class="text-xs text-gray-600 mb-3">Congratulations! You've completed the first trimester. Baby is now about the size of a plum.</p>
                        <div class="flex justify-between text-xs">
                            <div class="flex items-center text-pink-500">
                                <i class="fas fa-images mr-1"></i>
                                <span>3 photos</span>
                            </div>
                            <button class="text-pink-500 font-medium">View Details</button>
                        </div>
                    </div>
                </div>
                
                <!-- Week 11 Header -->
                <div class="week-header">
                    <div class="flex justify-between items-center">
                        <h3 class="font-medium">Week 11</h3>
                        <span class="text-xs opacity-90">June 3-9, 2024</span>
                    </div>
                    <div class="flex items-center mt-1">
                        <div class="progress-bar flex-1 mr-2">
                            <div class="progress-fill" style="width: 50%"></div>
                        </div>
                        <span class="text-xs font-medium">2/4 days</span>
                    </div>
                </div>
                
                <!-- Daily Entry 3 -->
                <div class="timeline-item" data-id="day-3">
                    <div class="timeline-dot bg-purple-500">
                        <i class="fas fa-pencil-alt"></i>
                    </div>
                    <div class="daily-card">
                        <div class="flex justify-between items-center mb-1">
                            <h3 class="text-sm font-medium text-gray-800">Day 80</h3>
                            <span class="text-xs text-gray-500">June 8</span>
                        </div>
                        <p class="text-xs text-gray-600 mb-2">Had our first ultrasound today! Saw the baby's heartbeat and everything looks perfect.</p>
                        <div class="photo-grid">
                            <img src="https://images.unsplash.com/photo-1529626455594-4ff0752f66a4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" class="photo-thumbnail">
                            <img src="https://images.unsplash.com/photo-1517486808906-6ca8b3f8e1c1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" class="photo-thumbnail">
                            <div class="photo-thumbnail flex items-center justify-center text-gray-400">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Daily Entry 4 -->
                <div class="timeline-item" data-id="day-4">
                    <div class="timeline-dot bg-purple-500">
                        <i class="fas fa-pencil-alt"></i>
                    </div>
                    <div class="daily-card">
                        <div class="flex justify-between items-center mb-1">
                            <h3 class="text-sm font-medium text-gray-800">Day 78</h3>
                            <span class="text-xs text-gray-500">June 6</span>
                        </div>
                        <p class="text-xs text-gray-600 mb-2">Started reading pregnancy books today. Learning so much about what's happening each week.</p>
                    </div>
                </div>
                
                <!-- Milestone 2 -->
                <div class="timeline-item" data-id="milestone-2">
                    <div class="timeline-dot bg-blue-500 milestone-dot">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="milestone-card">
                        <div class="milestone-badge">Milestone</div>
                        <div class="flex justify-between items-center mb-1">
                            <h3 class="text-sm font-semibold text-gray-800">First Ultrasound</h3>
                            <span class="text-xs text-gray-500">Week 11</span>
                        </div>
                        <p class="text-xs text-gray-600 mb-3">Heard the baby's heartbeat for the first time! The doctor said everything looks normal.</p>
                        <div class="flex justify-between text-xs">
                            <div class="flex items-center text-blue-500">
                                <i class="fas fa-images mr-1"></i>
                                <span>2 photos</span>
                            </div>
                            <button class="text-blue-500 font-medium">View Details</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Entry Button -->
    <button class="add-entry-btn">
        <i class="fas fa-plus text-xl"></i>
    </button>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg p-2 flex justify-around border-t">
        <button class="p-2 text-gray-500 rounded-full">
            <i class="fas fa-home text-xl"></i>
        </button>
        <button class="p-2 text-pink-500 rounded-full">
            <i class="fas fa-calendar text-xl"></i>
        </button>
        <button class="p-2 text-gray-500 rounded-full">
            <i class="fas fa-images text-xl"></i>
        </button>
        <button class="p-2 text-gray-500 rounded-full">
            <i class="fas fa-user text-xl"></i>
        </button>
    </nav>

    <!-- Timeline Item Modal -->
    <div id="timelineModal" class="modal-overlay">
        <div class="modal-content">
            <div class="p-5">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h3 id="modalTitle" class="text-lg font-semibold text-gray-800">First Trimester Complete!</h3>
                        <p id="modalDate" class="text-xs text-gray-500">Week 12</p>
                    </div>
                    <button id="closeModalBtn" class="p-2 text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div id="modalImages" class="mb-4">
                    <div class="photo-grid">
                        <img src="https://images.unsplash.com/photo-1517183140429-ece6e5d55d5a?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" class="photo-thumbnail">
                        <img src="https://images.unsplash.com/photo-1529626455594-4ff0752f66a4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" class="photo-thumbnail">
                        <img src="https://images.unsplash.com/photo-1517486808906-6ca8b3f8e1c1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" class="photo-thumbnail">
                    </div>
                </div>
                
                <div id="modalContent" class="text-sm text-gray-700 mb-4">
                    <p class="mb-3">You've completed the first trimester! This is a major milestone in your pregnancy journey.</p>
                    <p class="mb-3">Your baby is now about the size of a plum (approximately 5cm long). All major organs have formed and will continue to grow and mature.</p>
                    <p>Many women find that morning sickness starts to improve around this time as hormone levels stabilize.</p>
                </div>
                
                <div class="flex space-x-2">
                    <button class="flex-1 bg-pink-500 text-white py-2 rounded-lg text-sm font-medium">
                        <i class="fas fa-edit mr-1"></i> Add Note
                    </button>
                    <button class="flex-1 bg-white border border-gray-300 text-gray-700 py-2 rounded-lg text-sm font-medium">
                        <i class="fas fa-share-alt mr-1"></i> Share
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        const tabs = document.querySelectorAll('[data-tab]');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Update tabs
                tabs.forEach(t => {
                    t.classList.remove('tab-active');
                    t.classList.add('text-gray-600');
                });
                tab.classList.add('tab-active');
                tab.classList.remove('text-gray-600');
                
                // Update content
                const tabId = tab.getAttribute('data-tab');
                tabContents.forEach(c => c.classList.add('hidden'));
                document.getElementById(tabId).classList.remove('hidden');
            });
        });
        
        // Timeline item click handler
        document.querySelectorAll('.timeline-item').forEach(item => {
            item.addEventListener('click', () => {
                const id = item.getAttribute('data-id');
                showTimelineModal(id);
            });
        });
        
        // Close modal button
        const closeModalBtn = document.getElementById('closeModalBtn');
        const timelineModal = document.getElementById('timelineModal');
        
        closeModalBtn.addEventListener('click', () => {
            timelineModal.classList.remove('active');
        });
        
        // Show timeline modal with appropriate content
        function showTimelineModal(id) {
            // Sample data for different items
            const items = {
                'milestone-1': {
                    title: 'First Trimester Complete!',
                    date: 'Week 12',
                    content: 'You\'ve completed the first trimester! This is a major milestone in your pregnancy journey.\n\nYour baby is now about the size of a plum (approximately 5cm long). All major organs have formed and will continue to grow and mature.\n\nMany women find that morning sickness starts to improve around this time as hormone levels stabilize.',
                    images: [
                        'https://images.unsplash.com/photo-1517183140429-ece6e5d55d5a?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
                        'https://images.unsplash.com/photo-1529626455594-4ff0752f66a4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
                        'https://images.unsplash.com/photo-1517486808906-6ca8b3f8e1c1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
                    ]
                },
                'day-1': {
                    title: 'First Baby Movements',
                    date: 'Day 84',
                    content: 'Felt the first little flutters today! It was like tiny bubbles popping in my belly. The doctor said these are called "quickening" and it\'s completely normal to feel them between 16-25 weeks for first-time moms.\n\nI can\'t wait for these movements to get stronger so my partner can feel them too!',
                    images: [
                        'https://images.unsplash.com/photo-1517183140429-ece6e5d55d5a?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
                    ]
                },
                'milestone-2': {
                    title: 'First Ultrasound',
                    date: 'Week 11',
                    content: 'We had our first ultrasound today and heard the baby\'s heartbeat! The doctor said everything looks perfect and the baby is measuring right on track.\n\nThe heartbeat was strong at 165 beats per minute. We got to see the tiny arms and legs moving - it was absolutely magical!',
                    images: [
                        'https://images.unsplash.com/photo-1529626455594-4ff0752f66a4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
                        'https://images.unsplash.com/photo-1517486808906-6ca8b3f8e1c1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
                    ]
                },
                'day-3': {
                    title: 'First Ultrasound Appointment',
                    date: 'Day 80',
                    content: 'Had our first ultrasound today! We were so nervous but everything went perfectly. The baby was moving around so much - the technician said that\'s a good sign of healthy development.\n\nWe got printouts of the ultrasound images to show our families. Everyone is so excited!',
                    images: [
                        'https://images.unsplash.com/photo-1529626455594-4ff0752f66a4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
                        'https://images.unsplash.com/photo-1517486808906-6ca8b3f8e1c1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
                    ]
                }
            };
            
            const item = items[id];
            
            // Update modal content
            document.getElementById('modalTitle').textContent = item.title;
            document.getElementById('modalDate').textContent = item.date;
            document.getElementById('modalContent').innerHTML = item.content.split('\n\n').map(p => `<p class="mb-3">${p}</p>`).join('');
            
            // Update images
            const modalImages = document.getElementById('modalImages');
            modalImages.innerHTML = '';
            
            if (item.images.length > 0) {
                const grid = document.createElement('div');
                grid.className = 'photo-grid';
                
                item.images.forEach(img => {
                    const imgEl = document.createElement('img');
                    imgEl.src = img;
                    imgEl.className = 'photo-thumbnail';
                    grid.appendChild(imgEl);
                });
                
                modalImages.appendChild(grid);
            } else {
                const placeholder = document.createElement('div');
                placeholder.className = 'w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center text-gray-400';
                placeholder.innerHTML = '<i class="fas fa-images text-3xl"></i>';
                modalImages.appendChild(placeholder);
            }
            
            // Show modal
            timelineModal.classList.add('active');
        }
        
        // Add entry button
        const addEntryBtn = document.querySelector('.add-entry-btn');
        
        addEntryBtn.addEventListener('click', () => {
            alert('This would open a new entry form in a real app');
        });
    </script>
</body>
</html>