<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Reports Dashboard</title>
    <script src="https://unpkg.com/react@17/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
            --gray-color: #95a5a6;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .dashboard {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        .menu-item.active {
            background-color: var(--primary-color);
        }
        
        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .card-actions {
            display: flex;
        }
        
        .btn {
            padding: 8px 15px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .btn i {
            margin-right: 5px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--gray-color);
            color: var(--gray-color);
            margin-left: 10px;
        }
        
        .btn-outline:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            font-weight: 500;
        }
        
        .tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-success {
            background-color: rgba(46, 204, 113, 0.1);
            color: var(--secondary-color);
        }
        
        .badge-danger {
            background-color: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }
        
        .badge-warning {
            background-color: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }
        
        .chart-container {
            height: 300px;
            margin-top: 20px;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .summary-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .summary-card-title {
            font-size: 14px;
            color: var(--gray-color);
            margin-bottom: 10px;
        }
        
        .summary-card-value {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .summary-card-change {
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .change-up {
            color: var(--secondary-color);
        }
        
        .change-down {
            color: var(--danger-color);
        }
        
        .date-range-selector {
            display: flex;
            align-items: center;
            background-color: white;
            padding: 8px 15px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .date-range-selector i {
            margin-right: 10px;
            color: var(--gray-color);
        }
        
        .date-range-selector select {
            border: none;
            background-color: transparent;
            font-size: 14px;
            color: var(--dark-color);
            outline: none;
        }
        
        .search-box {
            position: relative;
            margin-left: 20px;
        }
        
        .search-box input {
            padding: 8px 15px 8px 35px;
            border-radius: 4px;
            border: 1px solid #ddd;
            width: 200px;
            outline: none;
        }
        
        .search-box i {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-color);
        }
        
        @media (max-width: 1200px) {
            .summary-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Sample data
        const customers = [
            { id: 1, name: 'ABC Corporation', revenue: 125000, cost: 75000, profit: 50000, change: 12.5 },
            { id: 2, name: 'XYZ Ltd', revenue: 98000, cost: 60000, profit: 38000, change: -5.2 },
            { id: 3, name: 'Global Tech', revenue: 145000, cost: 92000, profit: 53000, change: 8.7 },
            { id: 4, name: 'Sunrise Industries', revenue: 76000, cost: 45000, profit: 31000, change: 15.3 },
            { id: 5, name: 'Oceanic Solutions', revenue: 112000, cost: 68000, profit: 44000, change: -2.1 },
        ];

        const suppliers = [
            { id: 1, name: 'Material World Inc.', cost: 85000, revenue: 0, profit: -85000, change: 7.8 },
            { id: 2, name: 'Tech Parts Co.', cost: 62000, revenue: 0, profit: -62000, change: -3.5 },
            { id: 3, name: 'Logistics Pro', cost: 45000, revenue: 0, profit: -45000, change: 10.2 },
            { id: 4, name: 'Service Masters', cost: 38000, revenue: 0, profit: -38000, change: -1.9 },
            { id: 5, name: 'Office Supply Depot', cost: 29000, revenue: 0, profit: -29000, change: 5.6 },
        ];

        const timeRanges = [
            { value: 'today', label: 'Today' },
            { value: 'week', label: 'This Week' },
            { value: 'month', label: 'This Month' },
            { value: 'quarter', label: 'This Quarter' },
            { value: 'year', label: 'This Year' },
            { value: 'custom', label: 'Custom Range' },
        ];

        function formatCurrency(value) {
            return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
        }

        function FinancialReportsDashboard() {
            const [activeTab, setActiveTab] = useState('customers');
            const [timeRange, setTimeRange] = useState('month');
            const [searchTerm, setSearchTerm] = useState('');
            
            // Calculate totals
            const customerTotals = customers.reduce((acc, customer) => {
                acc.revenue += customer.revenue;
                acc.cost += customer.cost;
                acc.profit += customer.profit;
                return acc;
            }, { revenue: 0, cost: 0, profit: 0 });
            
            const supplierTotals = suppliers.reduce((acc, supplier) => {
                acc.cost += supplier.cost;
                acc.profit += supplier.profit;
                return acc;
            }, { cost: 0, profit: 0 });
            
            // Filter data based on search term
            const filteredCustomers = customers.filter(customer => 
                customer.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
            
            const filteredSuppliers = suppliers.filter(supplier => 
                supplier.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
            
            // Set up chart refs
            const customerChartRef = React.useRef(null);
            const supplierChartRef = React.useRef(null);
            const [customerChart, setCustomerChart] = useState(null);
            const [supplierChart, setSupplierChart] = useState(null);
            
            useEffect(() => {
                // Initialize or update customer chart
                if (customerChartRef.current) {
                    if (customerChart) {
                        customerChart.destroy();
                    }
                    
                    const ctx = customerChartRef.current.getContext('2d');
                    const newChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: filteredCustomers.map(c => c.name),
                            datasets: [
                                {
                                    label: 'Revenue',
                                    data: filteredCustomers.map(c => c.revenue),
                                    backgroundColor: 'rgba(52, 152, 219, 0.7)',
                                    borderColor: 'rgba(52, 152, 219, 1)',
                                    borderWidth: 1
                                },
                                {
                                    label: 'Cost',
                                    data: filteredCustomers.map(c => c.cost),
                                    backgroundColor: 'rgba(231, 76, 60, 0.7)',
                                    borderColor: 'rgba(231, 76, 60, 1)',
                                    borderWidth: 1
                                },
                                {
                                    label: 'Profit',
                                    data: filteredCustomers.map(c => c.profit),
                                    backgroundColor: 'rgba(46, 204, 113, 0.7)',
                                    borderColor: 'rgba(46, 204, 113, 1)',
                                    borderWidth: 1
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return formatCurrency(value);
                                        }
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            let label = context.dataset.label || '';
                                            if (label) {
                                                label += ': ';
                                            }
                                            if (context.parsed.y !== null) {
                                                label += formatCurrency(context.parsed.y);
                                            }
                                            return label;
                                        }
                                    }
                                }
                            }
                        }
                    });
                    setCustomerChart(newChart);
                }
                
                // Initialize or update supplier chart
                if (supplierChartRef.current) {
                    if (supplierChart) {
                        supplierChart.destroy();
                    }
                    
                    const ctx = supplierChartRef.current.getContext('2d');
                    const newChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: filteredSuppliers.map(s => s.name),
                            datasets: [
                                {
                                    label: 'Cost',
                                    data: filteredSuppliers.map(s => s.cost),
                                    backgroundColor: 'rgba(231, 76, 60, 0.7)',
                                    borderColor: 'rgba(231, 76, 60, 1)',
                                    borderWidth: 1
                                },
                                {
                                    label: 'Profit Impact',
                                    data: filteredSuppliers.map(s => s.profit),
                                    backgroundColor: 'rgba(243, 156, 18, 0.7)',
                                    borderColor: 'rgba(243, 156, 18, 1)',
                                    borderWidth: 1
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    ticks: {
                                        callback: function(value) {
                                            return formatCurrency(value);
                                        }
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            let label = context.dataset.label || '';
                                            if (label) {
                                                label += ': ';
                                            }
                                            if (context.parsed.y !== null) {
                                                label += formatCurrency(context.parsed.y);
                                            }
                                            return label;
                                        }
                                    }
                                }
                            }
                        }
                    });
                    setSupplierChart(newChart);
                }
                
                return () => {
                    if (customerChart) {
                        customerChart.destroy();
                    }
                    if (supplierChart) {
                        supplierChart.destroy();
                    }
                };
            }, [activeTab, filteredCustomers, filteredSuppliers]);
            
            return (
                <div className="dashboard">
                    <div className="sidebar">
                        <div className="sidebar-header">
                            <h2>Financial Dashboard</h2>
                        </div>
                        <div className="sidebar-menu">
                            <div className="menu-item active">
                                <i className="fas fa-chart-line"></i>
                                <span>Reports</span>
                            </div>
                            <div className="menu-item">
                                <i className="fas fa-users"></i>
                                <span>Customers</span>
                            </div>
                            <div className="menu-item">
                                <i className="fas fa-truck"></i>
                                <span>Suppliers</span>
                            </div>
                            <div className="menu-item">
                                <i className="fas fa-file-invoice-dollar"></i>
                                <span>Invoices</span>
                            </div>
                            <div className="menu-item">
                                <i className="fas fa-cog"></i>
                                <span>Settings</span>
                            </div>
                        </div>
                    </div>
                    
                    <div className="main-content">
                        <div className="header">
                            <div className="page-title">Financial Reports</div>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <div className="date-range-selector">
                                    <i className="fas fa-calendar-alt"></i>
                                    <select value={timeRange} onChange={(e) => setTimeRange(e.target.value)}>
                                        {timeRanges.map(range => (
                                            <option key={range.value} value={range.value}>{range.label}</option>
                                        ))}
                                    </select>
                                </div>
                                <div className="search-box">
                                    <i className="fas fa-search"></i>
                                    <input 
                                        type="text" 
                                        placeholder="Search..." 
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                                <div className="user-info">
                                    <div className="user-avatar">
                                        <i className="fas fa-user"></i>
                                    </div>
                                    <span>Admin</span>
                                </div>
                            </div>
                        </div>
                        
                        <div className="tabs">
                            <div 
                                className={`tab ${activeTab === 'customers' ? 'active' : ''}`}
                                onClick={() => setActiveTab('customers')}
                            >
                                <i className="fas fa-users"></i> By Customers
                            </div>
                            <div 
                                className={`tab ${activeTab === 'suppliers' ? 'active' : ''}`}
                                onClick={() => setActiveTab('suppliers')}
                            >
                                <i className="fas fa-truck"></i> By Suppliers
                            </div>
                        </div>
                        
                        {activeTab === 'customers' ? (
                            <>
                                <div className="summary-cards">
                                    <div className="summary-card">
                                        <div className="summary-card-title">Total Revenue</div>
                                        <div className="summary-card-value">{formatCurrency(customerTotals.revenue)}</div>
                                        <div className="summary-card-change">
                                            <i className="fas fa-arrow-up change-up"></i>
                                            <span className="change-up">12.5% from last {timeRange}</span>
                                        </div>
                                    </div>
                                    <div className="summary-card">
                                        <div className="summary-card-title">Total Cost</div>
                                        <div className="summary-card-value">{formatCurrency(customerTotals.cost)}</div>
                                        <div className="summary-card-change">
                                            <i className="fas fa-arrow-up change-up"></i>
                                            <span className="change-up">8.3% from last {timeRange}</span>
                                        </div>
                                    </div>
                                    <div className="summary-card">
                                        <div className="summary-card-title">Total Profit</div>
                                        <div className="summary-card-value">{formatCurrency(customerTotals.profit)}</div>
                                        <div className="summary-card-change">
                                            <i className="fas fa-arrow-up change-up"></i>
                                            <span className="change-up">15.7% from last {timeRange}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div className="card">
                                    <div className="card-header">
                                        <div className="card-title">Customer Financial Performance</div>
                                        <div className="card-actions">
                                            <button className="btn btn-primary">
                                                <i className="fas fa-download"></i> Export
                                            </button>
                                            <button className="btn btn-outline">
                                                <i className="fas fa-filter"></i> Filter
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div className="chart-container">
                                        <canvas ref={customerChartRef}></canvas>
                                    </div>
                                    
                                    <div className="table-container">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>Customer</th>
                                                    <th>Revenue</th>
                                                    <th>Cost</th>
                                                    <th>Profit</th>
                                                    <th>Profit Margin</th>
                                                    <th>Change</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {filteredCustomers.map(customer => (
                                                    <tr key={customer.id}>
                                                        <td>{customer.name}</td>
                                                        <td>{formatCurrency(customer.revenue)}</td>
                                                        <td>{formatCurrency(customer.cost)}</td>
                                                        <td>
                                                            <span className={customer.profit >= 0 ? 'badge badge-success' : 'badge badge-danger'}>
                                                                {formatCurrency(customer.profit)}
                                                            </span>
                                                        </td>
                                                        <td>{((customer.profit / customer.revenue) * 100).toFixed(1)}%</td>
                                                        <td>
                                                            <span className={customer.change >= 0 ? 'change-up' : 'change-down'}>
                                                                <i className={`fas fa-arrow-${customer.change >= 0 ? 'up' : 'down'}`}></i>
                                                                {Math.abs(customer.change)}%
                                                            </span>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="summary-cards">
                                    <div className="summary-card">
                                        <div className="summary-card-title">Total Supplier Cost</div>
                                        <div className="summary-card-value">{formatCurrency(supplierTotals.cost)}</div>
                                        <div className="summary-card-change">
                                            <i className="fas fa-arrow-up change-up"></i>
                                            <span className="change-up">4.2% from last {timeRange}</span>
                                        </div>
                                    </div>
                                    <div className="summary-card">
                                        <div className="summary-card-title">Total Profit Impact</div>
                                        <div className="summary-card-value">{formatCurrency(supplierTotals.profit)}</div>
                                        <div className="summary-card-change">
                                            <i className="fas fa-arrow-down change-down"></i>
                                            <span className="change-down">2.8% from last {timeRange}</span>
                                        </div>
                                    </div>
                                    <div className="summary-card">
                                        <div className="summary-card-title">Average Cost per Supplier</div>
                                        <div className="summary-card-value">{formatCurrency(supplierTotals.cost / suppliers.length)}</div>
                                        <div className="summary-card-change">
                                            <i className="fas fa-arrow-up change-up"></i>
                                            <span className="change-up">1.5% from last {timeRange}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div className="card">
                                    <div className="card-header">
                                        <div className="card-title">Supplier Cost Analysis</div>
                                        <div className="card-actions">
                                            <button className="btn btn-primary">
                                                <i className="fas fa-download"></i> Export
                                            </button>
                                            <button className="btn btn-outline">
                                                <i className="fas fa-filter"></i> Filter
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div className="chart-container">
                                        <canvas ref={supplierChartRef}></canvas>
                                    </div>
                                    
                                    <div className="table-container">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>Supplier</th>
                                                    <th>Cost</th>
                                                    <th>Profit Impact</th>
                                                    <th>% of Total Cost</th>
                                                    <th>Change</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {filteredSuppliers.map(supplier => (
                                                    <tr key={supplier.id}>
                                                        <td>{supplier.name}</td>
                                                        <td>{formatCurrency(supplier.cost)}</td>
                                                        <td>
                                                            <span className="badge badge-danger">
                                                                {formatCurrency(supplier.profit)}
                                                            </span>
                                                        </td>
                                                        <td>{((supplier.cost / supplierTotals.cost) * 100).toFixed(1)}%</td>
                                                        <td>
                                                            <span className={supplier.change >= 0 ? 'change-up' : 'change-down'}>
                                                                <i className={`fas fa-arrow-${supplier.change >= 0 ? 'up' : 'down'}`}></i>
                                                                {Math.abs(supplier.change)}%
                                                            </span>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            );
        }

        ReactDOM.render(<FinancialReportsDashboard />, document.getElementById('root'));
    </script>
</body>
</html>