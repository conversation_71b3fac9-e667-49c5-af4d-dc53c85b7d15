import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Reports from './Reports';
import apiService from '../services/api.service';

// Mock the API service
jest.mock('../services/api.service');

// Mock axios
jest.mock('axios', () => {
  const mockAxios = {
    get: jest.fn(() => Promise.resolve({ data: {} })),
    post: jest.fn(() => Promise.resolve({ data: {} }))
  };
  mockAxios.create = jest.fn(() => mockAxios);
  mockAxios.interceptors = {
    request: { use: jest.fn() },
    response: { use: jest.fn() }
  };
  return mockAxios;
});

// Mock recharts components
jest.mock('recharts', () => {
  const OriginalModule = jest.requireActual('recharts');
  return {
    ...OriginalModule,
    ResponsiveContainer: ({ children }) => <div data-testid="responsive-container">{children}</div>,
    BarChart: ({ children }) => <div data-testid="bar-chart">{children}</div>,
    LineChart: ({ children }) => <div data-testid="line-chart">{children}</div>,
    PieChart: ({ children }) => <div data-testid="pie-chart">{children}</div>,
    Bar: () => <div data-testid="bar" />,
    Line: () => <div data-testid="line" />,
    Pie: () => <div data-testid="pie" />,
    XAxis: () => <div data-testid="x-axis" />,
    YAxis: () => <div data-testid="y-axis" />,
    CartesianGrid: () => <div data-testid="cartesian-grid" />,
    Tooltip: () => <div data-testid="tooltip" />,
    Legend: () => <div data-testid="legend" />,
    Cell: () => <div data-testid="cell" />
  };
});

// Mock localStorage
const mockLocalStorage = (() => {
  let store = {};
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

describe('Reports Component', () => {
  const mockCompanies = [
    { id: 1, company_name: 'Company 1' },
    { id: 2, company_name: 'Company 2' }
  ];

  const mockCustomers = [
    { id: 1, customer_name: 'Customer 1' },
    { id: 2, customer_name: 'Customer 2' }
  ];

  const mockSuppliers = [
    { id: 1, supplier_name: 'Supplier 1' },
    { id: 2, supplier_name: 'Supplier 2' }
  ];

  const mockStaffs = [
    { id: 1, display_name: 'Staff 1' },
    { id: 2, display_name: 'Staff 2' }
  ];

  const mockReportData = [
    {
      ticket_id: 1,
      request_date: '2023-01-01',
      current_status: 'completed',
      customer_id: 1,
      customer_name: 'Customer 1',
      supplier_id: 1,
      supplier_name: 'Supplier 1',
      pricing_staff_name: 'Staff 1',
      dispatch_staff_name: 'Staff 2',
      sale_staff_name: 'Staff 1',
      company_id: 1,
      company_name: 'Company 1',
      total_receive_before_tax: 1000,
      total_receive_after_tax: 1100,
      total_pay_before_tax: 800,
      total_pay_after_tax: 880
    },
    {
      ticket_id: 2,
      request_date: '2023-01-02',
      current_status: 'initial',
      customer_id: 2,
      customer_name: 'Customer 2',
      supplier_id: 2,
      supplier_name: 'Supplier 2',
      pricing_staff_name: 'Staff 2',
      dispatch_staff_name: 'Staff 1',
      sale_staff_name: 'Staff 2',
      company_id: 2,
      company_name: 'Company 2',
      total_receive_before_tax: 1500,
      total_receive_after_tax: 1650,
      total_pay_before_tax: 1200,
      total_pay_after_tax: 1320
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.clear();

    // Mock API responses
    apiService.get.mockImplementation((endpoint) => {
      if (endpoint.includes('company')) {
        return Promise.resolve({
          companies: mockCompanies,
          data: mockCompanies
        });
      } else if (endpoint.includes('customer')) {
        return Promise.resolve({
          customers: mockCustomers,
          data: mockCustomers
        });
      } else if (endpoint.includes('supplier')) {
        return Promise.resolve({
          suppliers: mockSuppliers,
          data: mockSuppliers
        });
      } else if (endpoint.includes('user')) {
        return Promise.resolve({
          users: mockStaffs,
          data: mockStaffs
        });
      } else if (endpoint.includes('report')) {
        return Promise.resolve(mockReportData);
      }
      return Promise.resolve([]);
    });

    // Mock localStorage for company_id
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'user') {
        return JSON.stringify({ company_id: 1 });
      }
      return null;
    });

    // Set up date for consistent testing
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2023, 0, 15)); // January 15, 2023
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test('renders reports page with tabs', async () => {
    await act(async () => {
      render(<Reports />);
    });

    // Check if the main tabs are rendered
    expect(screen.getByText(/Báo cáo phân tích/i)).toBeInTheDocument();
    expect(screen.getByText(/Báo cáo khách hàng/i)).toBeInTheDocument();
    expect(screen.getByText(/Báo cáo nhà cung cấp/i)).toBeInTheDocument();

    // Check if view tabs are rendered
    expect(screen.getByText(/Tổng quan/i)).toBeInTheDocument();
    expect(screen.getByText(/Chi tiết/i)).toBeInTheDocument();
    expect(screen.getByText(/Xu hướng/i)).toBeInTheDocument();

    // Check if filter section is rendered
    expect(screen.getByText(/Từ ngày:/i)).toBeInTheDocument();
    expect(screen.getByText(/Đến ngày:/i)).toBeInTheDocument();
    expect(screen.getByText(/Công ty:/i)).toBeInTheDocument();

    // Wait for data to load
    await waitFor(() => {
      expect(apiService.get).toHaveBeenCalled();
    });
  });

  test('switches between customer and supplier tabs', async () => {
    await act(async () => {
      render(<Reports />);
    });

    // Initially on customer tab
    expect(screen.getByText(/Báo cáo khách hàng/i)).toHaveClass('active');

    // Switch to supplier tab
    await act(async () => {
      await userEvent.click(screen.getByText(/Báo cáo nhà cung cấp/i));
    });

    // Supplier tab should be active
    expect(screen.getByText(/Báo cáo nhà cung cấp/i)).toHaveClass('active');

    // Customer-specific filters should not be visible
    expect(screen.queryByText(/Khách hàng:/i)).not.toBeInTheDocument();

    // Supplier-specific filters should be visible
    expect(screen.getByText(/Nhà cung cấp:/i)).toBeInTheDocument();

    // Switch back to customer tab
    await act(async () => {
      await userEvent.click(screen.getByText(/Báo cáo khách hàng/i));
    });

    // Customer tab should be active
    expect(screen.getByText(/Báo cáo khách hàng/i)).toHaveClass('active');

    // Customer-specific filters should be visible
    expect(screen.getByText(/Khách hàng:/i)).toBeInTheDocument();
  });

  test('toggles filter visibility', async () => {
    await act(async () => {
      render(<Reports />);
    });

    // Filter should be visible initially
    expect(screen.getByText(/Từ ngày:/i)).toBeVisible();

    // Click filter toggle button
    await act(async () => {
      await userEvent.click(screen.getByText(/Bộ lọc/i));
    });

    // Filter should be collapsed
    const filterContainer = screen.getByText(/Từ ngày:/i).closest('.report-filter-container');
    expect(filterContainer).toHaveClass('collapsed');

    // Click filter toggle button again
    await act(async () => {
      await userEvent.click(screen.getByText(/Bộ lọc/i));
    });

    // Filter should be expanded
    expect(filterContainer).toHaveClass('expanded');
  });

  test('switches between report view types', async () => {
    await act(async () => {
      render(<Reports />);
    });

    // Initially on summary view
    expect(screen.getByText(/Tổng quan/i)).toHaveClass('active');

    // Switch to detail view
    await act(async () => {
      await userEvent.click(screen.getByText(/Chi tiết/i));
    });

    // Detail view should be active
    expect(screen.getByText(/Chi tiết/i)).toHaveClass('active');

    // Switch to trend view
    await act(async () => {
      await userEvent.click(screen.getByText(/Xu hướng/i));
    });

    // Trend view should be active
    expect(screen.getByText(/Xu hướng/i)).toHaveClass('active');
  });

  test('loads and displays report data', async () => {
    await act(async () => {
      render(<Reports />);
    });

    // Wait for data to load
    await waitFor(() => {
      expect(apiService.get).toHaveBeenCalledWith(expect.stringContaining('report'));
    });

    // Check if summary cards are displayed
    await waitFor(() => {
      expect(screen.getByText(/Tổng số khách hàng/i)).toBeInTheDocument();
      expect(screen.getByText(/Tổng số vé/i)).toBeInTheDocument();
      expect(screen.getByText(/Tổng doanh thu/i)).toBeInTheDocument();
    });
  });
});
