import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FiAlertCircle,
  <PERSON>Eye,
  FiEyeOff,
  FiUserPlus,
} from "react-icons/fi";
import "../styles/Login.css";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";
import axios from 'axios';
import { API_CONFIG } from '../config/api.config';

const Login = ({ onLogin }) => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      if (isRegistering) {
        // Handle registration
        const registerData = {
          username,
          password,
          ...(displayName && { display_name: displayName }),
        };

        const registerResponse = await apiService.post(
          ENDPOINTS.USERS.CREATE,
          registerData
        );

        if (registerResponse.message === "User created successfully") {
          // Switch back to login form after successful registration
          setIsRegistering(false);
          setError("Đăng ký thành công! Vui lòng đăng nhập.");
          setPassword("");
          setDisplayName("");
        }
      } else {
        // Handle login with form-urlencoded data
        const loginResponse = await axios.post(
          `${API_CONFIG.BASE_URL}${ENDPOINTS.AUTH.LOGIN}`,
          `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              "Accept": "application/json"
            }
          }
        ).then(response => response.data);

        if (loginResponse.status === 1 && loginResponse.access_token) {
          const expiresAt = new Date(
            Date.now() +
              (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000)
          );

          const tempUserData = {
            username: username,
            name: "",
            role: "",
            company_id: "",
            isLoggedIn: true,
            token: loginResponse.access_token,
            expiresAt: expiresAt.toISOString(),
          };

          localStorage.setItem("user", JSON.stringify(tempUserData));

          const profileResponse = await apiService.get(ENDPOINTS.AUTH.PROFILE);

        // Update user data with profile information
        const userData = {
          ...tempUserData,
          username: profileResponse.username,
          name: profileResponse.display_name,
          role: profileResponse.role,
          id: profileResponse.id,
          company_id: profileResponse.company_id
        };

          localStorage.setItem("user", JSON.stringify(userData));

          // Fetch company information
          try {
            const companyResponse = await apiService.get(ENDPOINTS.COMPANY.GET_BASIC);

            if (companyResponse && companyResponse.companies) {
              localStorage.setItem("companyInfo", JSON.stringify(companyResponse.companies));
              console.log("Company info saved (companies):", companyResponse.companies);
            } else if (companyResponse && Array.isArray(companyResponse)) {
              localStorage.setItem("companyInfo", JSON.stringify(companyResponse));
              console.log("Company info saved (array):", companyResponse);
            } else if (companyResponse && companyResponse.data) {
              localStorage.setItem("companyInfo", JSON.stringify(companyResponse.data));
              console.log("Company info saved (data):", companyResponse.data);
            } else {
              console.error("Unexpected company response structure:", companyResponse);
            }
          } catch (companyError) {
            console.error("Error fetching company information:", companyError);
            // Continue with login even if company info fetch fails
          }

          onLogin(userData);
        } else {
          setError("Tên đăng nhập hoặc mật khẩu không đúng");
        }
      }
    } catch (error) {
      if (isRegistering) {
        if (error.response?.data?.detail === "Username already exists") {
          setError("Tên đăng nhập đã tồn tại");
        } else {
          setError("Đăng ký thất bại. Vui lòng thử lại.");
        }
      } else if (error.response?.data?.detail === "Inactive user") {
        setError("Tài khoản không hoạt động");
      } else {
        setError("Đã có lỗi xảy ra. Vui lòng thử lại sau.");
      }
      localStorage.removeItem("user");
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleForm = () => {
    setIsRegistering(!isRegistering);
    setError("");
    setPassword("");
    setDisplayName("");
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h2>Ticket System</h2>
          <p>
            {isRegistering ? "Đăng ký tài khoản mới" : "Đăng nhập để tiếp tục"}
          </p>
        </div>

        {error && (
          <div
            className={`login-error ${
              error.includes("thành công") ? "success" : ""
            }`}
          >
            <FiAlertCircle />
            <span>{error}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="login-form">
          <div className="login-form-group">
            <label htmlFor="username">
              <FiUser /> Tên đăng nhập
            </label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Nhập tên đăng nhập"
              required
            />
          </div>

          {isRegistering && (
            <div className="form-group">
              <label htmlFor="displayName">
                <FiUser /> Tên hiển thị (không bắt buộc)
              </label>
              <input
                type="text"
                id="displayName"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                placeholder="Nhập tên hiển thị"
              />
            </div>
          )}

          {isRegistering && (
            <div className="login-form-group">
              <label htmlFor="displayName">
                <FiUser /> Tên hiển thị (không bắt buộc)
              </label>
              <input
                type="text"
                id="displayName"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                placeholder="Nhập tên hiển thị"
              />
            </div>
          )}

          <div className="login-form-group">
            <label htmlFor="password">
              <FiLock /> Mật khẩu
            </label>
            <div className="login-password-input-container">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Nhập mật khẩu"
                required
              />
              <button
                type="button"
                className="login-password-toggle"
                onClick={togglePasswordVisibility}
                tabIndex="-1"
              >
                {showPassword ? <FiEyeOff /> : <FiEye />}
              </button>
            </div>
          </div>

          <div className="login-form-footer">
            <div className="login-remember-me">
              <input
                type="checkbox"
                id="remember"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              <label htmlFor="remember">Ghi nhớ đăng nhập</label>
            </div>
            <a href="#" className="forgot-password">
              Quên mật khẩu?
            </a>
          </div>

          <button type="submit" className="login-button" disabled={isLoading}>
            {isLoading
              ? isRegistering
                ? "Đang đăng ký..."
                : "Đang đăng nhập..."
              : isRegistering
              ? "Đăng ký"
              : "Đăng nhập"}
          </button>

          <div className="form-switch">
            <button
              type="button"
              className="switch-button"
              onClick={toggleForm}
            >
              {isRegistering ? (
                <>
                  <FiUser /> Đã có tài khoản? Đăng nhập
                </>
              ) : (
                <>
                  <FiUserPlus /> Chưa có tài khoản? Đăng ký
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;
