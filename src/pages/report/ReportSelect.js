import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  <PERSON><PERSON>ie<PERSON>hart, 
  FiUser, 
  FiTruck, 
  FiBarChart2, 
  FiFileText 
} from 'react-icons/fi';
import '../../styles/Reports.css';
import '../../styles/ReportSelect.css';

const ReportSelect = () => {
  const navigate = useNavigate();

  const reportTypes = [
    {
      id: 'overview',
      title: 'Báo cáo tổng hợp',
      description: 'Xem tổng quan về doanh thu, chi phí, và các chỉ số kinh doanh chính',
      icon: <FiPieChart />,
      path: '/report/summary',
      params: { tab: 'overview' }
    },
    {
      id: 'customer',
      title: 'Tạo bảng kê khách hàng',
      description: 'Tạo báo cáo chi tiết về khách hàng, doanh thu và các giao dịch',
      icon: <FiUser />,
      path: '/report/customer',
      params: { tab: 'customer' }
    },
    {
      id: 'supplier',
      title: 'Tạo bảng kê nhà cung cấp',
      description: 'Tạo báo cáo chi tiết về nhà cung cấp, chi phí và các giao dịch',
      icon: <FiTruck />,
      path: '/report/supplier',
      params: { tab: 'supplier' }
    }
  ];

  const handleReportSelect = (reportType) => {
    // Navigate to the selected report page with appropriate parameters
    navigate(reportType.path, { state: reportType.params });
  };

  return (
    <div className="report-select-container">
      <div className="report-select-header">
        <h1>Chọn loại báo cáo</h1>
        <p>Vui lòng chọn loại báo cáo bạn muốn xem hoặc tạo</p>
      </div>

      <div className="report-select-cards">
        {reportTypes.map((reportType) => (
          <div 
            key={reportType.id} 
            className="report-select-card" 
            onClick={() => handleReportSelect(reportType)}
          >
            <div className="report-card-icon">
              {reportType.icon}
            </div>
            <div className="report-card-content">
              <h3>{reportType.title}</h3>
              <p>{reportType.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReportSelect;
