import React, { useState, useEffect } from 'react';
import {
  FiDownload,
  <PERSON><PERSON>ilter,
  FiCalendar,
  Fi<PERSON>ser,
  FiRefreshCw,
  FiList,
  FiAlertCircle,
  FiCheckCircle,
  FiHome
} from 'react-icons/fi';
import apiService from '../../services/api.service';
import { ENDPOINTS } from '../../config/api.config';
import '../../styles/Reports.css';
import '../../styles/ReportCustomer.css';
import * as XLSX from 'xlsx';

const ReportCustomer = () => {
  // State for filters
  const [filters, setFilters] = useState({
    month: new Date().getMonth() + 1, // Current month (1-12)
    year: new Date().getFullYear(), // Current year
    customer_id: '',
    customer_name: '',
    company_id: ''
  });

  // State for data
  const [reportData, setReportData] = useState([]);
  const [selectedItems, setSelectedItems] = useState({});
  const [selectAll, setSelectAll] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');

  // Auto-hide success and error messages after 3 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [error]);

  // State for dropdown options
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [showCustomerSuggestions, setShowCustomerSuggestions] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [isCustomerLoading, setIsCustomerLoading] = useState(false);
  const [isCompanyLoading, setIsCompanyLoading] = useState(false);

  // Fetch customers and companies on component mount
  useEffect(() => {
    fetchCustomers();
    fetchCompanies();
  }, []);

  // Fetch customers
  const fetchCustomers = async () => {
    try {
      setIsCustomerLoading(true);
      const response = await apiService.get(ENDPOINTS.CUSTOMERS.GET_BASIC);

      if (response && response.customers) {
        setCustomers(response.customers);
      } else if (response && Array.isArray(response)) {
        setCustomers(response);
      } else if (response && response.data) {
        setCustomers(response.data);
      } else {
        console.error('Unexpected customer response structure:', response);
        setCustomers([]);
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
      setCustomers([]);
    } finally {
      setIsCustomerLoading(false);
    }
  };

  // Fetch companies
  const fetchCompanies = async () => {
    try {
      setIsCompanyLoading(true);
      const response = await apiService.get(ENDPOINTS.COMPANY.GET_BASIC);

      if (response && response.companies) {
        setCompanies(response.companies);
      } else if (response && Array.isArray(response)) {
        setCompanies(response);
      } else if (response && response.data) {
        setCompanies(response.data);
      } else {
        console.error('Unexpected company response structure:', response);
        setCompanies([]);
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
      setCompanies([]);
    } finally {
      setIsCompanyLoading(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle customer input change
  const handleCustomerInputChange = (e) => {
    const value = e.target.value;
    setFilters(prev => ({
      ...prev,
      customer_name: value,
      customer_id: '' // Reset customer_id when typing
    }));

    // Filter customers based on input
    if (value.trim() === '') {
      setFilteredCustomers([]);
      setShowCustomerSuggestions(false);
    } else {
      const filtered = customers.filter(customer =>
        customer.customer_name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredCustomers(filtered);
      setShowCustomerSuggestions(true);
    }
  };

  // Handle customer selection from suggestions
  const handleCustomerSelect = (customer) => {
    setFilters(prev => ({
      ...prev,
      customer_id: customer.id,
      customer_name: customer.customer_name
    }));
    setShowCustomerSuggestions(false);
  };

  // Handle click outside suggestions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.customer-autocomplete')) {
        setShowCustomerSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle month/year changes
  const handleMonthChange = (e) => {
    const month = parseInt(e.target.value);
    if (month >= 1 && month <= 12) {
      setFilters(prev => ({
        ...prev,
        month
      }));
    }
  };

  const handleYearChange = (e) => {
    const year = parseInt(e.target.value);
    if (year >= 2000 && year <= 2100) {
      setFilters(prev => ({
        ...prev,
        year
      }));
    }
  };

  // Format date to YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Generate start and end dates for the selected month
  const getDateRangeForMonth = (year, month) => {
    // Month is 1-based in our UI but 0-based in Date constructor
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0); // Last day of the month

    return {
      startDate: formatDate(startDate),
      endDate: formatDate(endDate)
    };
  };

  // Fetch report data
  const fetchReportData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccessMessage('');

      const { startDate, endDate } = getDateRangeForMonth(filters.year, filters.month);

      const params = {
        start_date: startDate,
        end_date: endDate
      };

      if (filters.customer_id) {
        params.customer_id = filters.customer_id;
      }

      if (filters.company_id) {
        params.company_id = filters.company_id;
      }

      const response = await apiService.get(ENDPOINTS.REPORT.CUSTOMER(params));

      if (response) {
        setReportData(Array.isArray(response) ? response : []);
        // Reset selection state
        setSelectedItems({});
        setSelectAll(false);
        setSuccessMessage('Dữ liệu báo cáo đã được tải thành công');
      } else {
        setReportData([]);
        setSelectedItems({});
        setSelectAll(false);
        setError('Không có dữ liệu báo cáo');
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      setError('Đã xảy ra lỗi khi tải dữ liệu báo cáo');
      setReportData([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    fetchReportData();
  };

  // Reset filters
  const handleResetFilters = () => {
    setFilters({
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      customer_id: '',
      customer_name: '',
      company_id: ''
    });
    setShowCustomerSuggestions(false);
  };

  // Handle item selection
  const handleItemSelect = (ticketId) => {
    setSelectedItems(prev => ({
      ...prev,
      [ticketId]: !prev[ticketId]
    }));
  };

  // Handle select all
  const handleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);

    const newSelectedItems = {};
    if (newSelectAll) {
      // Select all items
      reportData.forEach(item => {
        newSelectedItems[item.ticket_id] = true;
      });
    }
    setSelectedItems(newSelectedItems);
  };

  // Check if any items are selected
  const hasSelectedItems = () => {
    return Object.values(selectedItems).some(value => value);
  };

  // Get selected items count
  const getSelectedItemsCount = () => {
    return Object.values(selectedItems).filter(value => value).length;
  };

  // Export to Excel
  const exportToExcel = () => {
    if (reportData.length === 0) {
      setError('Không có dữ liệu để xuất');
      return;
    }

    try {
      // Determine which items to export
      const itemsToExport = hasSelectedItems()
        ? reportData.filter(item => selectedItems[item.ticket_id])
        : reportData;

      if (itemsToExport.length === 0) {
        setError('Vui lòng chọn ít nhất một mục để xuất');
        return;
      }

      // Format data for export
      const exportData = itemsToExport.map(item => ({
        'Mã phiếu': item.ticket_id,
        'Ngày yêu cầu': item.request_date,
        'Khách hàng': item.customer_name,
        'Công ty': item.company_name,
        'Biển số xe': item.vehicle_plate || 'N/A',
        'Địa chỉ lấy hàng': item.pickup_address,
        'Địa chỉ giao hàng': item.delivery_address,
        'Ghi chú': item.note,
        'Tổng tiền trước thuế': item.total_receive_before_tax,
        'Tổng tiền sau thuế': item.total_receive_after_tax
      }));

      // Create worksheet
      const ws = XLSX.utils.json_to_sheet(exportData);

      // Create workbook
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Customer Report');

      // Generate filename
      const fileName = `BaoCaoKhachHang_${filters.month}_${filters.year}.xlsx`;

      // Export to file
      XLSX.writeFile(wb, fileName);

      setSuccessMessage('Xuất báo cáo thành công');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      setError('Đã xảy ra lỗi khi xuất báo cáo');
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  // Format date for display
  const formatDateForDisplay = (dateString) => {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };

  return (
    <div className="report-customer-container">
      <div className="report-customer-header">
        <h1>Báo cáo khách hàng</h1>
        <p>Xem và xuất báo cáo chi tiết về khách hàng</p>
      </div>

      <div className="report-filter-section">
        <form onSubmit={handleSubmit}>
          <div className="report-filter-group">
            <div className="report-filter-item">
              <label><FiHome /> Công ty:</label>
              {isCompanyLoading ? (
                <div className="select-loading">Đang tải...</div>
              ) : (
                <select
                  name="company_id"
                  value={filters.company_id}
                  onChange={handleFilterChange}
                >
                  <option value="">Tất cả công ty</option>
                  {companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.company_name}
                    </option>
                  ))}
                </select>
              )}
            </div>

            <div className="report-filter-item month-year-filter">
              <label><FiCalendar /> Tháng/Năm:</label>
              <div className="month-year-inputs">
                <input
                  type="number"
                  name="month"
                  min="1"
                  max="12"
                  value={filters.month}
                  onChange={handleMonthChange}
                  placeholder="MM"
                />
                <span>/</span>
                <input
                  type="number"
                  name="year"
                  min="2000"
                  max="2100"
                  value={filters.year}
                  onChange={handleYearChange}
                  placeholder="YYYY"
                />
              </div>
            </div>

            <div className="report-filter-item customer-autocomplete">
              <label><FiUser /> Khách hàng:</label>
              {isCustomerLoading ? (
                <div className="select-loading">Đang tải...</div>
              ) : (
                <div className="autocomplete-wrapper">
                  <input
                    type="text"
                    name="customer_name"
                    value={filters.customer_name}
                    onChange={handleCustomerInputChange}
                    placeholder="Nhập tên khách hàng"
                    autoComplete="off"
                  />
                  {showCustomerSuggestions && filteredCustomers.length > 0 && (
                    <div className="autocomplete-suggestions">
                      {filteredCustomers.map(customer => (
                        <div
                          key={customer.id}
                          className="autocomplete-suggestion"
                          onClick={() => handleCustomerSelect(customer)}
                        >
                          {customer.customer_name}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="report-filter-actions">
              <button type="submit" className="report-btn-filter">
                <FiFilter /> Lọc
              </button>
              <button type="button" className="report-btn-filter report-btn-clear" onClick={handleResetFilters}>
                <FiRefreshCw /> Đặt lại
              </button>
              <button
                type="button"
                className="report-btn-filter report-btn-export"
                onClick={exportToExcel}
                disabled={reportData.length === 0}
                title={hasSelectedItems() ? `Xuất ${getSelectedItemsCount()} mục đã chọn` : 'Xuất tất cả dữ liệu'}
              >
                <FiDownload /> {hasSelectedItems() ? `Xuất (${getSelectedItemsCount()})` : 'Xuất Excel'}
              </button>
            </div>
          </div>
        </form>
      </div>

      {error && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{error}</span>
        </div>
      )}

      {successMessage && (
        <div className="success-message">
          <FiCheckCircle />
          <span>{successMessage}</span>
        </div>
      )}

      {isLoading ? (
        <div className="loading-container">
          <div className="report-loading-spinner"></div>
        </div>
      ) : reportData.length > 0 ? (
        <div className="report-content-container">
          <div className="dashboard-card full-width">
            <div className="dashboard-card-header">
              <h3>
                <FiList /> Chi tiết báo cáo khách hàng
              </h3>
              <div className="report-summary">
                <span>Tổng số phiếu: {reportData.length}</span>
                <span>Đã chọn: {getSelectedItemsCount()} phiếu</span>
                <span>Tổng tiền: {formatCurrency(reportData.reduce((sum, item) => sum + (item.total_receive_after_tax || 0), 0))}</span>
              </div>
            </div>
            <div className="dashboard-card-body">
              <div className="report-table-container">
                <table className="report-table modern-table">
                  <thead>
                    <tr>
                      <th className="checkbox-column">
                        <label className="checkbox-container">
                          <input
                            type="checkbox"
                            checked={selectAll}
                            onChange={handleSelectAll}
                          />
                          <span className="checkmark"></span>
                        </label>
                      </th>
                      <th>Mã phiếu</th>
                      <th>Ngày yêu cầu</th>
                      <th>Khách hàng</th>
                      <th>Công ty</th>
                      <th>Biển số xe</th>
                      <th>Địa chỉ lấy hàng</th>
                      <th>Địa chỉ giao hàng</th>
                      <th className="right-align">Tổng tiền trước thuế</th>
                      <th className="right-align">Tổng tiền sau thuế</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.map((item, index) => (
                      <tr key={index}>
                        <td className="checkbox-column">
                          <label className="checkbox-container">
                            <input
                              type="checkbox"
                              checked={!!selectedItems[item.ticket_id]}
                              onChange={() => handleItemSelect(item.ticket_id)}
                            />
                            <span className="checkmark"></span>
                          </label>
                        </td>
                        <td>{item.ticket_id}</td>
                        <td>{formatDateForDisplay(item.request_date)}</td>
                        <td>{item.customer_name}</td>
                        <td>{item.company_name}</td>
                        <td>{item.vehicle_plate || 'N/A'}</td>
                        <td>{item.pickup_address}</td>
                        <td>{item.delivery_address}</td>
                        <td className="right-align">{formatCurrency(item.total_receive_before_tax)}</td>
                        <td className="right-align">{formatCurrency(item.total_receive_after_tax)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="no-data-message">
          <p>Chưa có dữ liệu báo cáo. Vui lòng chọn điều kiện lọc và nhấn nút "Lọc".</p>
        </div>
      )}
    </div>
  );
};

export default ReportCustomer;
