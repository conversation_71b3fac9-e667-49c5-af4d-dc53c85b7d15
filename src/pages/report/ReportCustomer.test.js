import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ReportCustomer from './ReportCustomer';
import apiService from '../../services/api.service';

// Mock the apiService
jest.mock('../../services/api.service', () => ({
  get: jest.fn()
}));

// Mock XLSX
jest.mock('xlsx', () => ({
  utils: {
    json_to_sheet: jest.fn(),
    book_new: jest.fn(() => ({})),
    book_append_sheet: jest.fn()
  },
  writeFile: jest.fn()
}));

describe('ReportCustomer Component', () => {
  const mockCustomers = [
    { id: 1, customer_name: 'Customer 1' },
    { id: 2, customer_name: 'Customer 2' }
  ];

  const mockCompanies = [
    { id: 1, company_name: 'Company 1' },
    { id: 2, company_name: 'Company 2' }
  ];

  const mockReportData = [
    {
      ticket_id: 61,
      request_date: '2025-04-22',
      customer_id: 4,
      customer_name: 'VietStar Logistics JSC',
      company_id: 2,
      company_name: 'Uy Vận',
      vehicle_plate: null,
      pickup_address: 'QB',
      delivery_address: 'HCM',
      note: '',
      total_receive_before_tax: 2000000.0,
      total_receive_after_tax: 2200000.0
    }
  ];

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock API responses
    apiService.get.mockImplementation((url) => {
      if (url.includes('/customers/get_basic')) {
        return Promise.resolve(mockCustomers);
      } else if (url.includes('/company/get_basic')) {
        return Promise.resolve(mockCompanies);
      } else if (url.includes('/reports/customer')) {
        return Promise.resolve(mockReportData);
      }
      return Promise.resolve([]);
    });
  });

  test('renders the report customer page with filters', async () => {
    render(
      <BrowserRouter>
        <ReportCustomer />
      </BrowserRouter>
    );

    // Check if the header is rendered
    expect(screen.getByText('Báo cáo khách hàng')).toBeInTheDocument();

    // Check if filters are rendered
    expect(screen.getByText('Công ty:')).toBeInTheDocument();
    expect(screen.getByText('Tháng/Năm:')).toBeInTheDocument();
    expect(screen.getByText('Khách hàng:')).toBeInTheDocument();

    // Wait for customers and companies to load
    await waitFor(() => {
      expect(apiService.get).toHaveBeenCalledWith(expect.stringContaining('/customers/get_basic'));
      expect(apiService.get).toHaveBeenCalledWith(expect.stringContaining('/company/get_basic'));
    });
  });

  test('fetches and displays report data when filter button is clicked', async () => {
    render(
      <BrowserRouter>
        <ReportCustomer />
      </BrowserRouter>
    );

    // Wait for customers and companies to load
    await waitFor(() => {
      expect(apiService.get).toHaveBeenCalledWith(expect.stringContaining('/customers/get_basic'));
      expect(apiService.get).toHaveBeenCalledWith(expect.stringContaining('/company/get_basic'));
    });

    // Click the filter button
    fireEvent.click(screen.getByText('Lọc'));

    // Wait for report data to load
    await waitFor(() => {
      expect(apiService.get).toHaveBeenCalledWith(expect.stringContaining('/reports/customer'));
    });

    // Check if report data is displayed
    await waitFor(() => {
      expect(screen.getByText('VietStar Logistics JSC')).toBeInTheDocument();
      expect(screen.getByText('Uy Vận')).toBeInTheDocument();
    });
  });
});
