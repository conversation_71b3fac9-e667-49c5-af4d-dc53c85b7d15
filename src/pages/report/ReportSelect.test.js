import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ReportSelect from './ReportSelect';

// Mock the useNavigate hook
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('ReportSelect Component', () => {
  beforeEach(() => {
    // Clear mock before each test
    mockNavigate.mockClear();
  });

  test('renders report selection cards', () => {
    render(
      <BrowserRouter>
        <ReportSelect />
      </BrowserRouter>
    );

    // Check if the header is rendered
    expect(screen.getByText('Chọn loại báo cáo')).toBeInTheDocument();

    // Check if all report type cards are rendered
    expect(screen.getByText('Báo cáo tổng hợp')).toBeInTheDocument();
    expect(screen.getByText('Tạo bảng kê khách hàng')).toBeInTheDocument();
    expect(screen.getByText('Tạo bảng kê nhà cung cấp')).toBeInTheDocument();
  });

  test('navigates to the correct report page when a card is clicked', () => {
    render(
      <BrowserRouter>
        <ReportSelect />
      </BrowserRouter>
    );

    // Click on the first report card
    fireEvent.click(screen.getByText('Báo cáo tổng hợp'));
    expect(mockNavigate).toHaveBeenCalledWith('/reports', { state: { tab: 'overview' } });

    // Click on the second report card
    fireEvent.click(screen.getByText('Tạo bảng kê khách hàng'));
    expect(mockNavigate).toHaveBeenCalledWith('/reports', { state: { tab: 'customer' } });

    // Click on the third report card
    fireEvent.click(screen.getByText('Tạo bảng kê nhà cung cấp'));
    expect(mockNavigate).toHaveBeenCalledWith('/reports', { state: { tab: 'supplier' } });
  });
});
