import React, { useState, useEffect, useRef } from "react";
import {
  FiEdit,
  FiTrash2,
  FiUserPlus,
  FiUserCheck,
  FiChevronUp,
  FiChevronDown,
  FiSearch,
  FiAlertCircle,
} from "react-icons/fi";
import "../styles/Suppliers.css";
import apiService from "../services/api.service";
import { ENDPOINTS } from "../config/api.config";

const Suppliers = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [filteredSuppliers, setFilteredSuppliers] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(() => {
    const savedItemsPerPage = localStorage.getItem("itemsPerPage");
    return savedItemsPerPage ? parseInt(savedItemsPerPage, 10) : 10;
  });
  const [sortConfig, setSortConfig] = useState({
    key: "updated_at",
    direction: "descending",
  });
  const [filters, setFilters] = useState({
    role: "all",
    status: "all",
    company: "all",
    supplier_type: "all",
    sort: "newest",
    search: "",
  });
  const [modalError, setModalError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [companies, setCompanies] = useState([]);
  const companySearchRef = useRef(null);
  const phoneNumberInputRef = useRef(null);
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState("view"); // 'view', 'edit', 'add', 'delete'
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [columns, setColumns] = useState([]);
  const initialLoadDone = useRef(false);

  // Role options
  const roleOptions = [
    { id: "all", name: "All" },
    { id: "admin", name: "Admin" },
    { id: "approval", name: "Approval" },
    { id: "supplier", name: "Supplier" },
  ];

  // Status options
  const statusOptions = [
    { id: "all", name: "All" },
    { id: "active", name: "Active" },
    { id: "inactive", name: "Inactive" },
    { id: "remove", name: "Removed" },
  ];

  // Supplier type options
  const supplierTypeOptions = [
    { id: "all", name: "All" },
    { id: "business", name: "Business" },
    { id: "individual", name: "Individual" },
  ];

  // Fetch suppliers from API
  const fetchSuppliers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // console.log("Fetching suppliers...");
      const response = await apiService.get(ENDPOINTS.SUPPLIERS.GET_ALL);
      // console.log("Suppliers fetched successfully:", response);
      setSuppliers(response);

      // Extract columns from the first supplier object
      if (response && response.length > 0) {
        const firstSupplier = response[0];
        const extractedColumns = Object.keys(firstSupplier)
          .filter(key => key !== 'created_at' && key !== 'updated_at') // Filter out created_at and updated_at
          .map((key) => ({
            key,
            label: formatColumnLabel(key),
          }));
        setColumns(extractedColumns);
      }
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      setError("Unable to load supplier data. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  // Format column label from key
  const formatColumnLabel = (key) => {
    // Special case for company_id
    if (key === "company_id") return "Company";

    // Convert snake_case or camelCase to Title Case
    return key
      .replace(/_/g, " ")
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  };

  // Get companies from localStorage
  const fetchCompanies = () => {
    try {
      const companyInfoStr = localStorage.getItem('companyInfo');
      if (companyInfoStr) {
        const companyInfo = JSON.parse(companyInfoStr);
        if (Array.isArray(companyInfo)) {
          setCompanies(companyInfo);
        } else {
          // If it's a single object, convert to array
          setCompanies([companyInfo]);
        }
      }
    } catch (error) {
      console.error("Error parsing company info:", error);
    }
  };

  // Fetch suppliers and companies when component mounts
  useEffect(() => {
    if (!initialLoadDone.current) {
      fetchSuppliers();
      fetchCompanies();
      initialLoadDone.current = true;
    }
  }, []);

  // Focus supplier_code input when modal opens in add mode
  useEffect(() => {
    if (isModalOpen && modalMode === "add" && phoneNumberInputRef.current) {
      setTimeout(() => {
        phoneNumberInputRef.current.focus();
      }, 100);
    }
  }, [isModalOpen, modalMode]);

  // Handle click outside for company dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (companySearchRef.current && !companySearchRef.current.contains(event.target)) {
        setSelectedSupplier(prev => prev ? { ...prev, showCompanyDropdown: false } : prev);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [companySearchRef]);

  // Handle filtering and sorting
  useEffect(() => {
    let result = [...suppliers];

    // Apply role filter
    if (filters.role !== "all") {
      result = result.filter((supplier) => supplier.role === filters.role);
    }

    // Apply status filter
    if (filters.status !== "all") {
      result = result.filter((supplier) => supplier.status === filters.status);
    }

    // Apply supplier_type filter
    if (filters.supplier_type !== "all") {
      result = result.filter((supplier) => supplier.supplier_type === filters.supplier_type);
    }

    // Apply company filter
    if (filters.company !== "all") {
      result = result.filter((supplier) => supplier.company_id === parseInt(filters.company));
    }

    // Apply search
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      result = result.filter((supplier) =>
        Object.values(supplier).some(
          (value) =>
            value &&
            typeof value === "string" &&
            value.toLowerCase().includes(searchLower)
        )
      );
    }

    // Apply sorting
    if (sortConfig.key) {
      result.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? 1 : -1;
        }
        return 0;
      });
    }

    setFilteredSuppliers(result);
  }, [suppliers, filters, sortConfig]);

  // Handle sorting when clicking on table header
  const requestSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  // Display sort icon
  const getSortIcon = (columnName) => {
    if (sortConfig.key !== columnName) {
      return <span className="sort-icon sort-inactive">⇅</span>;
    }

    return sortConfig.direction === "ascending" ? (
      <FiChevronUp className="sort-icon" />
    ) : (
      <FiChevronDown className="sort-icon" />
    );
  };

  // Handle pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredSuppliers.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredSuppliers.length / itemsPerPage);

  // Handle page navigation
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  const nextPage = () =>
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage((prev) => Math.max(prev - 1, 1));
  const firstPage = () => setCurrentPage(1);
  const lastPage = () => setCurrentPage(totalPages);

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;

    // Handle special sort logic
    if (name === 'sort') {
      if (value === 'newest') {
        setSortConfig({ key: 'updated_at', direction: 'descending' });
      } else if (value === 'oldest') {
        setSortConfig({ key: 'updated_at', direction: 'ascending' });
      }
    }

    setFilters((prev) => ({ ...prev, [name]: value }));
    setCurrentPage(1); // Reset to page 1 when filter changes
  };

  // Xử lý thay đổi số lượng items mỗi trang
  const handleItemsPerPageChange = (e) => {
    const value = parseInt(e.target.value, 10);
    setItemsPerPage(value);
    localStorage.setItem("itemsPerPage", value.toString());
    // Reset về trang 1 khi thay đổi số lượng items
    setCurrentPage(1);
    localStorage.setItem("currentPage", "1");
  };

  // Handle search
  const handleSearch = (e) => {
    const searchValue = e.target.value.toLowerCase();
    setFilters((prev) => ({ ...prev, search: searchValue }));
    setCurrentPage(1);

    // Filter suppliers based on search value across all columns
    const filtered = suppliers.filter((supplier) => {
      return Object.values(supplier).some((value) => {
        // Convert value to string and check if it includes search value
        const stringValue = String(value).toLowerCase();
        return stringValue.includes(searchValue);
      });
    });

    setFilteredSuppliers(filtered);
  };

  // Validate form fields before submission
  const validateForm = () => {
    const requiredFields = ['supplier_code', 'company_name', 'company_id', 'supplier_name'];

    const missingFields = [];

    requiredFields.forEach(field => {
      if (!selectedSupplier[field]) {
        missingFields.push(formatColumnLabel(field));
      }
    });

    if (missingFields.length > 0) {
      setModalError(`Please fill in all required fields: ${missingFields.join(', ')}`);
      return false;
    }

    setModalError(null);
    return true;
  };

  // Handle opening modal
  const openModal = (supplier, mode) => {
    // Clear any previous error messages
    setModalError(null);

    if (mode === "add") {
      setSelectedSupplier({
        id: suppliers.length > 0 ? Math.max(...suppliers.map((u) => u.id)) + 1 : 1000,
        supplier_code: "",
        supplier_name: "",
        company_name: "",
        company_id: "",
        // mst: "",
        address: "",
        supplier_type: "individual", // Default supplier type
        companySearch: "", // For company autocomplete
        showCompanyDropdown: false
      });
    } else {
      // For edit mode, find the company name
      let companyName = "";
      try {
        const companyInfoStr = localStorage.getItem('companyInfo');
        if (companyInfoStr && supplier.company_id) {
          const companyInfo = JSON.parse(companyInfoStr);
          const company = Array.isArray(companyInfo)
            ? companyInfo.find(c => c.id === supplier.company_id)
            : (companyInfo.id === supplier.company_id ? companyInfo : null);

          companyName = company ? company.company_name : "";
        }
      } catch (error) {
        console.error('Error parsing company info:', error);
      }

      // Make sure company_name is set correctly
      setSelectedSupplier({
        ...supplier,
        companySearch: companyName,
        company_name: companyName, // Ensure company_name is set to match the company
        showCompanyDropdown: false
      });
    }
    setModalMode(mode);
    setIsModalOpen(true);
  };

  // Handle closing modal
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedSupplier(null);
    setModalError(null);
  };

  // Handle deleting supplier
  const handleDeleteSupplier = async () => {
    if (selectedSupplier) {
      try {
        await apiService.delete(ENDPOINTS.SUPPLIERS.DELETE(selectedSupplier.id));
        // Update state directly instead of fetching
        setSuppliers((prevSuppliers) =>
          prevSuppliers.filter((supplier) => supplier.id !== selectedSupplier.id)
        );
        closeModal();
      } catch (error) {
        console.error("Error deleting supplier:", error);
        setError("Unable to delete supplier. Please try again later.");
      }
    }
  };

  // Handle adding/updating supplier
  const handleSaveSupplier = async () => {
    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    try {
      // Prepare request body with only the required fields
      const supplierData = {
        supplier_code: selectedSupplier.supplier_code,
        supplier_name: selectedSupplier.supplier_name,
        company_id: selectedSupplier.company_id,
        // mst: selectedSupplier.mst || "",
        address: selectedSupplier.address || "",
        supplier_type: selectedSupplier.supplier_type || "individual"
      };

      if (modalMode === "add") {
        await apiService.post(
          ENDPOINTS.SUPPLIERS.CREATE,
          supplierData
        );

        // Hiển thị thông báo thành công
        setSuccessMessage(`Supplier ${supplierData.supplier_name} has been created successfully!`);
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000); // Tự động ẩn thông báo sau 5 giây

        // Refresh supplier data after creation
        await fetchSuppliers();
        closeModal();
      } else if (modalMode === "edit") {
        // Include id for update
        supplierData.id = selectedSupplier.id;

        await apiService.post(ENDPOINTS.SUPPLIERS.UPDATE, supplierData);

        // Hiển thị thông báo thành công
        setSuccessMessage(`Supplier ${supplierData.supplier_name} has been updated successfully!`);
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000); // Tự động ẩn thông báo sau 5 giây

        // Refresh supplier data after modification
        await fetchSuppliers();
        closeModal();
      }
    } catch (error) {
      console.error("Error saving supplier:", error);

      // Hiển thị lỗi từ API trong modal
      if (error.response && error.response.data) {
        // Nếu API trả về thông báo lỗi cụ thể
        if (error.response.data.detail) {
          setModalError(error.response.data.detail);
        } else if (error.response.data.message) {
          setModalError(error.response.data.message);
        } else if (typeof error.response.data === 'string') {
          setModalError(error.response.data);
        } else {
          setModalError("Không thể lưu thông tin nhà cung cấp. Vui lòng thử lại sau.");
        }
      } else if (error.message) {
        // Nếu có thông báo lỗi từ JavaScript
        setModalError(error.message);
      } else {
        // Thông báo lỗi mặc định
        setModalError("Không thể lưu thông tin nhà cung cấp. Vui lòng thử lại sau.");
      }
    }
  };

  // Handle supplier information changes
  const handleSupplierChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Handle checkbox fields
    if (type === "checkbox") {
      setSelectedSupplier((prev) => ({ ...prev, [name]: checked }));
    }
    // Handle regular input fields
    else {
      setSelectedSupplier((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);

    // Format as YYYY-MM-DD HH:MM:SS
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  // Format cell value based on column type
  const formatCellValue = (key, value) => {
    if (value === null || value === undefined) return "";

    // Format status
    if (key === "status") {
      return (
        <span className={`status-badge ${value}`}>
          {value === "active"
            ? "Active"
            : value === "inactive"
            ? "Inactive"
            : value === "remove"
            ? "Removed"
            : value}
        </span>
      );
    }

    // Format dates
    if (
      key.includes("date") ||
      key.includes("created") ||
      key.includes("updated")
    ) {
      return formatDate(value);
    }

    // Format boolean values
    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }

    // Format role
    if (key === "role") {
      return (
        <span className={`role-badge ${value}`}>
          {value === "admin"
            ? "Admin"
            : value === "approval"
            ? "Approval"
            : value === "supplier"
            ? "Supplier"
            : value}
        </span>
      );
    }

    // Format company_id to display company name
    if (key === "company_id" && value) {
      try {
        const companyInfoStr = localStorage.getItem('companyInfo');
        if (companyInfoStr) {
          const companyInfo = JSON.parse(companyInfoStr);
          const company = Array.isArray(companyInfo)
            ? companyInfo.find(c => c.id === value)
            : (companyInfo.id === value ? companyInfo : null);

          return company ? company.company_name : value;
        }
      } catch (error) {
        console.error('Error parsing company info:', error);
      }
      return value;
    }

    // Default: return as is
    return value;
  };

  return (
    <div className="suppliers-container">
      <div className="suppliers-header">
        <div className="suppliers-filters">
          {/* <div className="filter-group">
            <label>Role:</label>
            <select
              name="role"
              value={filters.role}
              onChange={handleFilterChange}
            >
              {roleOptions.map((role) => (
                <option key={role.id} value={role.id}>
                  {role.name}
                </option>
              ))}
            </select>
          </div>
          <div className="filter-group">
            <label>Status:</label>
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
            >
              {statusOptions.map((status) => (
                <option key={status.id} value={status.id}>
                  {status.name}
                </option>
              ))}
            </select>
          </div> */}
          <div className="filter-group">
            <label>Company:</label>
            <select
              name="company"
              value={filters.company}
              onChange={handleFilterChange}
            >
              <option value="all">All Companies</option>
              {companies.map((company) => (
                <option key={company.id} value={company.id}>
                  {company.company_name}
                </option>
              ))}
            </select>
          </div>
          <div className="filter-group">
            <label>Supplier Type:</label>
            <select
              name="supplier_type"
              value={filters.supplier_type}
              onChange={handleFilterChange}
            >
              {supplierTypeOptions.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>
          <div className="filter-group">
            <label>Sort:</label>
            <select
              name="sort"
              value={filters.sort}
              onChange={handleFilterChange}
            >
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
            </select>
          </div>
          <div className="filter-group">
            <label>Show:</label>
            <select value={itemsPerPage} onChange={handleItemsPerPageChange}>
              <option value="5">5 mục</option>
              <option value="10">10 mục</option>
              <option value="20">20 mục</option>
              <option value="50">50 mục</option>
            </select>
          </div>
        </div>
        <div className="suppliers-actions">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search suppliers..."
              value={filters.search}
              onChange={handleSearch}
            />
            <FiSearch className="search-icon" />
          </div>
          <button
            className="btn-primary"
            onClick={() => openModal(null, "add")}
          >
            <FiUserPlus className="btn-icon-left" />
            Add Supplier
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <FiAlertCircle />
          <span>{error}</span>
        </div>
      )}

      {successMessage && (
        <div className="success-message">
          <FiUserCheck />
          <span>{successMessage}</span>
        </div>
      )}

      {isLoading ? (
        <div className="loading-spinner"></div>
      ) : (
        <div className="suppliers-table-container">
          <table className="suppliers-table">
            <thead>
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    onClick={() => requestSort(column.key)}
                    className={sortConfig.key === column.key ? "sorted" : ""}
                  >
                    {column.label} {getSortIcon(column.key)}
                  </th>
                ))}
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentItems.map((supplier) => (
                <tr key={supplier.id}>
                  {columns.map((column) => (
                    <td key={`${supplier.id}-${column.key}`}>
                      {formatCellValue(column.key, supplier[column.key])}
                    </td>
                  ))}
                  <td>
                    <div className="action-buttons">
                      <button
                        className="btn-icon edit"
                        onClick={() => openModal(supplier, "edit")}
                        title="Edit"
                      >
                        <FiEdit />
                      </button>
                      {/* <button
                        className="btn-icon delete"
                        onClick={() => openModal(supplier, "delete")}
                        disabled={true} // tạm thời ko cho xoá supplier
                        title="Delete"
                      >
                        <FiTrash2 />
                      </button> */}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="pagination-container">
        <div className="pagination-info">
          Showing {indexOfFirstItem + 1} -{" "}
          {Math.min(indexOfLastItem, filteredSuppliers.length)} of{" "}
          {filteredSuppliers.length} suppliers
        </div>
        <div className="pagination">
          <button
            className="pagination-btn"
            onClick={firstPage}
            disabled={currentPage === 1}
          >
            «
          </button>
          <button
            className="pagination-btn"
            onClick={prevPage}
            disabled={currentPage === 1}
          >
            ‹
          </button>
          {[...Array(Math.min(5, totalPages))].map((_, index) => {
            let pageNumber;
            if (totalPages <= 5) {
              pageNumber = index + 1;
            } else if (currentPage <= 3) {
              pageNumber = index + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNumber = totalPages - 4 + index;
            } else {
              pageNumber = currentPage - 2 + index;
            }

            return (
              <button
                key={pageNumber}
                className={`pagination-btn ${
                  currentPage === pageNumber ? "active" : ""
                }`}
                onClick={() => paginate(pageNumber)}
              >
                {pageNumber}
              </button>
            );
          })}
          <button
            className="pagination-btn"
            onClick={nextPage}
            disabled={currentPage === totalPages}
          >
            ›
          </button>
          <button
            className="pagination-btn"
            onClick={lastPage}
            disabled={currentPage === totalPages}
          >
            »
          </button>
        </div>
      </div>

      {isModalOpen && selectedSupplier && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-container" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>
                {modalMode === "add" && "Add New Supplier"}
                {modalMode === "edit" &&
                  `Edit Supplier: ${
                    selectedSupplier.company_name || selectedSupplier.supplier_code
                  }`}
                {modalMode === "delete" &&
                  `Delete Supplier: ${
                    selectedSupplier.company_name || selectedSupplier.supplier_code
                  }`}
              </h3>
              <button className="modal-close" onClick={closeModal}>
                ×
              </button>
            </div>
            <div className="modal-body">
              {modalError && (
                <div className="modal-error-message">
                  <FiAlertCircle className="error-icon" />
                  <span>{modalError}</span>
                </div>
              )}
              {modalMode === "delete" ? (
                <div className="delete-confirmation">
                  <p>Are you sure you want to delete this supplier?</p>
                  <div className="modal-actions">
                    <button className="btn-secondary" onClick={closeModal}>
                      Cancel
                    </button>
                    <button className="btn-danger" onClick={handleDeleteSupplier}>
                      Delete
                    </button>
                  </div>
                </div>
              ) : (
                <div className="supplier-form">
                  {columns.map((column) => {
                    // Skip certain fields that shouldn't be editable
                    if (
                      column.key === "id" ||
                      column.key === "created_at" ||
                      column.key === "updated_at"
                    ) {
                      return null;
                    }

                    // Determine input type based on column key
                    let inputType = "text";
                    if (column.key === "email") inputType = "email";
                    if (column.key === "phone") inputType = "tel";
                    if (column.key === "password") inputType = "password";

                    // Special handling for status field
                    if (column.key === "status") {
                      return (
                        <div className="form-group" key={column.key}>
                          <label>{column.label}:</label>
                          <select
                            name={column.key}
                            value={selectedSupplier[column.key] || ""}
                            onChange={handleSupplierChange}
                          >
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="remove">Removed</option>
                          </select>
                        </div>
                      );
                    }

                    // Special handling for company_id field
                    if (column.key === "company_id") {
                      return (
                        <div className="form-group" key={column.key}>
                          <label>{column.label}:<span className="required-field">*</span></label>
                          <div style={{ position: "relative" }} ref={companySearchRef}>
                            <input
                              type="text"
                              value={selectedSupplier.companySearch || ""}
                              onChange={(e) => {
                                const searchValue = e.target.value;
                                setSelectedSupplier(prev => ({
                                  ...prev,
                                  companySearch: searchValue,
                                  showCompanyDropdown: true
                                }));
                              }}
                              onFocus={() => {
                                setSelectedSupplier(prev => ({
                                  ...prev,
                                  showCompanyDropdown: true
                                }));
                              }}
                              placeholder="Type to search company..."
                              required
                            />
                            {selectedSupplier.showCompanyDropdown && (
                              <div style={{
                                position: "absolute",
                                top: "100%",
                                left: 0,
                                right: 0,
                                maxHeight: "200px",
                                overflowY: "auto",
                                backgroundColor: "white",
                                border: "1px solid #ddd",
                                borderRadius: "4px",
                                zIndex: 10,
                                boxShadow: "0 4px 8px rgba(0,0,0,0.1)"
                              }}>
                                {companies
                                  .filter(company => {
                                    if (!selectedSupplier.companySearch) return true; // Show all when no search text
                                    const companyName = company.company_name || "";
                                    return companyName.toLowerCase().includes(selectedSupplier.companySearch.toLowerCase());
                                  })
                                  .map(company => (
                                    <div
                                      key={company.id}
                                      onClick={() => {
                                        setSelectedSupplier(prev => ({
                                          ...prev,
                                          company_id: company.id,
                                          company_name: company.company_name,
                                          companySearch: company.company_name,
                                          showCompanyDropdown: false
                                        }));
                                      }}
                                      style={{
                                        padding: "8px 12px",
                                        cursor: "pointer",
                                        borderBottom: "1px solid #eee",
                                        backgroundColor: selectedSupplier.company_id === company.id ? "#f0f7ff" : "white"
                                      }}
                                      onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#f5f5f5"}
                                      onMouseOut={(e) => e.currentTarget.style.backgroundColor = selectedSupplier.company_id === company.id ? "#f0f7ff" : "white"}
                                    >
                                      {company.company_name}
                                    </div>
                                  ))
                                }
                                {companies.filter(company => {
                                  if (!selectedSupplier.companySearch) return true;
                                  const companyName = company.company_name || "";
                                  return companyName.toLowerCase().includes(selectedSupplier.companySearch.toLowerCase());
                                }).length === 0 && (
                                  <div style={{ padding: "8px 12px", color: "#999" }}>
                                    No companies found
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    }

                    // Special handling for role field
                    if (column.key === "role") {
                      return (
                        <div className="form-group" key={column.key}>
                          <label>{column.label}:</label>
                          <select
                            name={column.key}
                            value={selectedSupplier[column.key] || "supplier"}
                            onChange={handleSupplierChange}
                          >
                            <option value="admin">Admin</option>
                            <option value="approval">Approval</option>
                            <option value="supplier">Supplier</option>
                          </select>
                        </div>
                      );
                    }

                    // Special handling for supplier_type field
                    if (column.key === "supplier_type") {
                      return (
                        <div className="form-group" key={column.key}>
                          <label>{column.label}:</label>
                          <div className="radio-options" style={{ display: 'flex', gap: '20px', marginTop: '5px' }}>
                            <label className="radio-label" style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                              <input
                                type="radio"
                                name={column.key}
                                value="individual"
                                checked={selectedSupplier[column.key] === "individual" || !selectedSupplier[column.key]}
                                onChange={handleSupplierChange}
                                style={{ marginRight: '5px' }}
                              />
                              <span>Individual</span>
                            </label>
                            <label className="radio-label" style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                              <input
                                type="radio"
                                name={column.key}
                                value="business"
                                checked={selectedSupplier[column.key] === "business"}
                                onChange={handleSupplierChange}
                                style={{ marginRight: '5px' }}
                              />
                              <span>Business</span>
                            </label>
                          </div>
                        </div>
                      );
                    }

                    // Default input field
                    return (
                      <div className="form-group" key={column.key}>
                        <label>
                          {column.label}:
                          {(column.key === "supplier_code" || column.key === "company_name" || column.key === "supplier_name") &&
                            <span className="required-field">*</span>}
                        </label>
                        <input
                          type={inputType}
                          name={column.key}
                          value={selectedSupplier[column.key] || ""}
                          onChange={handleSupplierChange}
                          required={["supplier_code", "company_name", "supplier_name"].includes(column.key)}
                          disabled={column.key === "supplier_code" && modalMode === "edit"}
                          className={column.key === "supplier_code" && modalMode === "edit" ? "disabled-field" : ""}
                          ref={column.key === "supplier_code" ? phoneNumberInputRef : null}
                          placeholder={column.key === "supplier_code" ? "Nếu nhà cung cấp cá nhân: nhập SĐT, doanh nghiệp: nhập MST" : ""}
                        />
                      </div>
                    );
                  })}
                  <div className="modal-actions">
                    <button className="btn-secondary" onClick={closeModal}>
                      Cancel
                    </button>
                    <button className="btn-primary" onClick={handleSaveSupplier}>
                      {modalMode === "add" ? "Add" : "Save Changes"}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Suppliers;
