import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, FiFilter, FiCalendar, FiUser, FiBarChart2,
  FiTruck, FiDollarSign, FiPieChart, FiUsers, FiRefreshCw, FiList
} from 'react-icons/fi';
import {
  BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell, ComposedChart
} from 'recharts';
import apiService from '../services/api.service';
import { ENDPOINTS } from '../config/api.config';
import '../styles/Reports.css';

const Reports = () => {
  // State cho main tabs (overview/customer/supplier)
  const [mainTab, setMainTab] = useState('overview'); // 'overview', 'customer', 'supplier'

  // State cho form lọc
  const [dateRange, setDateRange] = useState(() => {
    // Tính ngày đầu tiên của tháng hiện tại
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

    // Định dạng ngày tháng theo YYYY-MM-DD
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    return {
      startDate: formatDate(firstDay),
      endDate: formatDate(today)
    };
  });

  // State cho các bộ lọc
  const [selectedCustomerId, setSelectedCustomerId] = useState('');
  const [selectedSupplierId, setSelectedSupplierId] = useState('');
  const [selectedSaleStaffId, setSelectedSaleStaffId] = useState('');
  const [selectedPricingStaffId, setSelectedPricingStaffId] = useState('');
  const [selectedDispatchStaffId, setSelectedDispatchStaffId] = useState('');
  const [selectedCompanyId, setSelectedCompanyId] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // State for report view type
  const [reportViewType, setReportViewType] = useState('summary'); // 'summary', 'detail', 'trend'

  // State for revenue chart time period
  const [revenuePeriod, setRevenuePeriod] = useState('month'); // 'day', 'month', 'quarter'

  // State for performance chart
  const [performancePeriod, setPerformancePeriod] = useState('month'); // 'month', 'quarter'

  // State for revenue and profit chart time period
  const [revenueProfitPeriod, setRevenueProfitPeriod] = useState('day'); // 'day', 'week', 'month'

  // State for customer revenue chart time period
  const [customerRevenuePeriod, setCustomerRevenuePeriod] = useState('day'); // 'day', 'week', 'month'

  // State for filter visibility
  const [showFilters, setShowFilters] = useState(true);

  // State for status counts (needed for filtering)
  // eslint-disable-next-line no-unused-vars
  const [statusCounts, setStatusCounts] = useState({});

  // State cho danh sách trạng thái
  const [statuses] = useState([
    { id: 'initial', name: 'Khởi tạo' },
    { id: 'approved', name: 'Đã duyệt' },
    { id: 'delivered', name: 'Đã giao hàng' },
    { id: 'customerpaid', name: 'Đã thu tiền khách hàng' },
    { id: 'supplierpaid', name: 'Đã trả tiền nhà cung cấp' },
    { id: 'completed', name: 'Hoàn thành' },
    { id: 'processing', name: 'Đang xử lý' },
    { id: 'cancelled', name: 'Đã hủy' }
  ]);
  const [companies, setCompanies] = useState([]);
  const [isCompanyLoading, setIsCompanyLoading] = useState(false);

  // State cho dữ liệu danh sách
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [staffs, setStaffs] = useState([]);

  // State cho trạng thái loading
  const [isCustomerLoading, setIsCustomerLoading] = useState(false);
  const [isSupplierLoading, setIsSupplierLoading] = useState(false);
  const [isStaffLoading, setIsStaffLoading] = useState(false);

  // State cho dữ liệu báo cáo
  const [customerReportData, setCustomerReportData] = useState([]);
  const [supplierReportData, setSupplierReportData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasAppliedFilters, setHasAppliedFilters] = useState(false);

  // State cho phân bố trạng thái ticket
  const [statusDistribution, setStatusDistribution] = useState({});

  // State cho dữ liệu gốc từ API
  const [rawReportData, setRawReportData] = useState([]);

  // Fetch danh sách khách hàng, nhà cung cấp, nhân viên và công ty
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch khách hàng
        setIsCustomerLoading(true);
        const customerResponse = await apiService.get(ENDPOINTS.CUSTOMERS.GET_BASIC);

        if (customerResponse && customerResponse.customers) {
          setCustomers(customerResponse.customers);
        } else if (customerResponse && Array.isArray(customerResponse)) {
          setCustomers(customerResponse);
        } else if (customerResponse && customerResponse.data) {
          setCustomers(customerResponse.data);
        } else {
          console.error('Unexpected customer response structure:', customerResponse);
        }
        setIsCustomerLoading(false);

        // Fetch nhà cung cấp
        setIsSupplierLoading(true);
        const supplierResponse = await apiService.get(ENDPOINTS.SUPPLIERS.GET_BASIC);

        if (supplierResponse && supplierResponse.suppliers) {
          setSuppliers(supplierResponse.suppliers);
        } else if (supplierResponse && Array.isArray(supplierResponse)) {
          setSuppliers(supplierResponse);
        } else if (supplierResponse && supplierResponse.data) {
          setSuppliers(supplierResponse.data);
        } else {
          console.error('Unexpected supplier response structure:', supplierResponse);
        }
        setIsSupplierLoading(false);

        // Fetch nhân viên
        setIsStaffLoading(true);
        const staffResponse = await apiService.get(ENDPOINTS.USERS.GET_BASIC);

        if (staffResponse && staffResponse.users) {
          setStaffs(staffResponse.users);
        } else if (staffResponse && Array.isArray(staffResponse)) {
          setStaffs(staffResponse);
        } else if (staffResponse && staffResponse.data) {
          setStaffs(staffResponse.data);
        } else {
          console.error('Unexpected staff response structure:', staffResponse);
        }
        setIsStaffLoading(false);

        // Fetch công ty
        setIsCompanyLoading(true);
        const companyResponse = await apiService.get(ENDPOINTS.COMPANY.GET_BASIC);

        if (companyResponse && companyResponse.companies) {
          setCompanies(companyResponse.companies);
        } else if (companyResponse && Array.isArray(companyResponse)) {
          setCompanies(companyResponse);
        } else if (companyResponse && companyResponse.data) {
          setCompanies(companyResponse.data);
        } else {
          console.error('Unexpected company response structure:', companyResponse);
        }
        setIsCompanyLoading(false);

      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Không thể tải dữ liệu. Vui lòng thử lại sau.');
        setIsCustomerLoading(false);
        setIsSupplierLoading(false);
        setIsStaffLoading(false);
        setIsCompanyLoading(false);
      }
    };

    fetchData();
  }, []);

  // Hàm xử lý khi thay đổi form lọc
  const handleDateChange = (e) => {
    const { name, value } = e.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCustomerChange = (e) => {
    setSelectedCustomerId(e.target.value);
  };

  const handleSupplierChange = (e) => {
    setSelectedSupplierId(e.target.value);
  };

  const handleSaleStaffChange = (e) => {
    setSelectedSaleStaffId(e.target.value);
  };

  const handlePricingStaffChange = (e) => {
    setSelectedPricingStaffId(e.target.value);
  };

  const handleDispatchStaffChange = (e) => {
    setSelectedDispatchStaffId(e.target.value);
  };

  const handleCompanyChange = (e) => {
    setSelectedCompanyId(e.target.value);
  };

  // Status is now handled by the status count cards

  // Hàm lấy dữ liệu báo cáo từ API
  const fetchReportData = async () => {
    setIsLoading(true);
    setError(null);

    // Initialize status counts
    const initialStatusCounts = {};
    const initialStatusDistribution = {};
    statuses.forEach(status => {
      initialStatusCounts[status.id] = 0;
      initialStatusDistribution[status.id] = 0;
    });
    setStatusCounts(initialStatusCounts);
    setStatusDistribution(initialStatusDistribution);

    try {
      // Chuẩn bị tham số cho API
      const params = {
        start_date: dateRange.startDate,
        end_date: dateRange.endDate,
        company_id: selectedCompanyId
      };

      // Thêm các tham số lọc nếu được chọn
      if (selectedCustomerId) {
        params.customer_id = selectedCustomerId;
      }

      if (selectedSupplierId) {
        params.supplier_id = selectedSupplierId;
      }

      if (selectedSaleStaffId) {
        params.sale_staff_id = selectedSaleStaffId;
      }

      if (selectedPricingStaffId) {
        params.pricing_staff_id = selectedPricingStaffId;
      }

      if (selectedDispatchStaffId) {
        params.dispatch_staff_id = selectedDispatchStaffId;
      }

      if (selectedStatus) {
        params.status = selectedStatus;
      }

      // Gọi API
      const response = await apiService.get(ENDPOINTS.REPORT.GET(params));

      // Kiểm tra nếu response là mảng hoặc có thuộc tính data là mảng
      const responseData = Array.isArray(response) ? response : (response.data || []);

      // Lưu trữ dữ liệu gốc từ API để sử dụng cho biểu đồ
      setRawReportData(responseData);

      if (responseData.length > 0) {
        // Chuyển đổi dữ liệu từ API sang định dạng hiển thị
        const formattedData = responseData.map(item => {
          // Tính toán số lượng ticket (nếu có)
          const totalTickets = 1; // Mỗi dòng là một ticket

          // Tính toán trạng thái ticket
          const isCompleted = item.current_status === 'completed' || item.current_status === 'supplierpaid';
          const completedTickets = isCompleted ? 1 : 0;
          const pendingTickets = isCompleted ? 0 : 1;

          // Tính toán status counts
          const status = item.current_status || 'initial';
          initialStatusCounts[status] = (initialStatusCounts[status] || 0) + 1;
          initialStatusDistribution[status] = (initialStatusDistribution[status] || 0) + 1;

          // Tính toán doanh thu, chi phí và lợi nhuận
          const totalRevenue = item.total_receive_after_tax || 0;
          const totalCost = item.total_pay_after_tax || 0;
          const profit = totalRevenue - totalCost;

          return {
            customerId: item.customer_id,
            customerName: item.customer_name,
            supplierId: item.supplier_id,
            supplierName: item.supplier_name,
            totalTickets,
            completedTickets,
            pendingTickets,
            totalRevenue,
            totalCost,
            profit,
            request_date: item.request_date,
            created_at: item.created_at,
            current_status: status
          };
        });

        // Nhóm dữ liệu theo khách hàng
        const customerMap = new Map();
        const supplierMap = new Map();

        formattedData.forEach(item => {
          // Xử lý dữ liệu khách hàng
          if (item.customerId) {
            if (customerMap.has(item.customerId)) {
              const existingData = customerMap.get(item.customerId);
              customerMap.set(item.customerId, {
                ...existingData,
                totalTickets: existingData.totalTickets + item.totalTickets,
                completedTickets: existingData.completedTickets + item.completedTickets,
                pendingTickets: existingData.pendingTickets + item.pendingTickets,
                totalRevenue: existingData.totalRevenue + item.totalRevenue,
                totalCost: existingData.totalCost + item.totalCost,
                profit: existingData.profit + item.profit
              });
            } else {
              customerMap.set(item.customerId, {
                customerId: item.customerId,
                customerName: item.customerName,
                totalTickets: item.totalTickets,
                completedTickets: item.completedTickets,
                pendingTickets: item.pendingTickets,
                totalRevenue: item.totalRevenue,
                totalCost: item.totalCost,
                profit: item.profit
              });
            }
          }

          // Xử lý dữ liệu nhà cung cấp
          if (item.supplierId) {
            if (supplierMap.has(item.supplierId)) {
              const existingData = supplierMap.get(item.supplierId);
              supplierMap.set(item.supplierId, {
                ...existingData,
                totalTickets: existingData.totalTickets + item.totalTickets,
                completedTickets: existingData.completedTickets + item.completedTickets,
                pendingTickets: existingData.pendingTickets + item.pendingTickets,
                totalRevenue: existingData.totalRevenue + item.totalRevenue,
                totalCost: existingData.totalCost + item.totalCost,
                profit: existingData.profit + item.profit
              });
            } else {
              supplierMap.set(item.supplierId, {
                supplierId: item.supplierId,
                supplierName: item.supplierName,
                totalTickets: item.totalTickets,
                completedTickets: item.completedTickets,
                pendingTickets: item.pendingTickets,
                totalRevenue: item.totalRevenue,
                totalCost: item.totalCost,
                profit: item.profit
              });
            }
          }
        });

        // Chuyển Map thành mảng
        const customerData = Array.from(customerMap.values());
        const supplierData = Array.from(supplierMap.values());

        setCustomerReportData(customerData);
        setSupplierReportData(supplierData);
        setStatusCounts(initialStatusCounts);
        setStatusDistribution(initialStatusDistribution);
      } else {
        // Nếu API trả về mảng rỗng, hiển thị thông báo không có dữ liệu
        console.log('API returned empty data');
        // Đặt dữ liệu về mảng rỗng
        setCustomerReportData([]);
        setSupplierReportData([]);
        setStatusCounts({});
        setStatusDistribution({});
      }
    } catch (err) {
      console.error('Error fetching report data:', err);
      setError('Không thể tải dữ liệu báo cáo. Vui lòng thử lại sau.');
      // Đặt dữ liệu về mảng rỗng khi gặp lỗi
      setCustomerReportData([]);
      setSupplierReportData([]);
      setStatusCounts({});
      setStatusDistribution({});
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm tạo dữ liệu mẫu đã bị xóa - không còn sử dụng dữ liệu mẫu

  // Hàm xử lý khi submit form
  const handleSubmit = (e) => {
    e.preventDefault();
    // Reset status counts before fetching new data
    setStatusCounts({});
    setStatusDistribution({});
    setHasAppliedFilters(true);
    fetchReportData();
  };

  // Hàm định dạng số tiền
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount);
  };

  // Hàm xuất báo cáo ra Excel
  const exportToExcel = () => {
    alert('Chức năng xuất Excel sẽ được triển khai sau.');
  };



  // Dữ liệu cho biểu đồ timeline khách hàng
  const getCustomerTimelineChartData = () => {
    if (!customerReportData.length) return [];

    // Giả lập dữ liệu theo thời gian cho mỗi khách hàng
    // Trong thực tế, dữ liệu này sẽ được lấy từ API
    const startDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);
    const dayDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

    // Tạo một mảng các ngày trong khoảng thời gian
    const timelineData = [];

    // Nếu khoảng thời gian quá dài, chia thành các điểm dữ liệu theo tuần hoặc tháng
    const interval = dayDiff > 60 ? 30 : (dayDiff > 14 ? 7 : 1); // Tháng, tuần hoặc ngày

    for (let i = 0; i <= dayDiff; i += interval) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dataPoint = {
        date: currentDate.toISOString().split('T')[0],
      };

      // Thêm dữ liệu cho mỗi khách hàng
      customerReportData.forEach(customer => {
        // Tạo dữ liệu ngẫu nhiên cho biểu đồ timeline
        // Trong thực tế, dữ liệu này sẽ được lấy từ API
        const randomFactor = 0.8 + Math.random() * 0.4; // Biến động 80-120%
        dataPoint[`customer_${customer.customerId}`] = Math.round(customer.totalRevenue * randomFactor / (dayDiff / interval));
      });

      timelineData.push(dataPoint);
    }

    return timelineData;
  };

  // Hàm tạo dữ liệu cho biểu đồ so sánh hiệu suất
  const getPerformanceComparisonData = () => {
    // Tính toán tổng doanh thu hiện tại và kỳ trước
    const currentRevenue = customerReportData.reduce((sum, item) => sum + item.totalRevenue, 0);
    const previousRevenue = currentRevenue * 0.85; // Giả định doanh thu kỳ trước là 85% hiện tại

    // Tính toán tổng chi phí hiện tại và kỳ trước
    const currentCost = supplierReportData.reduce((sum, item) => sum + item.totalCost, 0);
    const previousCost = currentCost * 0.9; // Giả định chi phí kỳ trước là 90% hiện tại

    // Tính toán lợi nhuận
    const currentProfit = currentRevenue - currentCost;
    const previousProfit = previousRevenue - previousCost;

    // Tính toán số lượng ticket
    const currentTickets = customerReportData.reduce((sum, item) => sum + item.totalTickets, 0);
    const previousTickets = currentTickets * 0.8; // Giả định số ticket kỳ trước là 80% hiện tại

    // Tính toán tỷ lệ hoàn thành
    const currentCompleted = customerReportData.reduce((sum, item) => sum + item.completedTickets, 0);
    const previousCompleted = previousTickets * 0.75; // Giả định số ticket hoàn thành kỳ trước là 75% số ticket kỳ trước

    const currentCompletionRate = currentTickets > 0 ? (currentCompleted / currentTickets) * 100 : 0;
    const previousCompletionRate = previousTickets > 0 ? (previousCompleted / previousTickets) * 100 : 0;

    // Xác định nhãn kỳ dựa trên performancePeriod
    const periodLabel = performancePeriod === 'month' ? 'Tháng' : 'Quý';

    return [
      {
        name: 'Doanh thu',
        current: currentRevenue,
        previous: previousRevenue,
        isCurrency: true,
        periodLabel
      },
      {
        name: 'Chi phí',
        current: currentCost,
        previous: previousCost,
        isCurrency: true,
        periodLabel
      },
      {
        name: 'Lợi nhuận',
        current: currentProfit,
        previous: previousProfit,
        isCurrency: true,
        periodLabel
      },
      {
        name: 'Số lượng ticket',
        current: currentTickets,
        previous: previousTickets,
        isCount: true,
        periodLabel
      },
      {
        name: 'Tỷ lệ hoàn thành',
        current: currentCompletionRate,
        previous: previousCompletionRate,
        isPercentage: true,
        periodLabel
      }
    ];
  };

  // Dữ liệu cho biểu đồ cột chồng theo trạng thái
  const getRevenueByStatusChartData = () => {
    if (!rawReportData.length) return [];

    // Sử dụng dữ liệu thực từ API
    const startDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);
    const dayDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

    // Xác định khoảng thời gian phù hợp dựa trên period được chọn
    let interval = 1; // Mặc định là ngày
    let dateFormat = { day: '2-digit', month: '2-digit' };
    let groupByFn;

    switch (revenuePeriod) {
      case 'day':
        interval = 1; // Ngày
        dateFormat = { day: '2-digit', month: '2-digit' };
        groupByFn = (date) => date.toLocaleDateString('vi-VN', dateFormat);
        break;
      case 'month':
        interval = 30; // Tháng
        dateFormat = { month: '2-digit', year: 'numeric' };
        groupByFn = (date) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          return `${String(month + 1).padStart(2, '0')}/${year}`;
        };
        break;
      case 'quarter':
        interval = 90; // Quý
        dateFormat = { year: 'numeric' };
        groupByFn = (date) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          const quarter = Math.floor(month / 3) + 1;
          return `Q${quarter}/${year}`;
        };
        break;
      default:
        // Mặc định là tháng
        interval = 30;
        dateFormat = { month: '2-digit', year: 'numeric' };
        groupByFn = (date) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          return `${String(month + 1).padStart(2, '0')}/${year}`;
        };
    }

    // Sẽ tạo một mảng các điểm thời gian từ dữ liệu thực

    // Lấy danh sách các trạng thái có trong dữ liệu
    const activeStatuses = Object.keys(statusDistribution).filter(status => statusDistribution[status] > 0);

    // Tạo một map để nhóm dữ liệu theo thời gian và trạng thái
    const dateStatusMap = new Map();

    // Xử lý dữ liệu thực từ API
    rawReportData.forEach(item => {
      // Lấy ngày tạo hoặc ngày yêu cầu
      const itemDate = item.request_date ? new Date(item.request_date) : new Date(item.created_at);

      // Bỏ qua các mục không có ngày hợp lệ hoặc nằm ngoài khoảng thời gian
      if (isNaN(itemDate.getTime()) || itemDate < startDate || itemDate > endDate) return;

      // Sử dụng hàm nhóm theo thời gian đã định nghĩa
      const formattedDate = groupByFn(itemDate);

      // Lấy trạng thái, doanh thu và chi phí
      const status = item.current_status || 'initial';
      const revenue = item.total_receive_after_tax || 0;
      const cost = item.total_pay_after_tax || 0;
      const profit = revenue - cost;

      // Tạo hoặc cập nhật dữ liệu cho ngày và trạng thái
      if (!dateStatusMap.has(formattedDate)) {
        const newDataPoint = {
          date: formattedDate,
          profit: 0,
          totalCost: 0
        };
        activeStatuses.forEach(s => newDataPoint[s] = 0);
        dateStatusMap.set(formattedDate, newDataPoint);
      }

      const dataPoint = dateStatusMap.get(formattedDate);
      dataPoint[status] = (dataPoint[status] || 0) + revenue;
      dataPoint.profit = (dataPoint.profit || 0) + profit;
      dataPoint.totalCost = (dataPoint.totalCost || 0) + cost;
    });

    // Chuyển map thành mảng và sắp xếp theo ngày
    const sortedData = Array.from(dateStatusMap.values()).sort((a, b) => {
      const dateA = a.date.split('/').reverse().join('');
      const dateB = b.date.split('/').reverse().join('');
      return dateA.localeCompare(dateB);
    });

    // Tính tổng cho mỗi điểm dữ liệu
    sortedData.forEach(dataPoint => {
      let total = 0;
      activeStatuses.forEach(status => {
        total += dataPoint[status] || 0;
      });
      dataPoint.total = total;
    });

    return sortedData;
  };

  // Dữ liệu cho biểu đồ timeline nhà cung cấp
  const getSupplierTimelineChartData = () => {
    if (!supplierReportData.length) return [];

    // Giả lập dữ liệu theo thời gian cho mỗi nhà cung cấp
    const startDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);
    const dayDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

    // Tạo một mảng các ngày trong khoảng thời gian
    const timelineData = [];

    // Nếu khoảng thời gian quá dài, chia thành các điểm dữ liệu theo tuần hoặc tháng
    const interval = dayDiff > 60 ? 30 : (dayDiff > 14 ? 7 : 1); // Tháng, tuần hoặc ngày

    for (let i = 0; i <= dayDiff; i += interval) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dataPoint = {
        date: currentDate.toISOString().split('T')[0],
      };

      // Thêm dữ liệu cho mỗi nhà cung cấp
      supplierReportData.forEach(supplier => {
        // Tạo dữ liệu ngẫu nhiên cho biểu đồ timeline
        const randomFactor = 0.8 + Math.random() * 0.4; // Biến động 80-120%
        dataPoint[`supplier_${supplier.supplierId}`] = Math.round(supplier.totalCost * randomFactor / (dayDiff / interval));
      });

      timelineData.push(dataPoint);
    }

    return timelineData;
  };

  // Dữ liệu cho biểu đồ cột khách hàng
  const getCustomerBarChartData = () => {
    if (!customerReportData.length) return [];

    return customerReportData.map(item => ({
      name: item.customerName,
      revenue: item.totalRevenue,
      cost: item.totalCost,
      profit: item.profit
    }));
  };

  // Dữ liệu cho biểu đồ doanh thu và chi phí theo thời gian cho khách hàng
  const getCustomerRevenueTimeChartData = () => {
    if (!rawReportData.length || !selectedCustomerId) return [];

    // Sử dụng dữ liệu thực từ API
    const startDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);

    // Xác định khoảng thời gian phù hợp dựa trên period được chọn
    let groupByFn;

    switch (customerRevenuePeriod) {
      case 'day':
        // Nhóm theo ngày
        groupByFn = (date) => {
          return date.toISOString().split('T')[0];
        };
        break;
      case 'week':
        // Nhóm theo tuần
        groupByFn = (date) => {
          const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
          const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
          const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
          return `Tuần ${weekNumber}, ${date.getFullYear()}`;
        };
        break;
      default:
        // Mặc định là tháng
        groupByFn = (date) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          return `${String(month + 1).padStart(2, '0')}/${year}`;
        };
    }

    // Tạo một map để nhóm dữ liệu theo thời gian
    const dateMap = new Map();

    // Xử lý dữ liệu thực từ API
    rawReportData.forEach(item => {
      // Chỉ xử lý dữ liệu của khách hàng được chọn
      if (item.customer_id.toString() !== selectedCustomerId) return;

      // Lấy ngày tạo hoặc ngày yêu cầu
      const itemDate = item.request_date ? new Date(item.request_date) : new Date(item.created_at);

      // Bỏ qua các mục không có ngày hợp lệ hoặc nằm ngoài khoảng thời gian
      if (isNaN(itemDate.getTime()) || itemDate < startDate || itemDate > endDate) return;

      // Sử dụng hàm nhóm theo thời gian đã định nghĩa
      const formattedDate = groupByFn(itemDate);

      // Lấy doanh thu và chi phí
      const revenue = item.total_receive_after_tax || 0;
      const cost = item.total_pay_after_tax || 0;

      // Tạo hoặc cập nhật dữ liệu cho ngày
      if (!dateMap.has(formattedDate)) {
        dateMap.set(formattedDate, {
          date: formattedDate,
          revenue: 0,
          cost: 0
        });
      }

      const dataPoint = dateMap.get(formattedDate);
      dataPoint.revenue += revenue;
      dataPoint.cost += cost;
    });

    // Chuyển map thành mảng và sắp xếp theo ngày
    const sortedData = Array.from(dateMap.values()).sort((a, b) => {
      // Xử lý đặc biệt cho định dạng tuần
      if (customerRevenuePeriod === 'week') {
        const weekA = parseInt(a.date.split(' ')[1].replace(',', ''));
        const yearA = parseInt(a.date.split(' ')[2]);
        const weekB = parseInt(b.date.split(' ')[1].replace(',', ''));
        const yearB = parseInt(b.date.split(' ')[2]);

        if (yearA !== yearB) return yearA - yearB;
        return weekA - weekB;
      }

      // Xử lý cho định dạng ngày và tháng
      const dateA = a.date.split('/').reverse().join('');
      const dateB = b.date.split('/').reverse().join('');
      return dateA.localeCompare(dateB);
    });

    return sortedData;
  };

  // Dữ liệu cho biểu đồ cột nhà cung cấp
  const getSupplierBarChartData = () => {
    if (!supplierReportData.length) return [];

    return supplierReportData.map(item => ({
      name: item.supplierName,
      revenue: item.totalRevenue,
      cost: item.totalCost,
      profit: item.profit
    }));
  };

  // Dữ liệu cho biểu đồ doanh thu và lợi nhuận theo thời gian
  const getRevenueProfitChartData = () => {
    if (!rawReportData.length) return [];

    // Sử dụng dữ liệu thực từ API
    const startDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);
    const dayDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

    // Xác định khoảng thời gian phù hợp dựa trên period được chọn
    let groupByFn;

    switch (revenueProfitPeriod) {
      case 'day':
        // Nhóm theo ngày
        groupByFn = (date) => {
          return date.toISOString().split('T')[0];
        };
        break;
      case 'week':
        // Nhóm theo tuần
        groupByFn = (date) => {
          const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
          const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
          const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
          return `Tuần ${weekNumber}, ${date.getFullYear()}`;
        };
        break;
      default:
        // Mặc định là tháng
        groupByFn = (date) => {
          const month = date.getMonth();
          const year = date.getFullYear();
          return `${String(month + 1).padStart(2, '0')}/${year}`;
        };
    }

    // Tạo một map để nhóm dữ liệu theo thời gian
    const dateMap = new Map();

    // Xử lý dữ liệu thực từ API
    rawReportData.forEach(item => {
      // Lấy ngày tạo hoặc ngày yêu cầu
      const itemDate = item.request_date ? new Date(item.request_date) : new Date(item.created_at);

      // Bỏ qua các mục không có ngày hợp lệ hoặc nằm ngoài khoảng thời gian
      if (isNaN(itemDate.getTime()) || itemDate < startDate || itemDate > endDate) return;

      // Sử dụng hàm nhóm theo thời gian đã định nghĩa
      const formattedDate = groupByFn(itemDate);

      // Lấy doanh thu và chi phí
      const revenue = item.total_receive_after_tax || 0;
      const cost = item.total_pay_after_tax || 0;
      const profit = revenue - cost;

      // Tạo hoặc cập nhật dữ liệu cho ngày
      if (!dateMap.has(formattedDate)) {
        dateMap.set(formattedDate, {
          date: formattedDate,
          revenue: 0,
          cost: 0,
          profit: 0
        });
      }

      const dataPoint = dateMap.get(formattedDate);
      dataPoint.revenue += revenue;
      dataPoint.cost += cost;
      dataPoint.profit += profit;
    });

    // Chuyển map thành mảng và sắp xếp theo ngày
    const sortedData = Array.from(dateMap.values()).sort((a, b) => {
      // Xử lý đặc biệt cho định dạng tuần
      if (revenueProfitPeriod === 'week') {
        const weekA = parseInt(a.date.split(' ')[1].replace(',', ''));
        const yearA = parseInt(a.date.split(' ')[2]);
        const weekB = parseInt(b.date.split(' ')[1].replace(',', ''));
        const yearB = parseInt(b.date.split(' ')[2]);

        if (yearA !== yearB) return yearA - yearB;
        return weekA - weekB;
      }

      // Xử lý cho định dạng ngày và tháng
      const dateA = a.date.split('/').reverse().join('');
      const dateB = b.date.split('/').reverse().join('');
      return dateA.localeCompare(dateB);
    });

    return sortedData;
  };

  // Màu cho biểu đồ tròn và trạng thái
  const getStatusColor = (status) => {
    // Map status to modern flat colors
    switch (status?.toLowerCase()) {
      case 'initial':
        return '#FFD166'; // Khởi tạo - Màu vàng nhạt hiện đại
      case 'approved':
        return '#06D6A0'; // Đã duyệt - Màu xanh lá mint
      case 'delivered':
        return '#118AB2'; // Đã giao hàng - Màu xanh dương hiện đại
      case 'customerpaid':
        return '#EF476F'; // Đã thu tiền khách hàng - Màu hồng hiện đại
      case 'supplierpaid':
        return '#9B5DE5'; // Đã trả tiền nhà cung cấp - Màu tím hiện đại
      case 'completed':
        return '#00B4D8'; // Hoàn thành - Màu xanh biển hiện đại
      case 'processing':
        return '#F8961E'; // Đang xử lý - Màu cam hiện đại
      case 'cancelled':
        return '#EF233C'; // Đã hủy - Màu đỏ hiện đại
      case 'progress':
        return '#3A86FF'; // Màu xanh dương sáng
      case 'rejected':
        return '#D90429'; // Màu đỏ đậm hiện đại
      case 'priced':
      case 'confirmed':
        return '#F9C74F'; // Màu vàng nghệ hiện đại
      default:
        return '#8D99AE'; // Màu mặc định - Xám xanh hiện đại
    }
  };

  // Fallback colors for additional statuses if needed - Modern flat palette
  const COLORS = ['#22577A', '#38A3A5', '#57CC99', '#80ED99', '#C7F9CC', '#FE5F55', '#F0B67F', '#D6D1B1', '#7EB2DD', '#748CAB'];

  return (
    <div className="reports-container">
      {/* Main Report Type Tabs */}
      <div className="main-tabs">
          <button
            className={`main-tab-button ${mainTab === 'overview' ? 'active' : ''}`}
            onClick={() => setMainTab('overview')}
          >
            <FiPieChart /> Tổng quan
          </button>
          <button
            className={`main-tab-button ${mainTab === 'customer' ? 'active' : ''}`}
            onClick={() => setMainTab('customer')}
          >
            <FiUser /> Báo cáo khách hàng
          </button>
          <button
            className={`main-tab-button ${mainTab === 'supplier' ? 'active' : ''}`}
            onClick={() => setMainTab('supplier')}
          >
            <FiTruck /> Báo cáo nhà cung cấp
          </button>
        </div>

      <div className={`report-filter-container ${showFilters ? 'expanded' : 'collapsed'}`}>
        <form onSubmit={handleSubmit}>
          <div className="report-filter-group compact">
            <div className="report-filter-item others-filter">
              <label><FiFilter /> Công ty:</label>
              {isCompanyLoading ? (
                <div className="select-loading">Đang tải danh sách công ty...</div>
              ) : (
                <select
                  value={selectedCompanyId}
                  onChange={handleCompanyChange}
                >
                  <option value="">Tất cả công ty</option>
                  {companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.company_name}
                    </option>
                  ))}
                </select>
              )}
            </div>
            <div className="report-filter-item date-filter">
              <label><FiCalendar /> Từ ngày:</label>
              <input
                type="date"
                name="startDate"
                value={dateRange.startDate}
                onChange={handleDateChange}
                required
              />
            </div>
            <div className="report-filter-item date-filter">
              <label><FiCalendar /> Đến ngày:</label>
              <input
                type="date"
                name="endDate"
                value={dateRange.endDate}
                onChange={handleDateChange}
                required
              />
            </div>

            {/* Only show additional filters for customer and supplier tabs */}
            {mainTab !== 'overview' && (
              <>
                {mainTab === 'customer' && (
                  <div className="report-filter-item others-filter">
                    <label><FiUser /> Khách hàng:</label>
                    {isCustomerLoading ? (
                      <div className="select-loading">Đang tải danh sách khách hàng...</div>
                    ) : (
                      <select
                        value={selectedCustomerId}
                        onChange={handleCustomerChange}
                      >
                        <option value="">Tất cả khách hàng</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.customer_name}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                )}
                {mainTab === 'supplier' && (
                  <div className="report-filter-item others-filter">
                    <label><FiUser /> Nhà cung cấp:</label>
                    {isSupplierLoading ? (
                      <div className="select-loading">Đang tải danh sách nhà cung cấp...</div>
                    ) : (
                      <select
                        value={selectedSupplierId}
                        onChange={handleSupplierChange}
                      >
                        <option value="">Tất cả nhà cung cấp</option>
                        {suppliers.map(supplier => (
                          <option key={supplier.id} value={supplier.id}>
                            {supplier.supplier_name}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                )}
              </>
            )}
          </div>

          {mainTab !== 'overview' && (
            <div className="report-filter-group compact">
              <div className="report-filter-item others-filter">
                <label><FiUser /> NV bán hàng:</label>
                {isStaffLoading ? (
                  <div className="select-loading">Đang tải danh sách nhân viên...</div>
                ) : (
                  <select
                    value={selectedSaleStaffId}
                    onChange={handleSaleStaffChange}
                  >
                    <option value="">Tất cả NV bán hàng</option>
                    {staffs.map(staff => (
                      <option key={staff.id} value={staff.id}>
                        {staff.display_name || staff.name || staff.username}
                      </option>
                    ))}
                  </select>
                )}
              </div>
              <div className="report-filter-item others-filter">
                <label><FiUser /> NV định giá:</label>
                {isStaffLoading ? (
                  <div className="select-loading">Đang tải danh sách nhân viên...</div>
                ) : (
                  <select
                    value={selectedPricingStaffId}
                    onChange={handlePricingStaffChange}
                  >
                    <option value="">Tất cả NV định giá</option>
                    {staffs.map(staff => (
                      <option key={staff.id} value={staff.id}>
                        {staff.display_name || staff.name || staff.username}
                      </option>
                    ))}
                  </select>
                )}
              </div>
              <div className="report-filter-item others-filter">
                <label><FiUser /> NV điều phối:</label>
                {isStaffLoading ? (
                  <div className="select-loading">Đang tải danh sách nhân viên...</div>
                ) : (
                  <select
                    value={selectedDispatchStaffId}
                    onChange={handleDispatchStaffChange}
                  >
                    <option value="">Tất cả NV điều phối</option>
                    {staffs.map(staff => (
                      <option key={staff.id} value={staff.id}>
                        {staff.display_name || staff.name || staff.username}
                      </option>
                    ))}
                  </select>
                )}
              </div>
            </div>
          )}

          <div className="report-filter-item report-filter-actions">
            <button type="submit" className="report-btn-filter">
              <FiFilter /> Lọc
            </button>
            <button type="button" className="report-btn-filter report-btn-clear" onClick={() => {
              // Reset tất cả các filter
              setSelectedCustomerId('');
              setSelectedSupplierId('');
              setSelectedSaleStaffId('');
              setSelectedPricingStaffId('');
              setSelectedDispatchStaffId('');
              setSelectedStatus('');
              setSelectedCompanyId('');

              // Reset date range về mặc định (đầu tháng đến hiện tại)
              const today = new Date();
              const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

              const formatDate = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
              };

              setDateRange({
                startDate: formatDate(firstDay),
                endDate: formatDate(today)
              });

              // Nếu đã áp dụng filter trước đó, gọi lại API để lấy dữ liệu mới
              if (hasAppliedFilters) {
                setTimeout(() => {
                  fetchReportData();
                }, 0);
              }
            }}>
              <FiFilter /> Xóa lọc
            </button>
          </div>
        </form>
      </div>

      {/* Report Tabs */}

      {error ? (
        <div className="error-message">{error}</div>
      ) : isLoading ? (
        <div className="loading-container">
          <div className="report-loading-spinner"></div>
        </div>
      ) : (mainTab === 'overview' && !hasAppliedFilters) ? (
        <div className="no-data-message">
          <p>Chưa có dữ liệu báo cáo. Vui lòng chọn điều kiện lọc và nhấn nút "Lọc".</p>
        </div>
      ) : (mainTab === 'overview' && hasAppliedFilters) || (mainTab === 'customer' ? customerReportData : supplierReportData).length > 0 ? (
        <div className="report-content-container">
          {/* Report Content based on selected view type */}
          {reportViewType === 'summary' && (
            <div className="report-summary-view">
              {mainTab === 'overview' ? (
                <>
                  {/* 1. SUMMARY GROUP - Tổng quan */}
                  <div className="report-group">
                    <h2 className="report-group-title">
                      <FiDollarSign /> Tổng quan
                    </h2>
                    <div className="report-group-content cards-grid">
                      {/* Financial cards */}
                      <div className="summary-card">
                        <div className="summary-icon">
                          <FiDollarSign />
                        </div>
                        <div className="summary-content">
                          <h3>Tổng doanh thu</h3>
                          <p className="summary-number">{formatCurrency(customerReportData.reduce((sum, item) => sum + item.totalRevenue, 0))}</p>
                          {/* <p className="summary-trend positive">+8% so với tháng trước</p> */}
                        </div>
                      </div>

                      <div className="summary-card">
                        <div className="summary-icon">
                          <FiDollarSign />
                        </div>
                        <div className="summary-content">
                          <h3>Tổng chi phí</h3>
                          <p className="summary-number">{formatCurrency(supplierReportData.reduce((sum, item) => sum + item.totalCost, 0))}</p>
                          {/* <p className="summary-trend negative">+5% so với tháng trước</p> */}
                        </div>
                      </div>

                      <div className="summary-card">
                        <div className="summary-icon">
                          <FiPieChart />
                        </div>
                        <div className="summary-content">
                          <h3>Tổng lợi nhuận</h3>
                          <p className="summary-number">{formatCurrency(customerReportData.reduce((sum, item) => sum + item.profit, 0))}</p>
                          {/* <p className={`summary-trend ${customerReportData.reduce((sum, item) => sum + item.profit, 0) >= 0 ? 'positive' : 'negative'}`}>
                            {customerReportData.reduce((sum, item) => sum + item.profit, 0) >= 0 ? '+15%' : '-3%'} so với tháng trước
                          </p> */}
                        </div>
                      </div>
                    </div>

                    {/* Count cards in a single row */}
                    <div className="report-group-content three-cards-row" style={{ marginTop: '20px' }}>
                      <div className="summary-card">
                        <div className="summary-icon">
                          <FiTruck />
                        </div>
                        <div className="summary-content">
                          <h3>Tổng số ticket</h3>
                          <p className="summary-number">{customerReportData.reduce((sum, item) => sum + item.totalTickets, 0)}</p>
                          {/* <p className="summary-trend positive">+12% so với tháng trước</p> */}
                        </div>
                      </div>

                      <div className="summary-card">
                        <div className="summary-icon">
                          <FiUsers />
                        </div>
                        <div className="summary-content">
                          <h3>Tổng số khách hàng</h3>
                          <p className="summary-number">{customerReportData.length}</p>
                          {/* <p className="summary-trend positive">+5% so với tháng trước</p> */}
                        </div>
                      </div>

                      <div className="summary-card">
                        <div className="summary-icon">
                          <FiTruck />
                        </div>
                        <div className="summary-content">
                          <h3>Tổng số nhà cung cấp</h3>
                          <p className="summary-number">{supplierReportData.length}</p>
                          {/* <p className="summary-trend positive">+3% so với tháng trước</p> */}
                        </div>
                      </div>
                    </div>

                    <div className="report-group-content">
                      {/* Biểu đồ doanh thu và chi phí theo thời gian */}
                      <div className="dashboard-card">
                        <div className="dashboard-card-header">
                          <h3>
                            <FiBarChart2 /> Doanh thu và chi phí theo thời gian
                          </h3>
                          <div className="card-actions">
                            <div className="chart-period-selector">
                              <button
                                className={`period-button ${revenueProfitPeriod === 'day' ? 'active' : ''}`}
                                onClick={() => setRevenueProfitPeriod('day')}
                              >
                                Ngày
                              </button>
                              <button
                                className={`period-button ${revenueProfitPeriod === 'week' ? 'active' : ''}`}
                                onClick={() => setRevenueProfitPeriod('week')}
                              >
                                Tuần
                              </button>
                              <button
                                className={`period-button ${revenueProfitPeriod === 'month' ? 'active' : ''}`}
                                onClick={() => setRevenueProfitPeriod('month')}
                              >
                                Tháng
                              </button>
                            </div>
                          </div>
                        </div>
                        <div className="dashboard-card-body">
                          {/* <div className="chart-summary">
                            <div className="chart-summary-item">
                              <span className="chart-summary-label">Tổng doanh thu</span>
                              <span className="chart-summary-value">
                                {formatCurrency(getRevenueProfitChartData().reduce((sum, item) => sum + item.revenue, 0))}
                              </span>
                            </div>
                            <div className="chart-summary-item">
                              <span className="chart-summary-label">Tổng chi phí</span>
                              <span className="chart-summary-value">
                                {formatCurrency(getRevenueProfitChartData().reduce((sum, item) => sum + item.cost, 0))}
                              </span>
                            </div>
                          </div> */}
                          <div className="chart-content">
                            <ResponsiveContainer width="100%" height={400}>
                              <BarChart
                                data={getRevenueProfitChartData()}
                                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                <XAxis
                                  dataKey="date"
                                  angle={-45}
                                  textAnchor="end"
                                  height={70}
                                  tick={{ fontSize: 12 }}
                                />
                                <YAxis
                                  tickFormatter={(value) => value >= 1000000 ? `${(value / 1000000).toFixed(1)}M` : value >= 1000 ? `${(value / 1000).toFixed(1)}K` : value}
                                />
                                <Tooltip
                                  formatter={(value, name) => {
                                    return [formatCurrency(value), name === 'revenue' ? 'Doanh thu' : 'Chi phí'];
                                  }}
                                  labelFormatter={(label) => `Thời gian: ${label}`}
                                  contentStyle={{
                                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                    borderRadius: '8px',
                                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                                    border: 'none',
                                    padding: '10px 15px'
                                  }}
                                />
                                <Legend
                                  formatter={(value) => value === 'revenue' ? 'Doanh thu' : 'Chi phí'}
                                />
                                <Bar
                                  dataKey="revenue"
                                  fill="#4caf50"
                                  name="revenue"
                                  radius={[4, 4, 0, 0]}
                                />
                                <Bar
                                  dataKey="cost"
                                  fill="#ff9800"
                                  name="cost"
                                  radius={[4, 4, 0, 0]}
                                />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      </div>

                      <div className="dashboard-card">
                        <div className="dashboard-card-header">
                          <h3>
                            <FiPieChart /> Phân bố trạng thái ticket
                          </h3>
                        </div>
                        <div className="dashboard-card-body">
                          <div className="chart-content">
                            <ResponsiveContainer width="100%" height={350}>
                              <PieChart>
                                <Pie
                                  data={statuses.map(status => {
                                    return {
                                      name: status.name,
                                      value: statusDistribution[status.id] || 0,
                                      statusId: status.id
                                    };
                                  }).filter(item => item.value > 0)}
                                  cx="50%"
                                  cy="50%"
                                  labelLine={true}
                                  outerRadius={110}
                                  innerRadius={50}
                                  fill="#8884d8"
                                  dataKey="value"
                                  nameKey="name"
                                  paddingAngle={3}
                                  label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                                >
                                  {statuses
                                    .filter(status => (statusDistribution[status.id] || 0) > 0)
                                    .map(status => (
                                      <Cell key={`cell-${status.id}`} fill={getStatusColor(status.id)} />
                                    ))}
                                </Pie>
                                <Tooltip
                                  formatter={(value, _, props) => {
                                    const total = Object.values(statusDistribution).reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return [
                                      `${value} ticket (${percentage}%)`,
                                      props.payload.name
                                    ];
                                  }}
                                  contentStyle={{
                                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                    borderRadius: '8px',
                                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                                    border: 'none',
                                    padding: '10px 15px'
                                  }}
                                  itemStyle={{ color: '#333' }}
                                />
                                <Legend
                                  layout="horizontal"
                                  verticalAlign="bottom"
                                  align="center"
                                  iconType="circle"
                                  iconSize={10}
                                  wrapperStyle={{
                                    paddingTop: '30px',
                                    fontSize: '13px',
                                    display: 'flex',
                                    flexWrap: 'wrap',
                                    justifyContent: 'center',
                                    gap: '10px'
                                  }}
                                  payload={
                                    statuses
                                      .filter(status => (statusDistribution[status.id] || 0) > 0)
                                      .map(status => ({
                                        id: status.id,
                                        type: 'circle',
                                        value: status.name,
                                        color: getStatusColor(status.id)
                                      }))
                                  }
                                />
                              </PieChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      </div>


                    </div>

                    <div className="report-group-content">
                      <div className="dashboard-card">
                        <div className="dashboard-card-header">
                          <h3>
                            <FiUsers /> Top 10 khách hàng theo doanh thu
                          </h3>
                        </div>
                        <div className="dashboard-card-body">
                          <div className="ranking-table-container">
                            <table className="ranking-table">
                              <thead>
                                <tr>
                                  <th>Hạng</th>
                                  <th>Khách hàng</th>
                                  <th>Doanh thu</th>
                                  <th>Tỷ lệ</th>
                                </tr>
                              </thead>
                              <tbody>
                                {customerReportData
                                  .sort((a, b) => b.totalRevenue - a.totalRevenue)
                                  .slice(0, 10)
                                  .map((item, index) => {
                                    const total = customerReportData.reduce((sum, i) => sum + i.totalRevenue, 0);
                                    const percentage = ((item.totalRevenue / total) * 100).toFixed(1);

                                    return (
                                      <tr key={index}>
                                        <td className="rank-cell">{index + 1}</td>
                                        <td>{item.customerName}</td>
                                        <td>{formatCurrency(item.totalRevenue)}</td>
                                        <td>
                                          <div className="percentage-bar-container">
                                            <div
                                              className="percentage-bar"
                                              style={{ width: `${percentage}%` }}
                                            ></div>
                                            <span className="percentage-text">{percentage}%</span>
                                          </div>
                                        </td>
                                      </tr>
                                    );
                                  })}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>

                      <div className="summary-cards cards-grid">
                        <div className="dashboard-card">
                          <div className="dashboard-card-header">
                            <h3>
                              <FiUsers /> Top 10 khách hàng theo lợi nhuận
                            </h3>
                          </div>
                          <div className="dashboard-card-body">
                            <div className="ranking-table-container">
                              <table className="ranking-table">
                                <thead>
                                  <tr>
                                    <th>Hạng</th>
                                    <th>Khách hàng</th>
                                    <th>Lợi nhuận</th>
                                    <th>Tỷ lệ</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {customerReportData
                                    .sort((a, b) => b.profit - a.profit)
                                    .slice(0, 10)
                                    .map((item, index) => {
                                      // Tính tỷ lệ lợi nhuận trên doanh thu của từng khách hàng
                                      const percentage = item.totalRevenue > 0 ? ((item.profit / item.totalRevenue) * 100).toFixed(1) : '0';

                                      return (
                                        <tr key={index}>
                                          <td className="rank-cell">{index + 1}</td>
                                          <td>{item.customerName}</td>
                                          <td className={item.profit >= 0 ? 'profit-positive' : 'profit-negative'}>
                                            {formatCurrency(item.profit)}
                                          </td>
                                          <td>
                                            <div className="percentage-bar-container">
                                              <div
                                                className="percentage-bar"
                                                style={{
                                                  width: `${Math.min(Math.abs(parseFloat(percentage)), 100)}%`,
                                                  backgroundColor: item.profit >= 0 ? '#4caf50' : '#f44336'
                                                }}
                                              ></div>
                                              <span className="percentage-text">{percentage}%</span>
                                            </div>
                                          </td>
                                        </tr>
                                      );
                                    })}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="report-group-content">
                      <div className="dashboard-card">
                        <div className="dashboard-card-header">
                          <h3>
                            <FiTruck /> Top 10 nhà cung cấp theo chi phí
                          </h3>
                        </div>
                        <div className="dashboard-card-body">
                          <div className="ranking-table-container">
                            <table className="ranking-table">
                              <thead>
                                <tr>
                                  <th>Hạng</th>
                                  <th>Nhà cung cấp</th>
                                  <th>Chi phí</th>
                                  <th>Tỷ lệ</th>
                                </tr>
                              </thead>
                              <tbody>
                                {supplierReportData
                                  .sort((a, b) => b.totalCost - a.totalCost)
                                  .slice(0, 10)
                                  .map((item, index) => {
                                    const total = supplierReportData.reduce((sum, i) => sum + i.totalCost, 0);
                                    const percentage = ((item.totalCost / total) * 100).toFixed(1);

                                    return (
                                      <tr key={index}>
                                        <td className="rank-cell">{index + 1}</td>
                                        <td>{item.supplierName}</td>
                                        <td>{formatCurrency(item.totalCost)}</td>
                                        <td>
                                          <div className="percentage-bar-container">
                                            <div
                                              className="percentage-bar"
                                              style={{ width: `${percentage}%` }}
                                            ></div>
                                            <span className="percentage-text">{percentage}%</span>
                                          </div>
                                        </td>
                                      </tr>
                                    );
                                  })}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>

                      <div className="summary-cards cards-grid">
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  {/* Customer/Supplier Summary Cards */}
                  <div className="summary-cards">
                    <div className="summary-card">
                      <div className="summary-icon">
                        <FiUsers />
                      </div>
                      <div className="summary-content">
                        <h3>{mainTab === 'customer' ? 'Tổng số khách hàng' : 'Tổng số nhà cung cấp'}</h3>
                        <p className="summary-number">{(mainTab === 'customer' ? customerReportData : supplierReportData).length}</p>
                        {/* <p className="summary-trend positive">+5% so với tháng trước</p> */}
                      </div>
                    </div>

                    <div className="summary-card">
                      <div className="summary-icon">
                        <FiTruck />
                      </div>
                      <div className="summary-content">
                        <h3>Tổng số ticket</h3>
                        <p className="summary-number">{(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalTickets, 0)}</p>
                        {/* <p className="summary-trend positive">+12% so với tháng trước</p> */}
                      </div>
                    </div>

                    <div className="summary-card">
                      <div className="summary-icon">
                        <FiDollarSign />
                      </div>
                      <div className="summary-content">
                        <h3>{mainTab === 'customer' ? 'Tổng doanh thu' : 'Tổng chi phí'}</h3>
                        <p className="summary-number">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + (mainTab === 'customer' ? item.totalRevenue : item.totalCost), 0))}</p>
                        {/* <p className="summary-trend positive">+8% so với tháng trước</p> */}
                      </div>
                    </div>

                    <div className="summary-card">
                      <div className="summary-icon">
                        <FiPieChart />
                      </div>
                      <div className="summary-content">
                        <h3>Tổng lợi nhuận</h3>
                        <p className="summary-number">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0))}</p>
                        <p className={`summary-trend ${(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0) >= 0 ? 'positive' : 'negative'}`}>
                          {(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0) >= 0 ? '+15%' : '-3%'} so với tháng trước
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* No additional content needed here for overview tab since we've already added all the content above */}
              {mainTab !== 'overview' && (
                <>
                  {/* Biểu đồ doanh thu và chi phí theo thời gian cho khách hàng */}
                  {mainTab === 'customer' && selectedCustomerId && (
                    <div className="dashboard-card">
                      <div className="dashboard-card-header">
                        <h3>
                          <FiBarChart2 /> Doanh thu và chi phí theo thời gian
                        </h3>
                        <div className="card-actions">
                          <div className="chart-period-selector">
                            <button
                              className={`period-button ${customerRevenuePeriod === 'day' ? 'active' : ''}`}
                              onClick={() => setCustomerRevenuePeriod('day')}
                            >
                              Ngày
                            </button>
                            <button
                              className={`period-button ${customerRevenuePeriod === 'week' ? 'active' : ''}`}
                              onClick={() => setCustomerRevenuePeriod('week')}
                            >
                              Tuần
                            </button>
                            <button
                              className={`period-button ${customerRevenuePeriod === 'month' ? 'active' : ''}`}
                              onClick={() => setCustomerRevenuePeriod('month')}
                            >
                              Tháng
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="dashboard-card-body">
                        <div className="chart-summary">
                          <div className="chart-summary-item">
                            <span className="chart-summary-label">Tổng doanh thu</span>
                            <span className="chart-summary-value">
                              {formatCurrency(getCustomerRevenueTimeChartData().reduce((sum, item) => sum + item.revenue, 0))}
                            </span>
                          </div>
                          <div className="chart-summary-item">
                            <span className="chart-summary-label">Tổng chi phí</span>
                            <span className="chart-summary-value">
                              {formatCurrency(getCustomerRevenueTimeChartData().reduce((sum, item) => sum + item.cost, 0))}
                            </span>
                          </div>
                        </div>
                        <div className="chart-content">
                          <ResponsiveContainer width="100%" height={400}>
                            <BarChart
                              data={getCustomerRevenueTimeChartData()}
                              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                              <XAxis
                                dataKey="date"
                                angle={-45}
                                textAnchor="end"
                                height={70}
                                tick={{ fontSize: 12 }}
                              />
                              <YAxis
                                tickFormatter={(value) => value >= 1000000 ? `${(value / 1000000).toFixed(1)}M` : value >= 1000 ? `${(value / 1000).toFixed(1)}K` : value}
                              />
                              <Tooltip
                                formatter={(value, name) => {
                                  return [formatCurrency(value), name === 'revenue' ? 'Doanh thu' : 'Chi phí'];
                                }}
                                labelFormatter={(label) => `Thời gian: ${label}`}
                                contentStyle={{
                                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                  borderRadius: '8px',
                                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                                  border: 'none',
                                  padding: '10px 15px'
                                }}
                              />
                              <Legend
                                formatter={(value) => value === 'revenue' ? 'Doanh thu' : 'Chi phí'}
                              />
                              <Bar
                                dataKey="revenue"
                                fill="#4caf50"
                                name="revenue"
                                radius={[4, 4, 0, 0]}
                              />
                              <Bar
                                dataKey="cost"
                                fill="#ff9800"
                                name="cost"
                                radius={[4, 4, 0, 0]}
                              />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Bảng dữ liệu chi tiết */}
                  <div className="dashboard-card full-width">
                    <div className="dashboard-card-header">
                      <h3>
                        <FiList /> {mainTab === 'customer' ? 'Chi tiết khách hàng' : 'Chi tiết nhà cung cấp'}
                      </h3>
                      <div className="card-actions">
                        <button className="action-button" onClick={exportToExcel}>
                          <FiDownload /> Xuất Excel
                        </button>
                      </div>
                    </div>
                    <div className="dashboard-card-body">
                      <div className="report-table-container">
                        <table className="report-table modern-table">
                          <thead>
                            <tr>
                              <th>{mainTab === 'customer' ? 'Khách hàng' : 'Nhà cung cấp'}</th>
                              <th className="center-align">Tổng số ticket</th>
                              <th className="center-align">Ticket hoàn thành</th>
                              <th className="center-align">Ticket đang xử lý</th>
                              <th className="right-align">Tổng doanh thu</th>
                              <th className="right-align">Tổng chi phí</th>
                              <th className="right-align">Lợi nhuận</th>
                              <th className="center-align">Tỷ lệ hoàn thành</th>
                            </tr>
                          </thead>
                          <tbody>
                            {(mainTab === 'customer' ? customerReportData : supplierReportData).map((item, index) => {
                              const completionRate = item.totalTickets > 0 ? (item.completedTickets / item.totalTickets) * 100 : 0;
                              return (
                                <tr key={index}>
                                  <td>
                                    <div className="entity-name">
                                      <div className="entity-avatar">{(mainTab === 'customer' ? item.customerName : item.supplierName).charAt(0)}</div>
                                      <span>{mainTab === 'customer' ? item.customerName : item.supplierName}</span>
                                    </div>
                                  </td>
                                  <td className="center-align">{item.totalTickets}</td>
                                  <td className="center-align">{item.completedTickets}</td>
                                  <td className="center-align">{item.pendingTickets}</td>
                                  <td className="right-align">{formatCurrency(item.totalRevenue)}</td>
                                  <td className="right-align">{formatCurrency(item.totalCost)}</td>
                                  <td className={`right-align ${item.profit >= 0 ? 'profit-positive' : 'profit-negative'}`}>
                                    {formatCurrency(item.profit)}
                                  </td>
                                  <td className="center-align">
                                    <div className="progress-bar-container">
                                      <div
                                        className="progress-bar"
                                        style={{ width: `${completionRate}%`, backgroundColor: completionRate > 70 ? '#4caf50' : completionRate > 30 ? '#ff9800' : '#f44336' }}
                                      ></div>
                                      <span className="progress-text">{completionRate.toFixed(0)}%</span>
                                    </div>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                          {(mainTab === 'customer' ? customerReportData : supplierReportData).length > 1 && (
                            <tfoot>
                              <tr>
                                <td><strong>Tổng cộng</strong></td>
                                <td className="center-align">{(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalTickets, 0)}</td>
                                <td className="center-align">{(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.completedTickets, 0)}</td>
                                <td className="center-align">{(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.pendingTickets, 0)}</td>
                                <td className="right-align">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalRevenue, 0))}</td>
                                <td className="right-align">{formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalCost, 0))}</td>
                                <td className={`right-align ${(mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0) >= 0 ? 'profit-positive' : 'profit-negative'}`}>
                                  {formatCurrency((mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.profit, 0))}
                                </td>
                                <td className="center-align">
                                  {(() => {
                                    const totalTickets = (mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.totalTickets, 0);
                                    const completedTickets = (mainTab === 'customer' ? customerReportData : supplierReportData).reduce((sum, item) => sum + item.completedTickets, 0);
                                    const completionRate = totalTickets > 0 ? (completedTickets / totalTickets) * 100 : 0;
                                    return (
                                      <div className="progress-bar-container">
                                        <div
                                          className="progress-bar"
                                          style={{ width: `${completionRate}%`, backgroundColor: completionRate > 70 ? '#4caf50' : completionRate > 30 ? '#ff9800' : '#f44336' }}
                                        ></div>
                                        <span className="progress-text">{completionRate.toFixed(0)}%</span>
                                      </div>
                                    );
                                  })()}
                                </td>
                              </tr>
                            </tfoot>
                          )}
                        </table>
                      </div>
                    </div>
                  </div>
                </>
              )}

            </div>
          )}
        </div>
      ) : (
        <div className="no-data-message">
          <p>Chưa có dữ liệu báo cáo. Vui lòng chọn điều kiện lọc và nhấn nút "Lọc".</p>
        </div>
      )}
    </div>
  );
};

export default Reports;