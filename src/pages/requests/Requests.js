import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiSearch, FiRefreshCw, FiGrid, FiList, FiChevronUp, FiChevronDown } from 'react-icons/fi';
import apiService from '../../services/api.service';
import { ENDPOINTS } from '../../config/api.config';
import '../../styles/Requests.css';

const Requests = () => {
  const navigate = useNavigate();
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusCounts, setStatusCounts] = useState({});
  const [staffList, setStaffList] = useState([]);

  // Filters
  const [selectedRequestType, setSelectedRequestType] = useState('');
  const [selectedStatus, setSelectedStatus] = useState(() => {
    // Load the saved status from localStorage, default to empty string if not found
    return localStorage.getItem('requestStatus') || '';
  });
  const [dueDateFilter, setDueDateFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOrder, setSortOrder] = useState('newest');
  const [viewMode, setViewMode] = useState(() => {
    // Load the saved view mode from localStorage, default to 'grid' if not found
    return localStorage.getItem('requestViewMode') || 'grid';
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(() => {
    // Load the saved items per page from localStorage, default to 10 if not found
    return parseInt(localStorage.getItem('ticketItemsPerPage') || '10', 10);
  });

  // Table sorting state
  const [sortField, setSortField] = useState('updated_at');
  const [sortDirection, setSortDirection] = useState('desc'); // 'asc' or 'desc'

  // Fetch staff information
  const fetchStaffList = async () => {
    try {
      const response = await apiService.get(ENDPOINTS.USERS.GET_BASIC);
      if (response && Array.isArray(response)) {
        setStaffList(response);
      } else if (response && response.users) {
        setStaffList(response.users);
      } else if (response && response.data) {
        setStaffList(response.data);
      } else {
        console.error('Unexpected staff response structure:', response);
      }
    } catch (err) {
      console.error('Error fetching staff list:', err);
    }
  };

  useEffect(() => {
    const fetchRequests = async () => {
      try {
        setLoading(true);
        const userData = localStorage.getItem('user');
        if (!userData) {
          navigate('/login');
          return;
        }

        const user = JSON.parse(userData);

        // Fetch staff list first
        await fetchStaffList();

        // Then fetch tickets
        const response = await apiService.get(ENDPOINTS.TICKET.GET_BY_USER(user.id));
        setRequests(response);

        // Calculate status counts
        const counts = response.reduce((acc, req) => {
          acc[req.status] = (acc[req.status] || 0) + 1;
          return acc;
        }, {});
        setStatusCounts(counts);
      } catch (err) {
        console.error('Error fetching requests:', err);
        setError('Failed to load requests');
      } finally {
        setLoading(false);
      }
    };

    fetchRequests();
  }, [navigate]);

  const handleRequestClick = (id) => {
    navigate(`/ticket/detail?id=${id}`);
  };

  const filterRequests = () => {
    let filtered = requests.filter(request => {
      // Filter by request type
      if (selectedRequestType && request.request_type !== selectedRequestType) {
        return false;
      }

      // Filter by status
      if (selectedStatus && request.current_status !== selectedStatus) {
        return false;
      }

      // Filter by due date
      if (dueDateFilter && request.request_date) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const requestDate = new Date(request.request_date);
        requestDate.setHours(0, 0, 0, 0);

        if (dueDateFilter === 'today') {
          // Show only items due today
          const todayDate = new Date();
          todayDate.setHours(0, 0, 0, 0);

          if (requestDate.getTime() !== todayDate.getTime()) {
            return false;
          }
        } else if (dueDateFilter === 'week') {
          // Show items due within the next 7 days
          const weekLater = new Date(today);
          weekLater.setDate(weekLater.getDate() + 7);

          if (requestDate < today || requestDate > weekLater) {
            return false;
          }
        } else if (dueDateFilter === 'month') {
          // Show items due within the next 30 days
          const monthLater = new Date(today);
          monthLater.setDate(monthLater.getDate() + 30);

          if (requestDate < today || requestDate > monthLater) {
            return false;
          }
        } else if (dueDateFilter === 'custom' && request.request_date) {
          // Handle custom date if implemented
          return true;
        }
      }

      // Filter by search term
      if (searchTerm.trim() !== '') {
        const searchLower = searchTerm.toLowerCase();
        return (
          (request.customer_name && request.customer_name.toLowerCase().includes(searchLower)) ||
          (request.company_name && request.company_name.toLowerCase().includes(searchLower))
          // (request.pickup_address && request.pickup_address.toLowerCase().includes(searchLower)) ||
          // (request.delivery_address && request.delivery_address.toLowerCase().includes(searchLower))
          // (request.vehicle_info && request.vehicle_info.toLowerCase().includes(searchLower))
        );
      }

      return true;
    });

    // Apply sorting based on sortField and sortDirection
    filtered.sort((a, b) => {
      // For dropdown sort (only applies to updated_at)
      if (sortField === 'updated_at' && sortOrder) {
        const dateA = new Date(a.updated_at);
        const dateB = new Date(b.updated_at);

        if (sortOrder === 'newest') {
          return dateB - dateA; // Newest first
        } else {
          return dateA - dateB; // Oldest first
        }
      }

      // For table header sorting
      const valueA = a[sortField];
      const valueB = b[sortField];

      // Handle different data types
      if (sortField === 'created_at' || sortField === 'updated_at' || sortField === 'request_date') {
        // Date comparison
        const dateA = valueA ? new Date(valueA) : new Date(0);
        const dateB = valueB ? new Date(valueB) : new Date(0);

        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
      } else {
        // String comparison
        const strA = String(valueA || '').toLowerCase();
        const strB = String(valueB || '').toLowerCase();

        if (sortDirection === 'asc') {
          return strA.localeCompare(strB);
        } else {
          return strB.localeCompare(strA);
        }
      }
    });

    return filtered;
  };

  const filteredRequests = filterRequests();

  // Get paginated data
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredRequests.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    const newItemsPerPage = parseInt(e.target.value, 10);
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
    localStorage.setItem('ticketItemsPerPage', newItemsPerPage.toString());
  };

  // Handle table header click for sorting
  const handleSort = (field) => {
    // If clicking the same field, toggle direction
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // If clicking a new field, set it as the sort field with default direction
      setSortField(field);
      // For dates, default to descending (newest first)
      if (field === 'created_at' || field === 'updated_at' || field === 'request_date') {
        setSortDirection('desc');
      } else {
        setSortDirection('asc');
      }
    }
  };

  // Calculate filtered status counts (excluding the status filter itself)
  const getFilteredStatusCounts = () => {
    // Apply all filters except the status filter
    return requests.filter(request => {
      // Filter by request type
      if (selectedRequestType && request.request_type !== selectedRequestType) {
        return false;
      }

      // Filter by due date
      if (dueDateFilter && request.request_date) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const requestDate = new Date(request.request_date);
        requestDate.setHours(0, 0, 0, 0);

        if (dueDateFilter === 'today') {
          // Show only items due today
          const todayDate = new Date();
          todayDate.setHours(0, 0, 0, 0);

          if (requestDate.getTime() !== todayDate.getTime()) {
            return false;
          }
        } else if (dueDateFilter === 'week') {
          // Show items due within the next 7 days
          const weekLater = new Date(today);
          weekLater.setDate(weekLater.getDate() + 7);

          if (requestDate < today || requestDate > weekLater) {
            return false;
          }
        } else if (dueDateFilter === 'month') {
          // Show items due within the next 30 days
          const monthLater = new Date(today);
          monthLater.setDate(monthLater.getDate() + 30);

          if (requestDate < today || requestDate > monthLater) {
            return false;
          }
        } else if (dueDateFilter === 'custom' && request.request_date) {
          // Handle custom date if implemented
          return true;
        }
      }

      // Filter by search term
      if (searchTerm.trim() !== '') {
        const searchLower = searchTerm.toLowerCase();
        return (
          (request.customer_name && request.customer_name.toLowerCase().includes(searchLower)) ||
          (request.company_name && request.company_name.toLowerCase().includes(searchLower))
          // (request.pickup_address && request.pickup_address.toLowerCase().includes(searchLower)) ||
          // (request.delivery_address && request.delivery_address.toLowerCase().includes(searchLower)) ||
          // (request.vehicle_info && request.vehicle_info.toLowerCase().includes(searchLower))
        );
      }

      return true;
    }).reduce((counts, request) => {
      counts[request.current_status] = (counts[request.current_status] || 0) + 1;
      return counts;
    }, {});
  };

  const filteredStatusCounts = getFilteredStatusCounts();
  const totalFilteredCount = Object.values(filteredStatusCounts).reduce((sum, count) => sum + count, 0);

  const clearFilters = () => {
    setSelectedRequestType('');
    setSelectedStatus('');
    localStorage.removeItem('requestStatus');
    setDueDateFilter('');
    setSearchTerm('');
    setSortOrder('newest');
  };

  // Function to get staff name by ID
  const getStaffName = (staffId) => {
    if (!staffId) return 'Not assigned';

    const staff = staffList.find(s => (s.id || s._id) === staffId);
    if (staff) {
      return staff.display_name || staff.name || staff.fullName || staffId;
    }
    return staffId; // Return ID if staff not found
  };

  const refreshRequests = async () => {
    setLoading(true);
    try {
      const userData = localStorage.getItem('user');
      if (!userData) {
        navigate('/login');
        return;
      }

      // Refresh staff list first
      await fetchStaffList();

      const user = JSON.parse(userData);
      const response = await apiService.get(ENDPOINTS.TICKET.GET_BY_USER(user.id));
      setRequests(response);

      // Calculate status counts
      const counts = response.reduce((acc, req) => {
        acc[req.status] = (acc[req.status] || 0) + 1;
        return acc;
      }, {});
      setStatusCounts(counts);
    } catch (err) {
      console.error('Error refreshing requests:', err);
      setError('Failed to refresh requests');
    } finally {
      setLoading(false);
    }
  };

  const getStatusClass = (status) => {
    // Map status to appropriate CSS class to match button colors
    switch (status?.toLowerCase()) {
      case 'initial':
        return 'initial';
      case 'approved':
        return 'approved';
      case 'delivered':
      case 'progress':
        return 'progress'; // Use progress class for delivered status
      case 'customerpaid':
        return 'customerpaid'; // Use rejected class for paid statuses
      case 'supplierpaid':
        return 'supplierpaid'; // Use rejected class for paid statuses
      case 'completed':
        return 'done';
      case 'rejected':
        return 'rejected';
      case 'priced':
        return 'pending';
      default:
        return status || '';
    }
  };

  // Function to get Vietnamese status name
  const getStatusName = (status) => {
    switch (status?.toLowerCase()) {
      case 'initial':
        return 'Khởi tạo';
      case 'approved':
        return 'Đã duyệt';
      case 'delivered':
        return 'Đã giao hàng';
      case 'customerpaid':
        return 'Đã thu tiền khách hàng';
      case 'supplierpaid':
        return 'Đã trả tiền nhà cung cấp';
      case 'completed':
        return 'Hoàn thành';
      case 'rejected':
        return 'Đã hủy';
      case 'priced':
        return 'Đã định giá';
      case 'progress':
        return 'Đang xử lý';
      default:
        return status || '';
    }
  };

  if (loading) {
    return <div className="content-container">Loading...</div>;
  }

  if (error) {
    return <div className="content-container">{error}</div>;
  }

  return (
    <div className="content-container">
      <div className="request-page-header">
        <div className="request-filters">

          <div className="request-filter-group">
            <label>Loại yêu cầu:</label>
            <select
              value={selectedRequestType}
              onChange={(e) => setSelectedRequestType(e.target.value)}
              className="request-select"
            >
              <option value="">Tất cả</option>
            </select>
          </div>

          <div className="request-filter-group">
            <label>Ngày yêu cầu:</label>
            <select
              className="request-select"
              onChange={(e) => setDueDateFilter(e.target.value)}
              value={dueDateFilter}
            >
              <option value="">Tất cả</option>
              <option value="today">Hôm nay</option>
              <option value="week">Tuần này</option>
              <option value="month">Tháng này</option>
            </select>
          </div>

          <div className="request-filter-group">
            <label>Sắp xếp:</label>
            <select
              className="request-select"
              onChange={(e) => setSortOrder(e.target.value)}
              value={sortOrder}
            >
              <option value="newest">Mới nhất</option>
              <option value="oldest">Cũ nhất</option>
            </select>
          </div>

          <div className="request-filter-group">
            <label>Số mục mỗi trang:</label>
            <select
              className="request-select"
              onChange={handleItemsPerPageChange}
              value={itemsPerPage}
            >
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>

        <div className="request-search">
          <div className="request-search-container">
            <span className="search-icon"><FiSearch /></span>
            <input
              type="text"
              placeholder="Tìm kiếm yêu cầu..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="request-search-input"
            />
          </div>
        </div>

        <div className="request-actions">
          <button
            className="request-btn-refresh"
            onClick={refreshRequests}
            title="Refresh"
          >
            <FiRefreshCw />
          </button>
          <button
            className="request-btn-view-mode"
            onClick={() => {
              const newMode = viewMode === 'grid' ? 'table' : 'grid';
              setViewMode(newMode);
              localStorage.setItem('requestViewMode', newMode);
              // Reset to first page when switching views
              setCurrentPage(1);
            }}
            title={viewMode === 'grid' ? 'Switch to Table View' : 'Switch to Grid View'}
          >
            {viewMode === 'grid' ? <FiList /> : <FiGrid />}
          </button>
        </div>

        <div className="request-status-count">
          <button
            className={`request-status-btn btn-all ${selectedStatus === '' ? 'active' : ''}`}
            onClick={() => {
              setSelectedStatus('');
              localStorage.removeItem('requestStatus');
            }}
          >
            Tất cả ({totalFilteredCount})
          </button>
          <button
            className={`request-status-btn btn-initial ${selectedStatus === 'initial' ? 'active' : ''}`}
            onClick={() => {
              setSelectedStatus('initial');
              localStorage.setItem('requestStatus', 'initial');
            }}
          >
            Khởi tạo ({filteredStatusCounts.initial || 0})
          </button>
          <button
            className={`request-status-btn btn-approved ${selectedStatus === 'approved' ? 'active' : ''}`}
            onClick={() => {
              setSelectedStatus('approved');
              localStorage.setItem('requestStatus', 'approved');
            }}
          >
            Đã duyệt ({filteredStatusCounts.approved || 0})
          </button>
          <button
            className={`request-status-btn btn-progress ${selectedStatus === 'delivered' ? 'active' : ''}`}
            onClick={() => {
              setSelectedStatus('delivered');
              localStorage.setItem('requestStatus', 'delivered');
            }}
          >
            Đã giao hàng ({filteredStatusCounts.delivered || 0})
          </button>
          <button
            className={`request-status-btn btn-customerpaid ${selectedStatus === 'customerpaid' ? 'active' : ''}`}
            onClick={() => {
              setSelectedStatus('customerpaid');
              localStorage.setItem('requestStatus', 'customerpaid');
            }}
          >
            Đã thu tiền KH ({filteredStatusCounts.customerpaid || 0})
          </button>
          <button
            className={`request-status-btn btn-supplierpaid ${selectedStatus === 'supplierpaid' ? 'active' : ''}`}
            onClick={() => {
              setSelectedStatus('supplierpaid');
              localStorage.setItem('requestStatus', 'supplierpaid');
            }}
          >
            Đã trả tiền NCC ({filteredStatusCounts.supplierpaid || 0})
          </button>
          <button
            className={`request-status-btn btn-done ${selectedStatus === 'completed' ? 'active' : ''}`}
            onClick={() => {
              setSelectedStatus('completed');
              localStorage.setItem('requestStatus', 'completed');
            }}
          >
            Hoàn thành ({filteredStatusCounts.completed || 0})
          </button>
          {/* <button
            className={`request-status-btn btn-rejected ${selectedStatus === 'rejected' ? 'active' : ''}`}
            onClick={() => {
              setSelectedStatus('rejected');
              localStorage.setItem('requestStatus', 'rejected');
            }}
          >
            Rejected ({filteredStatusCounts.rejected || 0})
          </button> */}
        </div>
      </div>

      {filteredRequests.length === 0 ? (
        <div className="request-empty">
          <p>Không tìm thấy yêu cầu nào phù hợp với bộ lọc của bạn.</p>
        </div>
      ) : viewMode === 'grid' ? (
        <div className="request-grid-container">
          <div className="request-list">
            {currentItems.map((request) => (
              <div
                key={request.id}
                className="request-item"
                onClick={() => handleRequestClick(request.id)}
              >
                <div className="request-item-header">
                  <h3 className="request-item-title">{request.customer_name || "Khách hàng"}</h3>
                  <span className={`status-badge ${getStatusClass(request.current_status)}`}>
                    {getStatusName(request.current_status)}
                  </span>
                </div>
                <div className="request-item-info">
                  <p className="request-item-type">
                    <span className="request-item-label">Công ty:</span> {request.company_name}
                  </p>
                  <p className="request-item-platform">
                    <span className="request-item-label">Địa chỉ lấy hàng:</span> {request.pickup_address}
                  </p>
                  <p className="request-item-platform">
                    <span className="request-item-label">Địa chỉ giao hàng:</span> {request.delivery_address}
                  </p>
                  {request.request_date && (
                    <p className="request-item-date">
                      <span className="request-item-label">Ngày yêu cầu:</span> {request.request_date}
                    </p>
                  )}
                  <p className="request-item-platform">
                    <span className="request-item-label">NV bán hàng:</span> {getStaffName(request.sale_staff_id)}
                  </p>
                  <p className="request-item-platform">
                    <span className="request-item-label">NV định giá:</span> {getStaffName(request.pricing_staff_id)}
                  </p>
                  <p className="request-item-platform">
                    <span className="request-item-label">NV điều phối:</span> {getStaffName(request.dispatch_staff_id)}
                  </p>
                </div>
                <div className="request-item-footer">
                  <span className="request-item-created">
                    Ngày tạo: {new Date(request.created_at).toLocaleDateString()} - {new Date(request.created_at).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination for grid view */}
          {totalPages > 1 && (
            <div className="pagination">
              <button
                className="pagination-btn"
                onClick={() => handlePageChange(1)}
                disabled={currentPage === 1}
              >
                &laquo;
              </button>

              <button
                className="pagination-btn"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                &lsaquo;
              </button>

              {/* Page numbers */}
              {[...Array(totalPages).keys()].map(number => {
                // Show limited page numbers with ellipsis
                const pageNumber = number + 1;

                // Always show first page, last page, current page, and pages around current page
                if (
                  pageNumber === 1 ||
                  pageNumber === totalPages ||
                  (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                ) {
                  return (
                    <button
                      key={pageNumber}
                      className={`pagination-btn ${currentPage === pageNumber ? 'active' : ''}`}
                      onClick={() => handlePageChange(pageNumber)}
                    >
                      {pageNumber}
                    </button>
                  );
                }

                // Show ellipsis
                if (
                  (pageNumber === 2 && currentPage > 3) ||
                  (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
                ) {
                  return <span key={pageNumber} className="pagination-ellipsis">...</span>;
                }

                return null;
              })}

              <button
                className="pagination-btn"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                &rsaquo;
              </button>

              <button
                className="pagination-btn"
                onClick={() => handlePageChange(totalPages)}
                disabled={currentPage === totalPages}
              >
                &raquo;
              </button>
            </div>
          )}

          <div className="pagination-info">
            Hiển thị {indexOfFirstItem + 1} đến {Math.min(indexOfLastItem, filteredRequests.length)} của {filteredRequests.length} mục
          </div>
        </div>
      ) : (
        <div className="request-table-container">

          <table className="tickets-table">
            <thead>
              <tr>
                <th onClick={() => handleSort('customer_name')} className="sortable-header">
                  Khách hàng
                  {sortField === 'customer_name' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
                <th onClick={() => handleSort('company_name')} className="sortable-header">
                  Công ty
                  {sortField === 'company_name' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
                <th onClick={() => handleSort('pickup_address')} className="sortable-header">
                  Địa chỉ lấy hàng
                  {sortField === 'pickup_address' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
                <th onClick={() => handleSort('delivery_address')} className="sortable-header">
                  Địa chỉ giao hàng
                  {sortField === 'delivery_address' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
                <th onClick={() => handleSort('request_date')} className="sortable-header">
                  Ngày yêu cầu
                  {sortField === 'request_date' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
                <th onClick={() => handleSort('sale_staff_id')} className="sortable-header">
                  NV bán hàng
                  {sortField === 'sale_staff_id' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
                <th onClick={() => handleSort('pricing_staff_id')} className="sortable-header">
                  NV định giá
                  {sortField === 'pricing_staff_id' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
                <th onClick={() => handleSort('dispatch_staff_id')} className="sortable-header">
                  NV điều phối
                  {sortField === 'dispatch_staff_id' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
                <th onClick={() => handleSort('current_status')} className="sortable-header">
                  Trạng thái
                  {sortField === 'current_status' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
                <th onClick={() => handleSort('created_at')} className="sortable-header">
                  Ngày tạo
                  {sortField === 'created_at' && (
                    <span className="sort-icon">
                      {sortDirection === 'asc' ? <FiChevronUp /> : <FiChevronDown />}
                    </span>
                  )}
                </th>
              </tr>
            </thead>
            <tbody>
              {currentItems.map((request) => (
                <tr key={request.id} onClick={() => handleRequestClick(request.id)} style={{ cursor: 'pointer' }}>
                  <td>{request.customer_name || "Khách hàng"}</td>
                  <td>{request.company_name}</td>
                  <td>{request.pickup_address}</td>
                  <td>{request.delivery_address}</td>
                  <td>{request.request_date}</td>
                  <td>{getStaffName(request.sale_staff_id)}</td>
                  <td>{getStaffName(request.pricing_staff_id)}</td>
                  <td>{getStaffName(request.dispatch_staff_id)}</td>
                  <td>
                    <span className={`status-badge ${getStatusClass(request.current_status)}`}>
                      {getStatusName(request.current_status)}
                    </span>
                  </td>
                  <td>{new Date(request.created_at).toLocaleDateString()} {new Date(request.created_at).toLocaleTimeString()}</td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="pagination">
              <button
                className="pagination-btn"
                onClick={() => handlePageChange(1)}
                disabled={currentPage === 1}
              >
                &laquo;
              </button>

              <button
                className="pagination-btn"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                &lsaquo;
              </button>

              {/* Page numbers */}
              {[...Array(totalPages).keys()].map(number => {
                // Show limited page numbers with ellipsis
                const pageNumber = number + 1;

                // Always show first page, last page, current page, and pages around current page
                if (
                  pageNumber === 1 ||
                  pageNumber === totalPages ||
                  (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                ) {
                  return (
                    <button
                      key={pageNumber}
                      className={`pagination-btn ${currentPage === pageNumber ? 'active' : ''}`}
                      onClick={() => handlePageChange(pageNumber)}
                    >
                      {pageNumber}
                    </button>
                  );
                }

                // Show ellipsis
                if (
                  (pageNumber === 2 && currentPage > 3) ||
                  (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
                ) {
                  return <span key={pageNumber} className="pagination-ellipsis">...</span>;
                }

                return null;
              })}

              <button
                className="pagination-btn"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                &rsaquo;
              </button>

              <button
                className="pagination-btn"
                onClick={() => handlePageChange(totalPages)}
                disabled={currentPage === totalPages}
              >
                &raquo;
              </button>
            </div>
          )}

          <div className="pagination-info">
            Hiển thị {indexOfFirstItem + 1} đến {Math.min(indexOfLastItem, filteredRequests.length)} của {filteredRequests.length} mục
          </div>
        </div>
      )}
    </div>
  );
};

export default Requests;