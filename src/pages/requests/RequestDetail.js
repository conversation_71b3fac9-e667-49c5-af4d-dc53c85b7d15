import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  FiArrowLeft,
  FiCalendar,
  FiUser,
  FiMapPin,
  FiTruck,
  FiFileText,
  FiDollarSign,
  FiClock,
  FiActivity,
  FiCheckCircle,
  FiEdit,
  FiInfo,
  FiUsers,
  FiBarChart2,
  FiX,
  FiPrinter,
} from "react-icons/fi";
import apiService from "../../services/api.service";
import { ENDPOINTS } from "../../config/api.config";
import "../../styles/RequestDetail.css";

const TicketChiTiet = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [ticket, setTicket] = useState(null);
  const [ticketLogs, setTicketLogs] = useState([]);
  const [ticketPay, setTicketPay] = useState([]);
  const [ticketReceive, setTicketReceive] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [customerLoading, setCustomerLoading] = useState(true);
  const [suppliers, setSuppliers] = useState([]);
  const [supplierLoading, setSupplierLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(true);
  const ticketIdRef = useRef(null);
  const [userRole, setUserRole] = useState(null);
  const [currentUserId, setCurrentUserId] = useState(null);

  const [editingSections, setEditingSections] = useState({});
  const [editedValues, setEditedValues] = useState({});
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("");
  const [showPrintModal, setShowPrintModal] = useState(false);
  const printFrameRef = useRef(null);
  const customerSearchRef = useRef(null);
  const supplierSearchRef = useRef(null);
  const pricingStaffSearchRef = useRef(null);
  const dispatchStaffSearchRef = useRef(null);
  const saleStaffSearchRef = useRef(null);

  // Get user role and ID from localStorage
  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      const parsedUser = JSON.parse(userData);
      setUserRole(parsedUser.role);
      setCurrentUserId(parsedUser.id);
    }
  }, []);

  // Handle clicks outside of search dropdowns
  useEffect(() => {
    function handleClickOutside(event) {
      // Handle customer dropdown
      if (
        customerSearchRef.current &&
        !customerSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showCustomerDropdown: false,
        }));
      }

      // Handle supplier dropdown
      if (
        supplierSearchRef.current &&
        !supplierSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showSupplierDropdown: false,
        }));
      }

      // Handle pricing staff dropdown
      if (
        pricingStaffSearchRef.current &&
        !pricingStaffSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showPricingStaffDropdown: false,
        }));
      }

      // Handle dispatch staff dropdown
      if (
        dispatchStaffSearchRef.current &&
        !dispatchStaffSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showDispatchStaffDropdown: false,
        }));
      }

      // Handle sale staff dropdown
      if (
        saleStaffSearchRef.current &&
        !saleStaffSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showSaleStaffDropdown: false,
        }));
      }

      // Handle supplier dropdowns in Buy Price section
      if (editingSections["buy-price"]) {
        // Create a new editedValues object without any showSupplierDropdown keys
        const newEditedValues = { ...editedValues };
        let hasChanges = false;

        Object.keys(newEditedValues).forEach((key) => {
          if (key.startsWith("showSupplierDropdown_")) {
            // Check if the clicked element is inside the dropdown
            const dropdownIndex = key.split("_")[1];
            const dropdownElement = document.querySelector(
              `#supplier-dropdown-${dropdownIndex}`
            );

            if (dropdownElement && !dropdownElement.contains(event.target)) {
              delete newEditedValues[key];
              hasChanges = true;
            }
          }
        });

        if (hasChanges) {
          setEditedValues(newEditedValues);
        }
      }
    }

    // Add event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // Remove event listener on cleanup
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [editedValues, editingSections]);

  // Fetch customer data
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setCustomerLoading(true);
        const response = await apiService.get(ENDPOINTS.CUSTOMERS.GET_BASIC);
        // console.log('Customer API response:', response); // Log the response to see its structure

        // Check different possible response structures
        if (response && response.customers) {
          setCustomers(response.customers);
        } else if (response && Array.isArray(response)) {
          // If the response itself is an array
          setCustomers(response);
        } else if (response && response.data) {
          // If the data is in a 'data' property
          setCustomers(response.data);
        } else {
          console.error("Unexpected response structure:", response);
        }
      } catch (err) {
        console.error("Error fetching customers:", err);
      } finally {
        setCustomerLoading(false);
      }
    };

    fetchCustomers();
  }, []);

  // Fetch supplier data
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        setSupplierLoading(true);
        const response = await apiService.get(ENDPOINTS.SUPPLIERS.GET_BASIC);
        // console.log('Supplier API response:', response); // Log the response to see its structure

        // Check different possible response structures
        if (response && response.suppliers) {
          setSuppliers(response.suppliers);
        } else if (response && Array.isArray(response)) {
          // If the response itself is an array
          setSuppliers(response);
        } else if (response && response.data) {
          // If the data is in a 'data' property
          setSuppliers(response.data);
        } else {
          console.error("Unexpected response structure:", response);
        }
      } catch (err) {
        console.error("Error fetching suppliers:", err);
      } finally {
        setSupplierLoading(false);
      }
    };

    fetchSuppliers();
  }, []);

  // Fetch users data
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setUsersLoading(true);
        const response = await apiService.get(ENDPOINTS.USERS.GET_BASIC);
        // console.log('Users API response:', response); // Log the response to see its structure

        let usersList = [];
        // Check different possible response structures
        if (response && response.users) {
          usersList = response.users;
        } else if (response && Array.isArray(response)) {
          // If the response itself is an array
          usersList = response;
        } else if (response && response.data) {
          // If the data is in a 'data' property
          usersList = response.data;
        } else {
          console.error("Unexpected response structure:", response);
          setUsersLoading(false);
          return;
        }

        // Set users with all users for reference
        setUsers(usersList);
      } catch (err) {
        console.error("Error fetching users:", err);
      } finally {
        setUsersLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Fetch ticket details
  useEffect(() => {
    const fetchTicketDetails = async () => {
      try {
        let isMounted = true;
        const ticketId = new URLSearchParams(location.search).get("id");

        // Skip if the same request ID
        if (ticketId === ticketIdRef.current) {
          return;
        }
        ticketIdRef.current = ticketId;

        const fetchRequest = async () => {
          if (!ticketId) {
            setError("Request ID is required");
            return;
          }

          try {
            const response = await apiService.get(
              ENDPOINTS.TICKET.GET(ticketId)
            );
            if (isMounted) {
              setTicket(response.ticket_basic);
              setTicketLogs(response.ticket_logs);
              setTicketPay(response.ticket_pay);
              setTicketReceive(response.ticket_receive);
            }
          } catch (err) {
            console.error("Error fetching request:", err);
            if (isMounted) {
              setError("Failed to load request details");
            }
          } finally {
            if (isMounted) {
              setLoading(false);
            }
          }
        };

        fetchRequest();

        return () => {
          isMounted = false;
        };
      } catch (err) {
        console.error("Error setting up mock data:", err);
        setError("Failed to load ticket details");
        setLoading(false);
      }
    };

    fetchTicketDetails();
  }, [location]);

  const handleBack = () => {
    navigate("/tickets");
  };

  // Function to handle printing the ticket
  const handlePrint = () => {
    // Only allow admin users to print
    if (userRole !== 'admin') {
      alert('Chỉ có quản trị viên mới có quyền in phiếu');
      return;
    }

    // Show the print modal
    setShowPrintModal(true);
  };

  // Function to close the print modal
  const closePrintModal = () => {
    setShowPrintModal(false);
  };

  // Function to generate print content
  const getPrintContent = () => {
    // Get customer and staff names
    const customerName = (() => {
      const customer = customers.find(
        (c) => (c.id || c._id) === ticket.customer_id
      );
      return customer
        ? customer.customer_name || customer.name || customer.fullName
        : ticket.customer_id;
    })();

    const pricingStaffName = (() => {
      const staff = users.find(
        (u) => (u.id || u._id) === ticket.pricing_staff_id
      );
      return staff
        ? staff.display_name || staff.name || staff.fullName
        : ticket.pricing_staff_id;
    })();

    const dispatchStaffName = (() => {
      const staff = users.find(
        (u) => (u.id || u._id) === ticket.dispatch_staff_id
      );
      return staff
        ? staff.display_name || staff.name || staff.fullName
        : ticket.dispatch_staff_id;
    })();

    const saleStaffName = (() => {
      const staff = users.find((u) => (u.id || u._id) === ticket.sale_staff_id);
      return staff
        ? staff.display_name || staff.name || staff.fullName
        : ticket.sale_staff_id;
    })();

    // Calculate totals
    const buyTotal = ticketPay.reduce(
      (sum, item) => sum + item.amount_aft_tax,
      0
    );
    const sellTotal = ticketReceive.reduce(
      (sum, item) => sum + item.amount_aft_tax,
      0
    );

    return `
    <!DOCTYPE html>
    <html lang="vi">
    <head>
        <meta charset="UTF-8">
        <title>Phiếu Yêu Cầu Công Việc Phòng XNK #${ticket.id}</title>
        <style>
            @page {
                size: A4;
                margin: 0;
            }
            html {
                background-color: #f0f0f0;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            body {
                font-family: Arial, sans-serif;
                margin: 0 auto;
                padding: 10mm 15mm;
                width: 210mm; /* A4 width */
                min-height: 297mm; /* A4 height */
                font-size: 11pt;
                box-sizing: border-box;
                background-color: white;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
                position: relative;
                overflow: visible;
            }
            /* A4 border outline visible only on screen */
            body::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                border: 1px solid #ccc;
                pointer-events: none;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 10px;
                page-break-inside: avoid; /* Avoid breaking tables across pages */
            }
            th, td {
                border: 1px solid #000;
                padding: 3px 5px;
                text-align: left;
                font-size: 10pt;
            }
            .center {
                text-align: center;
            }
            .bold {
                font-weight: bold;
            }
            .no-border {
                border: none;
            }
            .info-table td {
                width: 25%;
            }
            .double-table td {
                width: 25%;
            }
            .payment-table th, .payment-table td {
                text-align: center;
            }
            .flex-container {
                display: flex;
                gap: 5px;
                justify-content: space-between;
                page-break-inside: avoid; /* Keep the flex container together */
            }
            .half-table.buy {
                flex: 3; /* 3/5 of the width */
                width: 60%;
            }
            .half-table.sell {
                flex: 2; /* 2/5 of the width */
                width: 40%;
            }
            .half-table table {
                width: 100%;
            }
            .status-badge {
                display: inline-block;
                padding: 2px 6px;
                border-radius: 3px;
                font-weight: bold;
                color: white;
                background-color: #777;
                font-size: 9pt;
            }
            .status-badge.initial { background-color: #777; }
            .status-badge.approved { background-color: #2196F3; }
            .status-badge.delivered { background-color: #FF9800; }
            .status-badge.customerpaid { background-color: #4CAF50; }
            .status-badge.supplierpaid { background-color: #9C27B0; }
            .status-badge.completed { background-color: #4CAF50; }
            .signature-section {
                margin-top: 20px;
                display: flex;
                justify-content: space-between;
                page-break-inside: avoid;
            }
            .signature-box {
                width: 30%;
                text-align: center;
            }
            .signature-line {
                margin-top: 40px;
                border-top: 1px solid #333;
            }
            h2 {
                margin: 10px 0 5px 0;
                font-size: 14pt;
                display: block;
                text-align: center;
                page-break-after: avoid;
            }
            h3 {
                margin: 10px 0 5px 0;
                font-size: 12pt;
                display: block;
                text-align: center;
                page-break-after: avoid;
            }
            p {
                margin: 5px 0;
                display: block;
            }
            @media print {
                html {
                    background: none;
                    height: auto;
                    display: block;
                    align-items: initial;
                    justify-content: initial;
                }
                body {
                    padding: 10mm 15mm;
                    margin: 0;
                    box-shadow: none;
                    height: auto;
                    min-height: 0;
                }
                body::before {
                    display: none; /* Hide the A4 border when printing */
                }
                button {
                    display: none;
                }
                html, body {
                    width: 210mm;
                }
            }
        </style>
    </head>
    <body>

        <h2 class="center">ASIA DRAGON LOGISTIC CO., LTD</h2>
        <p class="center">2F Khang Phu, 118/9 Huỳnh Thiện Lộc, Tân Phú, Hồ Chí Minh, Việt Nam<br>
        Tel: (08) 38606799 – Fax: (08) 38606797 – MST: 0313701236</p>

        <h3 class="center bold">PHIẾU YÊU CẦU CÔNG VIỆC PHÒNG XNK</h3>
        <p class="center">Mã phiếu: #${ticket.id} - <span class="status-badge ${ticket.current_status}">${getStatusLabel(ticket.current_status)}</span></p>

        <div class="flex-container" style="margin-bottom: 10px; display: flex;">
            <div style="width: 60%; box-sizing: border-box;">
                <table class="info-table" style="width: 100%;">
                    <tr>
                        <td style="width: 30%; font-weight: bold; text-transform: uppercase;">NGÀY YÊU CẦU:</td>
                        <td style="width: 70%;">${formatDate(ticket.request_date)}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold; text-transform: uppercase;">TÊN KHÁCH HÀNG:</td>
                        <td>${customerName}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold; text-transform: uppercase;">NƠI LẤY HÀNG:</td>
                        <td>${ticket.pickup_address || ''}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold; text-transform: uppercase;">NGƯỜI LIÊN HỆ:</td>
                        <td>${ticket.contact_person || ''}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold; text-transform: uppercase;">NƠI GIAO HÀNG:</td>
                        <td>${ticket.delivery_address || ''}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold; text-transform: uppercase;">NGƯỜI LIÊN HỆ:</td>
                        <td></td>
                    </tr>
                </table>
            </div>
            <div style="width: 40%; box-sizing: border-box;">
                <table class="info-table" style="width: 100%;">
                    <tr>
                        <td colspan="2" style="font-weight: bold; text-transform: uppercase; text-align: center; background-color: #f5f5f5;">THÔNG TIN XE - TÀI XẾ:</td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            ${ticket.vehicle_info ? ticket.vehicle_info.split('\n').join('<br>') : 'Không có thông tin'}
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 30%; font-weight: bold; text-transform: uppercase;">SỐ LƯỢNG:</td>
                        <td style="width: 70%;">${ticket.vehicle_quantity}</td>
                    </tr>
                    <tr>
                        <td colspan="2" style="font-size: 9pt; height: 60px;">
                            ${ticket.vehicle_note ? ticket.vehicle_note : ''}
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <table class="double-table" style="width: 100%; margin-bottom: 20px;">
            <tr style="font-weight: bold">
                <th style="text-align:center">BÁO GIÁ</th>
                <th style="text-align:center">ĐIỀU XE</th>
                <th style="text-align:center">SALE</th>
            </tr>
            <tr>
                <td style="text-align:center">${pricingStaffName || ''}</td>
                <td style="text-align:center">${dispatchStaffName || ''}</td>
                <td style="text-align:center">${saleStaffName || ''}</td>
            </tr>
        </table>

        <div class="flex-container">
            <div class="half-table buy">
                <table class="payment-table">
                    <tr>
                        <th colspan="5">Giá mua</th>
                    </tr>
                    <tr>
                        <th>Diễn giải</th>
                        <th>Số tiền<br>(Chưa VAT)</th>
                        <th>VAT</th>
                        <th>Tổng phải trả</th>
                        <th>Ngày chi tiền & chữ ký</th>
                    </tr>
                    ${(() => {
                      // Generate rows based on ticketPay data
                      let rows = '';

                      // If we have data, use it for the first rows
                      if (ticketPay && ticketPay.length > 0) {
                        for (let i = 0; i < Math.min(ticketPay.length, 8); i++) {
                          const item = ticketPay[i];
                          rows += `
                          <tr>
                            <td>${item.description || ''}</td>
                            <td>${formatCurrency(item.amount_bfr_tax)}</td>
                            <td>${item.tax_rate}%</td>
                            <td>${formatCurrency(item.amount_aft_tax)}</td>
                            <td></td>
                          </tr>
                          `;
                        }
                      }

                      // Add empty rows to reach 8 rows total
                      const emptyRowsNeeded = 8 - (ticketPay ? ticketPay.length : 0);
                      for (let i = 0; i < emptyRowsNeeded; i++) {
                        rows += `
                        <tr>
                          <td>&nbsp;</td>
                          <td></td>
                          <td></td>
                          <td></td>
                          <td></td>
                        </tr>
                        `;
                      }

                      return rows;
                    })()}
                    <tr>
                        <th colspan="3" class="center">Tổng cộng</th>
                        <td>${formatCurrency(buyTotal)}</td>
                        <td></td>
                    </tr>
                </table>
            </div>

            ${userRole !== 'purchaser' ? `
            <div class="half-table sell">
                <table class="payment-table">
                    <tr>
                        <th colspan="2">Giá bán (Đã bao gồm VAT)</th>
                    </tr>
                    <tr>
                        <th>Diễn giải</th>
                        <th>Số tiền<br>(Đã VAT)</th>
                    </tr>
                    ${(() => {
                      // Generate rows based on ticketReceive data
                      let rows = '';

                      // If we have data, use it for the first rows
                      if (ticketReceive && ticketReceive.length > 0) {
                        for (let i = 0; i < Math.min(ticketReceive.length, 8); i++) {
                          const item = ticketReceive[i];
                          rows += `
                          <tr>
                            <td>${item.description || ''}</td>
                            <td>${formatCurrency(item.amount_aft_tax)}</td>
                          </tr>
                          `;
                        }
                      }

                      // Add empty rows to reach 8 rows total
                      const emptyRowsNeeded = 8 - (ticketReceive ? ticketReceive.length : 0);
                      for (let i = 0; i < emptyRowsNeeded; i++) {
                        rows += `
                        <tr>
                          <td>&nbsp;</td>
                          <td></td>
                        </tr>
                        `;
                      }

                      return rows;
                    })()}
                    <tr>
                        <th class="center">Tổng cộng</th>
                        <td>${formatCurrency(sellTotal)}</td>
                    </tr>
                </table>
            </div>
            ` : ''}
        </div>

        <p><strong>Ghi chú khác:</strong> ${ticket.note || 'Không có ghi chú'}</p>

        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line"></div>
                <p><strong>Ban Giám Đốc</strong></p>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <p><strong>Quote CUS</strong></p>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <p><strong>Người Yêu Cầu</strong></p>
            </div>
        </div>

    </body>
    </html>
    `;
  };

  const handleEditSection = (sectionId) => {
    // Prevent sale role from editing staff information
    if (sectionId === "staff-info" && userRole === 'sale') {
      alert('Nhân viên bán hàng không có quyền chỉnh sửa thông tin người phụ trách');
      return;
    }

    // If we're starting to edit, initialize the edited values for this section
    if (!editingSections[sectionId]) {
      if (sectionId === "basic-info") {
        // Find customer name for the current customer_id
        const currentCustomer = customers.find(
          (c) => (c.id || c._id) === ticket.customer_id
        );
        const customerName = currentCustomer
          ? currentCustomer.customer_name ||
            currentCustomer.name ||
            currentCustomer.fullName
          : "";

        // Find supplier name for the current supplier_id
        const currentSupplier = suppliers.find(
          (s) => (s.id || s._id) === ticket.supplier_id
        );
        const supplierName = currentSupplier
          ? currentSupplier.supplier_name ||
            currentSupplier.name ||
            currentSupplier.fullName
          : "";

        setEditedValues((prev) => ({
          ...prev,
          request_date: ticket.request_date,
          report_closing_date: ticket.report_closing_date,
          customer_id: ticket.customer_id,
          customerSearch: customerName, // Initialize search field with current customer name
          showCustomerDropdown: false, // Initially hide the dropdown
          supplier_id: ticket.supplier_id,
          supplierSearch: supplierName, // Initialize search field with current supplier name
          showSupplierDropdown: false, // Initially hide the dropdown
          phone: ticket.phone || "",
          pickup_address: ticket.pickup_address,
          delivery_address: ticket.delivery_address,
        }));

        // If customers haven't been loaded yet, fetch them
        if (customers.length === 0 && !customerLoading) {
          const fetchCustomers = async () => {
            try {
              setCustomerLoading(true);
              const response = await apiService.get(
                ENDPOINTS.CUSTOMERS.GET_BASIC
              );
              // console.log('Customer API response (edit):', response);

              if (response && response.customers) {
                setCustomers(response.customers);
              } else if (response && Array.isArray(response)) {
                setCustomers(response);
              } else if (response && response.data) {
                setCustomers(response.data);
              }
            } catch (err) {
              console.error("Error fetching customers:", err);
            } finally {
              setCustomerLoading(false);
            }
          };

          fetchCustomers();
        }

        // If suppliers haven't been loaded yet, fetch them
        if (suppliers.length === 0 && !supplierLoading) {
          const fetchSuppliers = async () => {
            try {
              setSupplierLoading(true);
              const response = await apiService.get(
                ENDPOINTS.SUPPLIERS.GET_BASIC
              );
              // console.log('Supplier API response (edit):', response);

              if (response && response.suppliers) {
                setSuppliers(response.suppliers);
              } else if (response && Array.isArray(response)) {
                setSuppliers(response);
              } else if (response && response.data) {
                setSuppliers(response.data);
              }
            } catch (err) {
              console.error("Error fetching suppliers:", err);
            } finally {
              setSupplierLoading(false);
            }
          };

          fetchSuppliers();
        }
      } else if (sectionId === "vehicle-info") {
        setEditedValues((prev) => ({
          ...prev,
          vehicle_info: ticket.vehicle_info,
          vehicle_quantity: ticket.vehicle_quantity,
          vehicle_plate: ticket.vehicle_plate,
          vehicle_note: ticket.vehicle_note || "",
        }));
      } else if (sectionId === "staff-info") {
        // Find staff names for the current staff IDs
        const pricingStaff = users.find(
          (u) => (u.id || u._id) === ticket.pricing_staff_id
        );
        const dispatchStaff = users.find(
          (u) => (u.id || u._id) === ticket.dispatch_staff_id
        );
        const saleStaff = users.find(
          (u) => (u.id || u._id) === ticket.sale_staff_id
        );

        const pricingStaffName = pricingStaff
          ? pricingStaff.display_name ||
            pricingStaff.name ||
            pricingStaff.fullName
          : "";
        const dispatchStaffName = dispatchStaff
          ? dispatchStaff.display_name ||
            dispatchStaff.name ||
            dispatchStaff.fullName
          : "";
        const saleStaffName = saleStaff
          ? saleStaff.display_name || saleStaff.name || saleStaff.fullName
          : "";

        setEditedValues((prev) => ({
          ...prev,
          pricing_staff_id: ticket.pricing_staff_id || "",
          dispatch_staff_id: ticket.dispatch_staff_id || "",
          sale_staff_id: ticket.sale_staff_id || "",
          pricingStaffSearch: pricingStaffName,
          dispatchStaffSearch: dispatchStaffName,
          saleStaffSearch: saleStaffName,
          showPricingStaffDropdown: false,
          showDispatchStaffDropdown: false,
          showSaleStaffDropdown: false,
        }));

        // If users haven't been loaded yet, fetch them
        if (users.length === 0 && !usersLoading) {
          const fetchUsers = async () => {
            try {
              setUsersLoading(true);
              const response = await apiService.get(ENDPOINTS.USERS.GET_BASIC);
              // console.log('Users API response (edit):', response);

              if (response && response.users) {
                setUsers(response.users);
              } else if (response && Array.isArray(response)) {
                setUsers(response);
              } else if (response && response.data) {
                setUsers(response.data);
              }
            } catch (err) {
              console.error("Error fetching users:", err);
            } finally {
              setUsersLoading(false);
            }
          };

          fetchUsers();
        }
      } else if (sectionId === "notes") {
        setEditedValues((prev) => ({
          ...prev,
          note: ticket.note || "",
        }));
      } else if (sectionId === "buy-price") {
        // Create a deep copy of the ticketPay array and add supplierSearch field
        setEditedValues((prev) => ({
          ...prev,
          ticketPay: ticketPay.map((item) => {
            // Find supplier name for the current supplier_id
            const supplier = suppliers.find(
              (s) => (s.id || s._id) === item.supplier_id
            );
            const supplierName = supplier
              ? supplier.supplier_name || supplier.name || supplier.fullName
              : "";

            return {
              ...item,
              supplierSearch: supplierName,
            };
          }),
        }));
      } else if (sectionId === "sell-price") {
        // Create a deep copy of the ticketReceive array
        setEditedValues((prev) => ({
          ...prev,
          ticketReceive: ticketReceive.map((item) => ({ ...item })),
        }));
      }
    }

    setEditingSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  };

  const handleInputChange = (field, value) => {
    setEditedValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const [sectionLoading, setSectionLoading] = useState({});
  const [sectionError, setSectionError] = useState({});
  const [sectionSuccess, setSectionSuccess] = useState({});

  const handleConfirm = async (sectionId) => {
    // Set loading state for this section
    setSectionLoading((prev) => ({
      ...prev,
      [sectionId]: true,
    }));

    // Clear previous error and success messages
    setSectionError((prev) => ({
      ...prev,
      [sectionId]: null,
    }));
    setSectionSuccess((prev) => ({
      ...prev,
      [sectionId]: null,
    }));

    try {
      const ticketId = ticket.id;

      // Call the appropriate API endpoint based on the section
      if (sectionId === "basic-info") {
        // Prepare data for basic info update
        const basicData = {
          request_date: editedValues.request_date,
          report_closing_date: editedValues.report_closing_date,
          company_id: editedValues.company_id,
          customer_id: editedValues.customer_id,
          supplier_id: editedValues.supplier_id,
          // phone: editedValues.phone,
          pickup_address: editedValues.pickup_address,
          delivery_address: editedValues.delivery_address,
        };

        // Call API to update basic info
        // console.log("Basic data:", basicData);
        await apiService.post(
          ENDPOINTS.TICKET.UPDATE_BASIC(ticketId),
          basicData
        );

        // Create a new log entry
        const newLog = {
          log_type: "change_basic",
          value: "Cập nhật thông tin cơ bản",
          changed_by: currentUserId,
          created_at: new Date(),
        };

        // Add log to the list
        setTicketLogs((prev) => [newLog, ...prev]);
      } else if (sectionId === "vehicle-info") {
        // Prepare data for vehicle info update
        const vehicleData = {
          vehicle_info: editedValues.vehicle_info,
          vehicle_quantity: editedValues.vehicle_quantity,
          vehicle_plate: editedValues.vehicle_plate,
          vehicle_note: editedValues.vehicle_note,
        };

        // Call API to update vehicle info
        await apiService.post(
          ENDPOINTS.TICKET.UPDATE_VEHICLE(ticketId),
          vehicleData
        );

        // Create a new log entry
        const newLog = {
          log_type: "change_vehicle",
          value: "Cập nhật thông tin xe",
          changed_by: currentUserId,
          created_at: new Date(),
        };

        // Add log to the list
        setTicketLogs((prev) => [newLog, ...prev]);
      } else if (sectionId === "staff-info") {
        // Prepare data for staff info update
        const staffData = {
          pricing_staff_id: editedValues.pricing_staff_id,
          dispatch_staff_id: editedValues.dispatch_staff_id,
          sale_staff_id: editedValues.sale_staff_id,
        };

        // Call API to update staff info
        await apiService.post(
          ENDPOINTS.TICKET.UPDATE_STAFF(ticketId),
          staffData
        );

        // Create a new log entry
        const newLog = {
          log_type: "change_staff",
          value: "Cập nhật người phụ trách",
          changed_by: currentUserId,
          created_at: new Date(),
        };

        // Add log to the list
        setTicketLogs((prev) => [newLog, ...prev]);
      } else if (sectionId === "notes") {
        // Prepare data for notes update
        const notesData = {
          note: editedValues.note,
        };

        // Call API to update notes
        await apiService.post(
          ENDPOINTS.TICKET.UPDATE_NOTE(ticketId),
          notesData
        );

        // Create a new log entry
        const newLog = {
          log_type: "field_change",
          value: "Cập nhật ghi chú",
          changed_by: currentUserId,
          created_at: new Date(),
        };

        // Add log to the list
        setTicketLogs((prev) => [newLog, ...prev]);
      } else if (sectionId === "buy-price") {
        // Prepare data for buy price update
        const buyPriceData = {
          items: editedValues.ticketPay,
        };

        // Call API to update buy price
        await apiService.post(
          ENDPOINTS.TICKET.UPDATE_PAY_AMOUNT(ticketId),
          buyPriceData
        );

        // Update local state
        setTicketPay(editedValues.ticketPay);

        // Create a new log entry
        const newLog = {
          log_type: "change_pay",
          value: "Cập nhật giá mua",
          changed_by: currentUserId,
          created_at: new Date(),
        };

        // Add log to the list
        setTicketLogs((prev) => [newLog, ...prev]);
      } else if (sectionId === "sell-price") {
        // Prepare data for sell price update
        const sellPriceData = {
          items: editedValues.ticketReceive,
        };

        // Call API to update sell price
        await apiService.post(
          ENDPOINTS.TICKET.UPDATE_RECEIVE_AMOUNT(ticketId),
          sellPriceData
        );

        // Update local state
        setTicketReceive(editedValues.ticketReceive);

        // Create a new log entry
        const newLog = {
          log_type: "change_receive",
          value: "Cập nhật giá bán",
          changed_by: currentUserId,
          created_at: new Date(),
        };

        // Add log to the list
        setTicketLogs((prev) => [newLog, ...prev]);
      }

      // Update local state for other sections
      if (sectionId !== "buy-price" && sectionId !== "sell-price") {
        setTicket((prev) => ({
          ...prev,
          ...editedValues,
        }));
      }

      // Set success message
      setSectionSuccess((prev) => ({
        ...prev,
        [sectionId]: "Cập nhật thành công",
      }));

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSectionSuccess((prev) => ({
          ...prev,
          [sectionId]: null,
        }));
      }, 3000);

      // Exit edit mode
      setEditingSections((prev) => ({
        ...prev,
        [sectionId]: false,
      }));
    } catch (error) {
      console.error(`Error updating ${sectionId}:`, error);

      // Set error message
      setSectionError((prev) => ({
        ...prev,
        [sectionId]:
          error.response?.data?.message || "Lỗi khi cập nhật dữ liệu",
      }));
    } finally {
      // Clear loading state
      setSectionLoading((prev) => ({
        ...prev,
        [sectionId]: false,
      }));
    }
  };

  const handleCancel = (sectionId) => {
    // Discard changes and exit edit mode
    setEditingSections((prev) => ({
      ...prev,
      [sectionId]: false,
    }));

    // Close any open supplier dropdowns if canceling buy-price section
    if (sectionId === "buy-price") {
      // Create a new editedValues object without any showSupplierDropdown keys
      const newEditedValues = { ...editedValues };
      Object.keys(newEditedValues).forEach((key) => {
        if (key.startsWith("showSupplierDropdown_")) {
          delete newEditedValues[key];
        }
      });
      setEditedValues(newEditedValues);
    }
  };

  const handleUpdateStatus = () => {
    setSelectedStatus(ticket.current_status);
    setShowStatusModal(true);
  };

  const handleStatusChange = (status) => {
    setSelectedStatus(status);
  };

  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  const [statusUpdateError, setStatusUpdateError] = useState(null);

  const handleConfirmStatusChange = async () => {
    if (!selectedStatus || selectedStatus === ticket.current_status) {
      return;
    }

    setStatusUpdateLoading(true);
    setStatusUpdateError(null);

    try {
      // Prepare data for status update
      const statusData = {
        current_status: selectedStatus,
      };

      // Call API to update status
      await apiService.post(
        ENDPOINTS.TICKET.UPDATE_STATUS(ticket.id),
        statusData
      );

      // Create a new log entry
      const newLog = {
        log_type: "change_status",
        value: selectedStatus,
        changed_by: currentUserId,
        created_at: new Date(),
      };

      // Update ticket and logs
      setTicket((prev) => ({
        ...prev,
        current_status: selectedStatus,
      }));

      setTicketLogs((prev) => [newLog, ...prev]);

      // Close the modal
      setShowStatusModal(false);
    } catch (error) {
      console.error("Error updating status:", error);
      setStatusUpdateError(
        error.response?.data?.message || "Lỗi khi cập nhật trạng thái"
      );
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  // This function is used in the UI to determine if a status can be selected
  // It's kept here for future use in validation logic

  // Functions for price table editing
  const handlePriceItemChange = (type, index, field, value) => {
    const arrayName = type === "buy" ? "ticketPay" : "ticketReceive";

    setEditedValues((prev) => {
      const newArray = [...prev[arrayName]];

      // If it's a numeric field, convert to number
      if (field === "amount_bfr_tax" || field === "tax_rate") {
        const numValue = parseFloat(value) || 0;
        newArray[index] = { ...newArray[index], [field]: numValue };

        // Recalculate amount_aft_tax
        if (field === "amount_bfr_tax" || field === "tax_rate") {
          const amount = newArray[index].amount_bfr_tax;
          const taxRate = newArray[index].tax_rate;
          newArray[index].amount_aft_tax = amount + (amount * taxRate) / 100;
        }
      } else {
        newArray[index] = { ...newArray[index], [field]: value };
      }

      return { ...prev, [arrayName]: newArray };
    });
  };

  const handleAddPriceItem = (type) => {
    const arrayName = type === "buy" ? "ticketPay" : "ticketReceive";

    setEditedValues((prev) => {
      const newItem = {
        description: "",
        amount_bfr_tax: "",
        tax_rate: 10,
        amount_aft_tax: "",
        supplier_id: type === "buy" ? "" : undefined, // Only add supplier_id for buy items
        supplierSearch: type === "buy" ? "" : undefined, // Only add supplierSearch for buy items
      };

      return {
        ...prev,
        [arrayName]: [...prev[arrayName], newItem],
      };
    });
  };

  const handleDeletePriceItem = (type, index) => {
    const arrayName = type === "buy" ? "ticketPay" : "ticketReceive";

    setEditedValues((prev) => {
      const newArray = [...prev[arrayName]];
      newArray.splice(index, 1);

      return { ...prev, [arrayName]: newArray };
    });
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    })
      .format(amount)
      .replace("₫", "VNĐ");
  };

  // Helper function to get user name from user ID
  const getUserNameById = (userId) => {
    if (!userId) return "Hệ thống";

    const user = users.find((u) => (u.id || u._id) === userId);
    if (!user) return userId; // Return the ID if user not found

    return user.display_name || user.name || user.fullName || userId;
  };

  const getStatusLabel = (status) => {
    const labels = {
      initial: "Khởi tạo",
      approved: "Đã duyệt",
      delivered: "Đã giao hàng",
      customerpaid: "Đã thu tiền khách hàng",
      supplierpaid: "Đã trả tiền nhà cung cấp",
      completed: "Hoàn thành",
    };
    return labels[status] || status;
  };

  if (loading) {
    return (
      <div className="loading-container">
        <p>Đang tải thông tin ticket...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <p>{error}</p>
        <button className="btn-primary" onClick={handleBack}>
          Quay lại danh sách
        </button>
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="error-container">
        <p>Không tìm thấy thông tin ticket</p>
        <button className="btn-primary" onClick={handleBack}>
          Quay lại danh sách
        </button>
      </div>
    );
  }

  // Sort timeline statuses in chronological order
  const statusTimeline = [
    { status: "initial", label: "Khởi tạo", date: null, changed_by: null },
    { status: "approved", label: "Đã duyệt", date: null, changed_by: null },
    {
      status: "delivered",
      label: "Đã giao hàng",
      date: null,
      changed_by: null,
    },
    {
      status: "customerpaid",
      label: "Đã thu tiền khách hàng",
      date: null,
      changed_by: null,
    },
    {
      status: "supplierpaid",
      label: "Đã trả tiền nhà cung cấp",
      date: null,
      changed_by: null,
    },
    { status: "completed", label: "Hoàn thành", date: null, changed_by: null },
  ];

  // Fill in the timeline with actual status changes from logs
  if (ticketLogs && ticketLogs.length > 0) {
    ticketLogs.forEach((log) => {
      if (log.log_type === "change_status") {
        const statusItem = statusTimeline.find(
          (item) => item.status === log.value
        );
        if (statusItem) {
          statusItem.date = log.created_at;
          statusItem.changed_by = log.changed_by;
        }
      }
    });
  }

  return (
    <div className="ticket-detail-container">
      <div className="ticket-detail-header">
        <button className="back-button" onClick={handleBack}>
          <FiArrowLeft /> Quay lại
        </button>
        <div className="ticket-detail-title">
          <h2>{ticket.company_name || "Công ty"}</h2>
          <div className="ticket-code">Mã ticket: #{ticket.id}</div>
        </div>
        <span className={`ticket-status ${ticket.current_status}`}>
          {getStatusLabel(ticket.current_status)}
        </span>
        <button className="update-status-button" onClick={handleUpdateStatus}>
          <FiEdit /> Cập nhật trạng thái
        </button>
        {/* Only show print button for admin role */}
        {userRole === 'admin' && (
          <button
            className="print-button"
            onClick={handlePrint}
            style={{
              marginLeft: "10px",
              backgroundColor: "#4a90e2",
              color: "white",
              border: "none",
              borderRadius: "4px",
              padding: "8px 16px",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              fontSize: "14px",
            }}
          >
            <FiPrinter style={{ marginRight: "5px" }} /> In phiếu
          </button>
        )}
      </div>

      <div className="ticket-detail-content">
        {/* Row 1: Basic Info & Vehicle Info */}
        <div className="ticket-detail-row">
          {/* Basic Info */}
          <div
            className="ticket-detail-section"
            style={{ position: "relative" }}
          >
            <h3>
              <FiInfo /> Thông tin cơ bản
              {sectionSuccess["basic-info"] && (
                <span
                  style={{
                    fontSize: "14px",
                    color: "#4caf50",
                    marginLeft: "10px",
                  }}
                >
                  ✓ {sectionSuccess["basic-info"]}
                </span>
              )}
              {sectionError["basic-info"] && (
                <span
                  style={{
                    fontSize: "14px",
                    color: "#f44336",
                    marginLeft: "10px",
                  }}
                >
                  ⚠ {sectionError["basic-info"]}
                </span>
              )}
            </h3>
            {editingSections["basic-info"] ? (
              <div
                style={{
                  position: "absolute",
                  top: "15px",
                  right: "15px",
                  display: "flex",
                  gap: "10px",
                }}
              >
                <button
                  onClick={() => handleConfirm("basic-info")}
                  disabled={sectionLoading["basic-info"]}
                  style={{
                    background: "#4caf50",
                    border: "none",
                    borderRadius: "4px",
                    padding: "5px 10px",
                    cursor: sectionLoading["basic-info"]
                      ? "not-allowed"
                      : "pointer",
                    color: "white",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                    opacity: sectionLoading["basic-info"] ? 0.7 : 1,
                  }}
                >
                  {sectionLoading["basic-info"] ? (
                    <>
                      <span
                        className="loading-spinner"
                        style={{ marginRight: "5px" }}
                      ></span>{" "}
                      Đang xử lý...
                    </>
                  ) : (
                    <>
                      <FiCheckCircle style={{ marginRight: "5px" }} /> Xác nhận
                    </>
                  )}
                </button>
                <button
                  onClick={() => handleCancel("basic-info")}
                  style={{
                    background: "#f44336",
                    border: "none",
                    borderRadius: "4px",
                    padding: "5px 10px",
                    cursor: "pointer",
                    color: "white",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                  }}
                >
                  Huỷ bỏ
                </button>
              </div>
            ) : (
              /* Only show edit button for purchaser and admin roles */
              (userRole === 'purchaser' || userRole === 'admin') && (
                <button
                  onClick={() => handleEditSection("basic-info")}
                  style={{
                    position: "absolute",
                    top: "15px",
                    right: "15px",
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    color: "#4a90e2",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                  }}
                >
                  <FiEdit style={{ marginRight: "5px" }} /> Chỉnh sửa
                </button>
              )
            )}
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiCalendar /> Ngày yêu cầu
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="date"
                  value={
                    editedValues.request_date
                      ? new Date(editedValues.request_date)
                          .toISOString()
                          .split("T")[0]
                      : ""
                  }
                  onChange={(e) =>
                    handleInputChange("request_date", new Date(e.target.value))
                  }
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                  }}
                />
              ) : (
                <div className="detail-value">
                  {formatDate(ticket.request_date)}
                </div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiCalendar /> Ngày chốt báo cáo
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="date"
                  value={
                    editedValues.report_closing_date
                      ? new Date(editedValues.report_closing_date)
                          .toISOString()
                          .split("T")[0]
                      : ""
                  }
                  onChange={(e) =>
                    handleInputChange(
                      "report_closing_date",
                      new Date(e.target.value)
                    )
                  }
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                  }}
                />
              ) : (
                <div className="detail-value">
                  {formatDate(ticket.report_closing_date)}
                </div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiUser /> Khách hàng
              </div>
              {editingSections["basic-info"] ? (
                <div
                  style={{ position: "relative", flex: 1 }}
                  ref={customerSearchRef}
                >
                  <input
                    type="text"
                    value={editedValues.customerSearch || ""}
                    onChange={(e) => {
                      const searchValue = e.target.value;
                      setEditedValues((prev) => ({
                        ...prev,
                        customerSearch: searchValue,
                        showCustomerDropdown: true, // Show dropdown when typing
                      }));
                    }}
                    onFocus={() => {
                      setEditedValues((prev) => ({
                        ...prev,
                        showCustomerDropdown: true, // Show dropdown on focus
                      }));
                    }}
                    placeholder="Gõ để tìm khách hàng..."
                    style={{
                      padding: "5px",
                      borderRadius: "4px",
                      border: "1px solid #ddd",
                      width: "100%",
                    }}
                  />
                  {editedValues.showCustomerDropdown && (
                    <div
                      style={{
                        position: "absolute",
                        top: "100%",
                        left: 0,
                        right: 0,
                        maxHeight: "200px",
                        overflowY: "auto",
                        backgroundColor: "white",
                        border: "1px solid #ddd",
                        borderRadius: "4px",
                        zIndex: 10,
                        boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                      }}
                    >
                      {customers
                        .filter((customer) => {
                          if (!editedValues.customerSearch) return true; // Show all when no search text
                          const customerName =
                            customer.customer_name ||
                            customer.name ||
                            customer.fullName ||
                            "";
                          return customerName
                            .toLowerCase()
                            .includes(
                              editedValues.customerSearch.toLowerCase()
                            );
                        })
                        .map((customer) => (
                          <div
                            key={customer.id || customer._id}
                            onClick={() => {
                              const customerId = customer.id || customer._id;
                              const customerName =
                                customer.customer_name ||
                                customer.name ||
                                customer.fullName ||
                                customerId;
                              handleInputChange("customer_id", customerId);
                              setEditedValues((prev) => ({
                                ...prev,
                                customerSearch: customerName,
                                showCustomerDropdown: false, // Hide dropdown after selection
                              }));
                            }}
                            style={{
                              padding: "8px 12px",
                              cursor: "pointer",
                              borderBottom: "1px solid #eee",
                              backgroundColor:
                                editedValues.customer_id ===
                                (customer.id || customer._id)
                                  ? "#f0f7ff"
                                  : "white",
                            }}
                            onMouseOver={(e) =>
                              (e.currentTarget.style.backgroundColor =
                                "#f5f5f5")
                            }
                            onMouseOut={(e) =>
                              (e.currentTarget.style.backgroundColor =
                                editedValues.customer_id ===
                                (customer.id || customer._id)
                                  ? "#f0f7ff"
                                  : "white")
                            }
                          >
                            {customer.customer_name ||
                              customer.name ||
                              customer.fullName ||
                              `${customer.id || customer._id}`}
                          </div>
                        ))}
                      {customers.filter((customer) => {
                        if (!editedValues.customerSearch) return true; // Show all when no search text
                        const customerName =
                          customer.customer_name ||
                          customer.name ||
                          customer.fullName ||
                          "";
                        return customerName
                          .toLowerCase()
                          .includes(editedValues.customerSearch.toLowerCase());
                      }).length === 0 && (
                        <div style={{ padding: "8px 12px", color: "#999" }}>
                          Không tìm thấy khách hàng
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="detail-value">
                  {customerLoading
                    ? "Đang tải..."
                    : (() => {
                        const customer = customers.find(
                          (c) => (c.id || c._id) === ticket.customer_id
                        );
                        return customer
                          ? customer.customer_name ||
                              customer.name ||
                              customer.fullName
                          : ticket.customer_id;
                      })()}
                </div>
              )}
            </div>

            {/* <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiUser /> Nhà cung cấp
              </div>
              {editingSections["basic-info"] ? (
                <div
                  style={{ position: "relative", flex: 1 }}
                  ref={supplierSearchRef}
                >
                  <input
                    type="text"
                    value={editedValues.supplierSearch || ""}
                    onChange={(e) => {
                      const searchValue = e.target.value;
                      setEditedValues((prev) => ({
                        ...prev,
                        supplierSearch: searchValue,
                        showSupplierDropdown: true, // Show dropdown when typing
                      }));
                    }}
                    onFocus={() => {
                      setEditedValues((prev) => ({
                        ...prev,
                        showSupplierDropdown: true, // Show dropdown on focus
                      }));
                    }}
                    placeholder="Gõ để tìm nhà cung cấp..."
                    style={{
                      padding: "5px",
                      borderRadius: "4px",
                      border: "1px solid #ddd",
                      width: "100%",
                    }}
                  />
                  {editedValues.showSupplierDropdown && (
                    <div
                      style={{
                        position: "absolute",
                        top: "100%",
                        left: 0,
                        right: 0,
                        maxHeight: "200px",
                        overflowY: "auto",
                        backgroundColor: "white",
                        border: "1px solid #ddd",
                        borderRadius: "4px",
                        zIndex: 10,
                        boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                      }}
                    >
                      {suppliers
                        .filter((supplier) => {
                          if (!editedValues.supplierSearch) return true; // Show all when no search text
                          const supplierName =
                            supplier.supplier_name ||
                            supplier.name ||
                            supplier.fullName ||
                            "";
                          return supplierName
                            .toLowerCase()
                            .includes(
                              editedValues.supplierSearch.toLowerCase()
                            );
                        })
                        .map((supplier) => (
                          <div
                            key={supplier.id || supplier._id}
                            onClick={() => {
                              const supplierId = supplier.id || supplier._id;
                              const supplierName =
                                supplier.supplier_name ||
                                supplier.name ||
                                supplier.fullName ||
                                supplierId;
                              handleInputChange("supplier_id", supplierId);
                              setEditedValues((prev) => ({
                                ...prev,
                                supplierSearch: supplierName,
                                showSupplierDropdown: false, // Hide dropdown after selection
                              }));
                            }}
                            style={{
                              padding: "8px 12px",
                              cursor: "pointer",
                              borderBottom: "1px solid #eee",
                              backgroundColor:
                                editedValues.supplier_id ===
                                (supplier.id || supplier._id)
                                  ? "#f0f7ff"
                                  : "white",
                            }}
                            onMouseOver={(e) =>
                              (e.currentTarget.style.backgroundColor =
                                "#f5f5f5")
                            }
                            onMouseOut={(e) =>
                              (e.currentTarget.style.backgroundColor =
                                editedValues.supplier_id ===
                                (supplier.id || supplier._id)
                                  ? "#f0f7ff"
                                  : "white")
                            }
                          >
                            {supplier.supplier_name ||
                              supplier.name ||
                              supplier.fullName ||
                              `${supplier.id || supplier._id}`}
                          </div>
                        ))}
                      {suppliers.filter((supplier) => {
                        if (!editedValues.supplierSearch) return true; // Show all when no search text
                        const supplierName =
                          supplier.supplier_name ||
                          supplier.name ||
                          supplier.fullName ||
                          "";
                        return supplierName
                          .toLowerCase()
                          .includes(editedValues.supplierSearch.toLowerCase());
                      }).length === 0 && (
                        <div style={{ padding: "8px 12px", color: "#999" }}>
                          Không tìm thấy nhà cung cấp
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="detail-value">
                  {supplierLoading
                    ? "Đang tải..."
                    : (() => {
                        const supplier = suppliers.find(
                          (s) => (s.id || s._id) === ticket.supplier_id
                        );
                        return supplier
                          ? supplier.supplier_name ||
                              supplier.name ||
                              supplier.fullName
                          : ticket.supplier_id || "Chưa chọn";
                      })()}
                </div>
              )}
            </div> */}
            {/* <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiPhone /> Số điện thoại
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="tel"
                  value={editedValues.phone || ""}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                  }}
                />
              ) : (
                <div className="detail-value">{ticket.phone || "N/A"}</div>
              )}
            </div> */}
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiMapPin /> Địa chỉ lấy hàng
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="text"
                  value={editedValues.pickup_address || ""}
                  onChange={(e) =>
                    handleInputChange("pickup_address", e.target.value)
                  }
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                  }}
                />
              ) : (
                <div className="detail-value">{ticket.pickup_address}</div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiMapPin /> Địa chỉ giao hàng
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="text"
                  value={editedValues.delivery_address || ""}
                  onChange={(e) =>
                    handleInputChange("delivery_address", e.target.value)
                  }
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                  }}
                />
              ) : (
                <div className="detail-value">{ticket.delivery_address}</div>
              )}
            </div>
          </div>

          {/* Vehicle Info */}
          <div
            className="ticket-detail-section"
            style={{ position: "relative" }}
          >
            <h3>
              <FiTruck /> Thông tin xe
              {sectionSuccess["vehicle-info"] && (
                <span
                  style={{
                    fontSize: "14px",
                    color: "#4caf50",
                    marginLeft: "10px",
                  }}
                >
                  ✓ {sectionSuccess["vehicle-info"]}
                </span>
              )}
              {sectionError["vehicle-info"] && (
                <span
                  style={{
                    fontSize: "14px",
                    color: "#f44336",
                    marginLeft: "10px",
                  }}
                >
                  ⚠ {sectionError["vehicle-info"]}
                </span>
              )}
            </h3>
            {editingSections["vehicle-info"] ? (
              <div
                style={{
                  position: "absolute",
                  top: "15px",
                  right: "15px",
                  display: "flex",
                  gap: "10px",
                }}
              >
                <button
                  onClick={() => handleConfirm("vehicle-info")}
                  disabled={sectionLoading["vehicle-info"]}
                  style={{
                    background: "#4caf50",
                    border: "none",
                    borderRadius: "4px",
                    padding: "5px 10px",
                    cursor: sectionLoading["vehicle-info"]
                      ? "not-allowed"
                      : "pointer",
                    color: "white",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                    opacity: sectionLoading["vehicle-info"] ? 0.7 : 1,
                  }}
                >
                  {sectionLoading["vehicle-info"] ? (
                    <>
                      <span
                        className="loading-spinner"
                        style={{ marginRight: "5px" }}
                      ></span>{" "}
                      Đang xử lý...
                    </>
                  ) : (
                    <>
                      <FiCheckCircle style={{ marginRight: "5px" }} /> Xác nhận
                    </>
                  )}
                </button>
                <button
                  onClick={() => handleCancel("vehicle-info")}
                  style={{
                    background: "#f44336",
                    border: "none",
                    borderRadius: "4px",
                    padding: "5px 10px",
                    cursor: "pointer",
                    color: "white",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                  }}
                >
                  Huỷ bỏ
                </button>
              </div>
            ) : (
              /* Only show edit button for purchaser and admin roles */
              (userRole === 'purchaser' || userRole === 'admin') && (
                <button
                  onClick={() => handleEditSection("vehicle-info")}
                  style={{
                    position: "absolute",
                    top: "15px",
                    right: "15px",
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    color: "#4a90e2",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                  }}
                >
                  <FiEdit style={{ marginRight: "5px" }} /> Chỉnh sửa
                </button>
              )
            )}
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiTruck /> Thông tin xe
              </div>
              {editingSections["vehicle-info"] ? (
                <textarea
                  value={editedValues.vehicle_info || ""}
                  onChange={(e) =>
                    handleInputChange("vehicle_info", e.target.value)
                  }
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                    minHeight: "60px",
                    resize: "vertical",
                  }}
                />
              ) : (
                <div className="detail-value">{ticket.vehicle_info}</div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiTruck /> Số lượng xe
              </div>
              {editingSections["vehicle-info"] ? (
                <input
                  type="text"
                  value={editedValues.vehicle_quantity || ""}
                  onChange={(e) =>
                    handleInputChange("vehicle_quantity", e.target.value)
                  }
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                  }}
                />
              ) : (
                <div className="detail-value">{ticket.vehicle_quantity}</div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiTruck /> Biển số xe (để xuất bảng kê cước vận chuyển)
              </div>
              {editingSections["vehicle-info"] ? (
                <input
                  type="text"
                  value={editedValues.vehicle_plate || ""}
                  onChange={(e) =>
                    handleInputChange("vehicle_plate", e.target.value)
                  }
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                  }}
                />
              ) : (
                <div className="detail-value">{ticket.vehicle_plate}</div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiFileText /> Ghi chú
              </div>
              {editingSections["vehicle-info"] ? (
                <textarea
                  value={editedValues.vehicle_note || ""}
                  onChange={(e) =>
                    handleInputChange("vehicle_note", e.target.value)
                  }
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                    minHeight: "60px",
                    resize: "vertical",
                  }}
                />
              ) : (
                <div className="detail-value">
                  {ticket.vehicle_note || "Không có ghi chú"}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Row 2: Staff Info */}
        <div className="ticket-detail-row">
          {/* Staff Info */}
          <div
            className="ticket-detail-section"
            style={{ position: "relative" }}
          >
            <h3>
              <FiUsers /> Người phụ trách
              {sectionSuccess["staff-info"] && (
                <span
                  style={{
                    fontSize: "14px",
                    color: "#4caf50",
                    marginLeft: "10px",
                  }}
                >
                  ✓ {sectionSuccess["staff-info"]}
                </span>
              )}
              {sectionError["staff-info"] && (
                <span
                  style={{
                    fontSize: "14px",
                    color: "#f44336",
                    marginLeft: "10px",
                  }}
                >
                  ⚠ {sectionError["staff-info"]}
                </span>
              )}
            </h3>
            {editingSections["staff-info"] ? (
              <div
                style={{
                  position: "absolute",
                  top: "15px",
                  right: "15px",
                  display: "flex",
                  gap: "10px",
                }}
              >
                <button
                  onClick={() => handleConfirm("staff-info")}
                  disabled={sectionLoading["staff-info"]}
                  style={{
                    background: "#4caf50",
                    border: "none",
                    borderRadius: "4px",
                    padding: "5px 10px",
                    cursor: sectionLoading["staff-info"]
                      ? "not-allowed"
                      : "pointer",
                    color: "white",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                    opacity: sectionLoading["staff-info"] ? 0.7 : 1,
                  }}
                >
                  {sectionLoading["staff-info"] ? (
                    <>
                      <span
                        className="loading-spinner"
                        style={{ marginRight: "5px" }}
                      ></span>{" "}
                      Đang xử lý...
                    </>
                  ) : (
                    <>
                      <FiCheckCircle style={{ marginRight: "5px" }} /> Xác nhận
                    </>
                  )}
                </button>
                <button
                  onClick={() => handleCancel("staff-info")}
                  style={{
                    background: "#f44336",
                    border: "none",
                    borderRadius: "4px",
                    padding: "5px 10px",
                    cursor: "pointer",
                    color: "white",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                  }}
                >
                  Huỷ bỏ
                </button>
              </div>
            ) : (
              /* Only show edit button for non-sale roles */
              (userRole === 'purchaser' || userRole === 'admin') && (
                <button
                  onClick={() => handleEditSection("staff-info")}
                  style={{
                    position: "absolute",
                    top: "15px",
                    right: "15px",
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    color: "#4a90e2",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                  }}
                >
                  <FiEdit style={{ marginRight: "5px" }} /> Chỉnh sửa
                </button>
              )
            )}
            <div style={{ display: "flex", gap: "20px" }}>
              <div style={{ flex: 1 }}>
                <div className="detail-item">
                  <div className="detail-label">
                    <FiUser /> Nhân viên định giá
                  </div>
                  {editingSections["staff-info"] ? (
                    <div
                      style={{ position: "relative" }}
                      ref={pricingStaffSearchRef}
                    >
                      <input
                        type="text"
                        value={editedValues.pricingStaffSearch || ""}
                        onChange={(e) => {
                          const searchValue = e.target.value;
                          setEditedValues((prev) => ({
                            ...prev,
                            pricingStaffSearch: searchValue,
                            showPricingStaffDropdown: true,
                          }));
                        }}
                        onFocus={() => {
                          setEditedValues((prev) => ({
                            ...prev,
                            showPricingStaffDropdown: true,
                          }));
                        }}
                        placeholder="Gõ để tìm nhân viên..."
                        style={{
                          padding: "5px",
                          borderRadius: "4px",
                          border: "1px solid #ddd",
                          width: "100%",
                        }}
                      />
                      {editedValues.showPricingStaffDropdown && (
                        <div
                          style={{
                            position: "absolute",
                            top: "100%",
                            left: 0,
                            right: 0,
                            maxHeight: "200px",
                            overflowY: "auto",
                            backgroundColor: "white",
                            border: "1px solid #ddd",
                            borderRadius: "4px",
                            zIndex: 10,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                          }}
                        >
                          {users
                            .filter((user) => {
                              if (!editedValues.pricingStaffSearch)
                                return user.role === "purchaser"; // Show all purchasers when no search text
                              const userName =
                                user.display_name ||
                                user.name ||
                                user.fullName ||
                                "";
                              return (
                                user.role === "purchaser" &&
                                userName
                                  .toLowerCase()
                                  .includes(
                                    editedValues.pricingStaffSearch.toLowerCase()
                                  )
                              );
                            })
                            .map((user) => (
                              <div
                                key={user.id || user._id}
                                onClick={() => {
                                  const userId = user.id || user._id;
                                  const userName =
                                    user.display_name ||
                                    user.name ||
                                    user.fullName ||
                                    userId;
                                  handleInputChange("pricing_staff_id", userId);
                                  setEditedValues((prev) => ({
                                    ...prev,
                                    pricingStaffSearch: userName,
                                    showPricingStaffDropdown: false,
                                  }));
                                }}
                                style={{
                                  padding: "8px 12px",
                                  cursor: "pointer",
                                  borderBottom: "1px solid #eee",
                                  backgroundColor:
                                    editedValues.pricing_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white",
                                }}
                                onMouseOver={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    "#f5f5f5")
                                }
                                onMouseOut={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    editedValues.pricing_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white")
                                }
                              >
                                {user.display_name ||
                                  user.name ||
                                  user.fullName ||
                                  `${user.id || user._id}`}
                              </div>
                            ))}
                          {users.filter((user) => {
                            if (!editedValues.pricingStaffSearch) return true; // Show all when no search text
                            const userName =
                              user.display_name ||
                              user.name ||
                              user.fullName ||
                              "";
                            return userName
                              .toLowerCase()
                              .includes(
                                editedValues.pricingStaffSearch.toLowerCase()
                              );
                          }).length === 0 && (
                            <div style={{ padding: "8px 12px", color: "#999" }}>
                              Không tìm thấy nhân viên
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="detail-value">
                      {usersLoading
                        ? "Đang tải..."
                        : (() => {
                            const user = users.find(
                              (u) => (u.id || u._id) === ticket.pricing_staff_id
                            );
                            return user
                              ? user.display_name || user.name || user.fullName
                              : ticket.pricing_staff_id || "Chưa phân công";
                          })()}
                    </div>
                  )}
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <div className="detail-item">
                  <div className="detail-label">
                    <FiUser /> Nhân viên điều phối
                  </div>
                  {editingSections["staff-info"] ? (
                    <div
                      style={{ position: "relative" }}
                      ref={dispatchStaffSearchRef}
                    >
                      <input
                        type="text"
                        value={editedValues.dispatchStaffSearch || ""}
                        onChange={(e) => {
                          const searchValue = e.target.value;
                          setEditedValues((prev) => ({
                            ...prev,
                            dispatchStaffSearch: searchValue,
                            showDispatchStaffDropdown: true,
                          }));
                        }}
                        onFocus={() => {
                          setEditedValues((prev) => ({
                            ...prev,
                            showDispatchStaffDropdown: true,
                          }));
                        }}
                        placeholder="Gõ để tìm nhân viên..."
                        style={{
                          padding: "5px",
                          borderRadius: "4px",
                          border: "1px solid #ddd",
                          width: "100%",
                        }}
                      />
                      {editedValues.showDispatchStaffDropdown && (
                        <div
                          style={{
                            position: "absolute",
                            top: "100%",
                            left: 0,
                            right: 0,
                            maxHeight: "200px",
                            overflowY: "auto",
                            backgroundColor: "white",
                            border: "1px solid #ddd",
                            borderRadius: "4px",
                            zIndex: 10,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                          }}
                        >
                          {users
                            .filter((user) => {
                              if (!editedValues.dispatchStaffSearch)
                                return user.role === "moderator"; // Show all moderators when no search text
                              const userName =
                                user.display_name ||
                                user.name ||
                                user.fullName ||
                                "";
                              return (
                                user.role === "moderator" &&
                                userName
                                  .toLowerCase()
                                  .includes(
                                    editedValues.dispatchStaffSearch.toLowerCase()
                                  )
                              );
                            })
                            .map((user) => (
                              <div
                                key={user.id || user._id}
                                onClick={() => {
                                  const userId = user.id || user._id;
                                  const userName =
                                    user.display_name ||
                                    user.name ||
                                    user.fullName ||
                                    userId;
                                  handleInputChange(
                                    "dispatch_staff_id",
                                    userId
                                  );
                                  setEditedValues((prev) => ({
                                    ...prev,
                                    dispatchStaffSearch: userName,
                                    showDispatchStaffDropdown: false,
                                  }));
                                }}
                                style={{
                                  padding: "8px 12px",
                                  cursor: "pointer",
                                  borderBottom: "1px solid #eee",
                                  backgroundColor:
                                    editedValues.dispatch_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white",
                                }}
                                onMouseOver={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    "#f5f5f5")
                                }
                                onMouseOut={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    editedValues.dispatch_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white")
                                }
                              >
                                {user.display_name ||
                                  user.name ||
                                  user.fullName ||
                                  `${user.id || user._id}`}
                              </div>
                            ))}
                          {users.filter((user) => {
                            if (!editedValues.dispatchStaffSearch) return true; // Show all when no search text
                            const userName =
                              user.display_name ||
                              user.name ||
                              user.fullName ||
                              "";
                            return userName
                              .toLowerCase()
                              .includes(
                                editedValues.dispatchStaffSearch.toLowerCase()
                              );
                          }).length === 0 && (
                            <div style={{ padding: "8px 12px", color: "#999" }}>
                              Không tìm thấy nhân viên
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="detail-value">
                      {usersLoading
                        ? "Đang tải..."
                        : (() => {
                            const user = users.find(
                              (u) =>
                                (u.id || u._id) === ticket.dispatch_staff_id
                            );
                            return user
                              ? user.display_name || user.name || user.fullName
                              : ticket.dispatch_staff_id || "Chưa phân công";
                          })()}
                    </div>
                  )}
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <div className="detail-item">
                  <div className="detail-label">
                    <FiUser /> Nhân viên bán hàng
                  </div>
                  {editingSections["staff-info"] ? (
                    <div
                      style={{ position: "relative" }}
                      ref={saleStaffSearchRef}
                    >
                      <input
                        type="text"
                        value={editedValues.saleStaffSearch || ""}
                        onChange={(e) => {
                          const searchValue = e.target.value;
                          setEditedValues((prev) => ({
                            ...prev,
                            saleStaffSearch: searchValue,
                            showSaleStaffDropdown: true,
                          }));
                        }}
                        onFocus={() => {
                          setEditedValues((prev) => ({
                            ...prev,
                            showSaleStaffDropdown: true,
                          }));
                        }}
                        placeholder="Gõ để tìm nhân viên..."
                        style={{
                          padding: "5px",
                          borderRadius: "4px",
                          border: "1px solid #ddd",
                          width: "100%",
                        }}
                      />
                      {editedValues.showSaleStaffDropdown && (
                        <div
                          style={{
                            position: "absolute",
                            top: "100%",
                            left: 0,
                            right: 0,
                            maxHeight: "200px",
                            overflowY: "auto",
                            backgroundColor: "white",
                            border: "1px solid #ddd",
                            borderRadius: "4px",
                            zIndex: 10,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                          }}
                        >
                          {users
                            .filter((user) => {
                              if (!editedValues.saleStaffSearch)
                                return user.role === "sale"; // Show all sales when no search text
                              const userName =
                                user.display_name ||
                                user.name ||
                                user.fullName ||
                                "";
                              return (
                                user.role === "sale" &&
                                userName
                                  .toLowerCase()
                                  .includes(
                                    editedValues.saleStaffSearch.toLowerCase()
                                  )
                              );
                            })
                            .map((user) => (
                              <div
                                key={user.id || user._id}
                                onClick={() => {
                                  const userId = user.id || user._id;
                                  const userName =
                                    user.display_name ||
                                    user.name ||
                                    user.fullName ||
                                    userId;
                                  handleInputChange("sale_staff_id", userId);
                                  setEditedValues((prev) => ({
                                    ...prev,
                                    saleStaffSearch: userName,
                                    showSaleStaffDropdown: false,
                                  }));
                                }}
                                style={{
                                  padding: "8px 12px",
                                  cursor: "pointer",
                                  borderBottom: "1px solid #eee",
                                  backgroundColor:
                                    editedValues.sale_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white",
                                }}
                                onMouseOver={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    "#f5f5f5")
                                }
                                onMouseOut={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    editedValues.sale_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white")
                                }
                              >
                                {user.display_name ||
                                  user.name ||
                                  user.fullName ||
                                  `${user.id || user._id}`}
                              </div>
                            ))}
                          {users.filter((user) => {
                            if (!editedValues.saleStaffSearch) return true; // Show all when no search text
                            const userName =
                              user.display_name ||
                              user.name ||
                              user.fullName ||
                              "";
                            return userName
                              .toLowerCase()
                              .includes(
                                editedValues.saleStaffSearch.toLowerCase()
                              );
                          }).length === 0 && (
                            <div style={{ padding: "8px 12px", color: "#999" }}>
                              Không tìm thấy nhân viên
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="detail-value">
                      {usersLoading
                        ? "Đang tải..."
                        : (() => {
                            const user = users.find(
                              (u) => (u.id || u._id) === ticket.sale_staff_id
                            );
                            return user
                              ? user.display_name || user.name || user.fullName
                              : ticket.sale_staff_id || "Chưa phân công";
                          })()}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Row 3: Pricing Info */}
        <div className="ticket-detail-row">
          {/* Left column containing both Buy Price and Sell Price - takes up 2/3 of width */}
          <div
            style={{
              flex: 2,
              display: "flex",
              flexDirection: "column",
              gap: "20px",
            }}
          >
            {/* Buy Price - Only visible to purchaser and admin roles, not to moderator */}
            {userRole !== 'moderator' && (
              <div
                className="ticket-detail-section"
                style={{ position: "relative" }}
              >
              <h3>
                <FiDollarSign /> Thông tin giá mua
                {sectionSuccess["buy-price"] && (
                  <span
                    style={{
                      fontSize: "14px",
                      color: "#4caf50",
                      marginLeft: "10px",
                    }}
                  >
                    ✓ {sectionSuccess["buy-price"]}
                  </span>
                )}
                {sectionError["buy-price"] && (
                  <span
                    style={{
                      fontSize: "14px",
                      color: "#f44336",
                      marginLeft: "10px",
                    }}
                  >
                    ⚠ {sectionError["buy-price"]}
                  </span>
                )}
              </h3>
              {editingSections["buy-price"] ? (
                <div
                  style={{
                    position: "absolute",
                    top: "15px",
                    right: "15px",
                    display: "flex",
                    gap: "10px",
                  }}
                >
                  <button
                    onClick={() => handleConfirm("buy-price")}
                    disabled={sectionLoading["buy-price"]}
                    style={{
                      background: "#4caf50",
                      border: "none",
                      borderRadius: "4px",
                      padding: "5px 10px",
                      cursor: sectionLoading["buy-price"]
                        ? "not-allowed"
                        : "pointer",
                      color: "white",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                      opacity: sectionLoading["buy-price"] ? 0.7 : 1,
                    }}
                  >
                    {sectionLoading["buy-price"] ? (
                      <>
                        <span
                          className="loading-spinner"
                          style={{ marginRight: "5px" }}
                        ></span>{" "}
                        Đang xử lý...
                      </>
                    ) : (
                      <>
                        <FiCheckCircle style={{ marginRight: "5px" }} /> Xác
                        nhận
                      </>
                    )}
                  </button>
                  <button
                    onClick={() => handleCancel("buy-price")}
                    style={{
                      background: "#f44336",
                      border: "none",
                      borderRadius: "4px",
                      padding: "5px 10px",
                      cursor: "pointer",
                      color: "white",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                    }}
                  >
                    Huỷ bỏ
                  </button>
                </div>
              ) : (
                /* Only show edit button for purchaser and admin roles */
                (userRole === 'purchaser' || userRole === 'admin') && (
                  <button
                    onClick={() => handleEditSection("buy-price")}
                    style={{
                      position: "absolute",
                      top: "15px",
                      right: "15px",
                      background: "none",
                      border: "none",
                      cursor: "pointer",
                      color: "#4a90e2",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                    }}
                  >
                    <FiEdit style={{ marginRight: "5px" }} /> Chỉnh sửa
                  </button>
                )
              )}
              {editingSections["buy-price"] ? (
                <div>
                  <table className="price-table">
                    <thead>
                      <tr>
                        {/* Hide supplier column for sale role */}
                        {userRole !== 'sale' && <th>Nhà cung cấp</th>}
                        <th>Mô tả</th>
                        <th>Giá trước thuế</th>
                        <th>Thuế (%)</th>
                        <th>Giá sau thuế</th>
                      </tr>
                    </thead>
                    <tbody>
                      {editedValues.ticketPay &&
                        editedValues.ticketPay.map((item, index) => (
                          <tr key={index}>
                            {/* Hide supplier cell for sale role */}
                            {userRole !== 'sale' && (
                              <td>
                                <div style={{ position: "relative" }}>
                                  <input
                                    type="text"
                                    value={item.supplierSearch || ""}
                                    onChange={(e) => {
                                      const searchValue = e.target.value;
                                      // Update the search value in the item
                                      const updatedItem = {
                                        ...item,
                                        supplierSearch: searchValue,
                                      };
                                      const newArray = [
                                        ...editedValues.ticketPay,
                                      ];
                                      newArray[index] = updatedItem;
                                      setEditedValues((prev) => ({
                                        ...prev,
                                        ticketPay: newArray,
                                        [`showSupplierDropdown_${index}`]: true,
                                      }));
                                    }}
                                    onFocus={() => {
                                      setEditedValues((prev) => ({
                                        ...prev,
                                        [`showSupplierDropdown_${index}`]: true,
                                      }));
                                    }}
                                    placeholder="Tìm nhà cung cấp..."
                                    style={{
                                      width: "100%",
                                      padding: "5px",
                                      borderRadius: "4px",
                                      border: "1px solid #ddd",
                                    }}
                                  />
                                  {editedValues[
                                    `showSupplierDropdown_${index}`
                                  ] && (
                                    <div
                                      id={`supplier-dropdown-${index}`}
                                      style={{
                                        position: "absolute",
                                        top: "100%",
                                        left: 0,
                                        right: 0,
                                        maxHeight: "150px",
                                        overflowY: "auto",
                                        backgroundColor: "white",
                                        border: "1px solid #ddd",
                                        borderRadius: "4px",
                                        zIndex: 10,
                                        boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                                      }}
                                    >
                                      {suppliers
                                        .filter((supplier) => {
                                          if (!item.supplierSearch) return true; // Show all when no search text
                                          const supplierName =
                                            supplier.supplier_name ||
                                            supplier.name ||
                                            supplier.fullName ||
                                            "";
                                          return supplierName
                                            .toLowerCase()
                                            .includes(
                                              item.supplierSearch.toLowerCase()
                                            );
                                        })
                                        .map((supplier) => (
                                          <div
                                            key={supplier.id || supplier._id}
                                            onClick={() => {
                                              const supplierId =
                                                supplier.id || supplier._id;
                                              const supplierName =
                                                supplier.supplier_name ||
                                                supplier.name ||
                                                supplier.fullName ||
                                                supplierId;

                                              // Update both the supplier_id and the search text
                                              const updatedItem = {
                                                ...item,
                                                supplier_id: supplierId,
                                                supplierSearch: supplierName,
                                              };
                                              const newArray = [
                                                ...editedValues.ticketPay,
                                              ];
                                              newArray[index] = updatedItem;

                                              setEditedValues((prev) => ({
                                                ...prev,
                                                ticketPay: newArray,
                                                [`showSupplierDropdown_${index}`]: false,
                                              }));
                                            }}
                                            style={{
                                              padding: "8px 12px",
                                              cursor: "pointer",
                                              borderBottom: "1px solid #eee",
                                              backgroundColor:
                                                item.supplier_id ===
                                                (supplier.id || supplier._id)
                                                  ? "#f0f7ff"
                                                  : "white",
                                            }}
                                            onMouseOver={(e) =>
                                              (e.currentTarget.style.backgroundColor =
                                                "#f5f5f5")
                                            }
                                            onMouseOut={(e) =>
                                              (e.currentTarget.style.backgroundColor =
                                                item.supplier_id ===
                                                (supplier.id || supplier._id)
                                                  ? "#f0f7ff"
                                                  : "white")
                                            }
                                          >
                                            {supplier.supplier_name ||
                                              supplier.name ||
                                              supplier.fullName ||
                                              `${supplier.id || supplier._id}`}
                                          </div>
                                        ))}
                                      {suppliers.filter((supplier) => {
                                        if (!item.supplierSearch) return true;
                                        const supplierName =
                                          supplier.supplier_name ||
                                          supplier.name ||
                                          supplier.fullName ||
                                          "";
                                        return supplierName
                                          .toLowerCase()
                                          .includes(
                                            item.supplierSearch.toLowerCase()
                                          );
                                      }).length === 0 && (
                                        <div
                                          style={{
                                            padding: "8px 12px",
                                            color: "#999",
                                          }}
                                        >
                                          Không tìm thấy nhà cung cấp
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </td>
                            )}
                            <td>
                              <textarea
                                type="text"
                                value={item.description}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "buy",
                                    index,
                                    "description",
                                    e.target.value
                                  )
                                }
                                style={{
                                  minWidth: "100%",
                                  height: "60px",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={item.amount_bfr_tax}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "buy",
                                    index,
                                    "amount_bfr_tax",
                                    e.target.value
                                  )
                                }
                                style={{
                                  width: "100%",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={item.tax_rate}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "buy",
                                    index,
                                    "tax_rate",
                                    e.target.value
                                  )
                                }
                                style={{
                                  width: "100%",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>{formatCurrency(item.amount_aft_tax)}</td>
                            <td>
                              <button
                                onClick={() =>
                                  handleDeletePriceItem("buy", index)
                                }
                                style={{
                                  background: "#f44336",
                                  border: "none",
                                  borderRadius: "4px",
                                  padding: "5px 10px",
                                  cursor: "pointer",
                                  color: "white",
                                  fontSize: "12px",
                                }}
                              >
                                X
                              </button>
                            </td>
                          </tr>
                        ))}
                      <tr
                        style={{
                          fontWeight: "bold",
                          borderTop: "2px solid #eee",
                        }}
                      >
                        {/* Adjust total row based on user role */}
                        {userRole !== 'sale' ? (
                          <>
                            <td>Tổng cộng</td>
                            <td></td>
                            <td>
                              {formatCurrency(
                                editedValues.ticketPay
                                  ? editedValues.ticketPay.reduce(
                                      (sum, item) =>
                                        sum +
                                        (parseFloat(item.amount_bfr_tax) || 0),
                                      0
                                    )
                                  : 0
                              )}
                            </td>
                            <td></td>
                            <td>
                              {formatCurrency(
                                editedValues.ticketPay
                                  ? editedValues.ticketPay.reduce(
                                      (sum, item) =>
                                        sum +
                                        (parseFloat(item.amount_aft_tax) || 0),
                                      0
                                    )
                                  : 0
                              )}
                            </td>
                            <td></td>
                          </>
                        ) : (
                          <>
                            <td>Tổng cộng</td>
                            <td>
                              {formatCurrency(
                                editedValues.ticketPay
                                  ? editedValues.ticketPay.reduce(
                                      (sum, item) =>
                                        sum +
                                        (parseFloat(item.amount_bfr_tax) || 0),
                                      0
                                    )
                                  : 0
                              )}
                            </td>
                            <td></td>
                            <td>
                              {formatCurrency(
                                editedValues.ticketPay
                                  ? editedValues.ticketPay.reduce(
                                      (sum, item) =>
                                        sum +
                                        (parseFloat(item.amount_aft_tax) || 0),
                                      0
                                    )
                                  : 0
                              )}
                            </td>
                            <td></td>
                          </>
                        )}
                      </tr>
                    </tbody>
                  </table>
                  <button
                    onClick={() => handleAddPriceItem("buy")}
                    style={{
                      marginTop: "10px",
                      background: "#4a90e2",
                      border: "none",
                      borderRadius: "4px",
                      padding: "5px 10px",
                      cursor: "pointer",
                      color: "white",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                      width: "fit-content",
                    }}
                  >
                    + Thêm mục
                  </button>
                </div>
              ) : ticketPay && ticketPay.length > 0 ? (
                <table className="price-table">
                  <thead>
                    <tr>
                      {/* Hide supplier column for sale role */}
                      {userRole !== 'sale' && <th>Nhà cung cấp</th>}
                      <th>Mô tả</th>
                      <th>Giá trước thuế</th>
                      <th>Thuế (%)</th>
                      <th>Giá sau thuế</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ticketPay.map((item, index) => (
                      <tr key={index}>
                        {/* Hide supplier cell for sale role */}
                        {userRole !== 'sale' && (
                          <td>
                            {(() => {
                              const supplier = suppliers.find(
                                (s) => (s.id || s._id) === item.supplier_id
                              );
                              return supplier
                                ? supplier.supplier_name ||
                                    supplier.name ||
                                    supplier.fullName
                                : item.supplier_id || "Chưa chọn";
                            })()}
                          </td>
                        )}
                        <td>{item.description}</td>
                        <td>{formatCurrency(item.amount_bfr_tax)}</td>
                        <td>{item.tax_rate}%</td>
                        <td>{formatCurrency(item.amount_aft_tax)}</td>
                      </tr>
                    ))}
                    <tr
                      style={{
                        fontWeight: "bold",
                        borderTop: "2px solid #eee",
                      }}
                    >
                      {/* Adjust total row based on user role */}
                      {userRole !== 'sale' ? (
                        <>
                          <td>Tổng cộng</td>
                          <td></td>
                          <td>
                            {formatCurrency(
                              ticketPay.reduce(
                                (sum, item) => sum + item.amount_bfr_tax,
                                0
                              )
                            )}
                          </td>
                          <td></td>
                          <td>
                            {formatCurrency(
                              ticketPay.reduce(
                                (sum, item) => sum + item.amount_aft_tax,
                                0
                              )
                            )}
                          </td>
                        </>
                      ) : (
                        <>
                          <td>Tổng cộng</td>
                          <td>
                            {formatCurrency(
                              ticketPay.reduce(
                                (sum, item) => sum + item.amount_bfr_tax,
                                0
                              )
                            )}
                          </td>
                          <td></td>
                          <td>
                            {formatCurrency(
                              ticketPay.reduce(
                                (sum, item) => sum + item.amount_aft_tax,
                                0
                              )
                            )}
                          </td>
                        </>
                      )}
                    </tr>
                  </tbody>
                </table>
              ) : (
                <div className="detail-value">Chưa có thông tin giá mua</div>
              )}
            </div>
            )}

            {/* Sell Price - Only visible to sale and admin roles, not to moderator or purchaser */}
            {userRole !== 'moderator' && userRole !== 'purchaser' && (
            <div
              className="ticket-detail-section"
              style={{ position: "relative" }}
            >
              <h3>
                <FiDollarSign /> Thông tin giá bán
                {sectionSuccess["sell-price"] && (
                  <span
                    style={{
                      fontSize: "14px",
                      color: "#4caf50",
                      marginLeft: "10px",
                    }}
                  >
                    ✓ {sectionSuccess["sell-price"]}
                  </span>
                )}
                {sectionError["sell-price"] && (
                  <span
                    style={{
                      fontSize: "14px",
                      color: "#f44336",
                      marginLeft: "10px",
                    }}
                  >
                    ⚠ {sectionError["sell-price"]}
                  </span>
                )}
              </h3>
              {editingSections["sell-price"] ? (
                <div
                  style={{
                    position: "absolute",
                    top: "15px",
                    right: "15px",
                    display: "flex",
                    gap: "10px",
                  }}
                >
                  <button
                    onClick={() => handleConfirm("sell-price")}
                    disabled={sectionLoading["sell-price"]}
                    style={{
                      background: "#4caf50",
                      border: "none",
                      borderRadius: "4px",
                      padding: "5px 10px",
                      cursor: sectionLoading["sell-price"]
                        ? "not-allowed"
                        : "pointer",
                      color: "white",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                      opacity: sectionLoading["sell-price"] ? 0.7 : 1,
                    }}
                  >
                    {sectionLoading["sell-price"] ? (
                      <>
                        <span
                          className="loading-spinner"
                          style={{ marginRight: "5px" }}
                        ></span>{" "}
                        Đang xử lý...
                      </>
                    ) : (
                      <>
                        <FiCheckCircle style={{ marginRight: "5px" }} /> Xác
                        nhận
                      </>
                    )}
                  </button>
                  <button
                    onClick={() => handleCancel("sell-price")}
                    style={{
                      background: "#f44336",
                      border: "none",
                      borderRadius: "4px",
                      padding: "5px 10px",
                      cursor: "pointer",
                      color: "white",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                    }}
                  >
                    Huỷ bỏ
                  </button>
                </div>
              ) : (
                /* Only show edit button for sale and admin roles */
                (userRole === 'sale' || userRole === 'admin') && (
                  <button
                    onClick={() => handleEditSection("sell-price")}
                    style={{
                      position: "absolute",
                      top: "15px",
                      right: "15px",
                      background: "none",
                      border: "none",
                      cursor: "pointer",
                      color: "#4a90e2",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                    }}
                  >
                    <FiEdit style={{ marginRight: "5px" }} /> Chỉnh sửa
                  </button>
                )
              )}
              {editingSections["sell-price"] ? (
                <div>
                  <table className="price-table">
                    <thead>
                      <tr>
                        <th>Mô tả</th>
                        <th>Giá trước thuế</th>
                        <th>Thuế (%)</th>
                        <th>Giá sau thuế</th>
                      </tr>
                    </thead>
                    <tbody>
                      {editedValues.ticketReceive &&
                        editedValues.ticketReceive.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <textarea
                                type="text"
                                value={item.description}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "sell",
                                    index,
                                    "description",
                                    e.target.value
                                  )
                                }
                                style={{
                                  width: "100%",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={item.amount_bfr_tax}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "sell",
                                    index,
                                    "amount_bfr_tax",
                                    e.target.value
                                  )
                                }
                                style={{
                                  width: "100%",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={item.tax_rate}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "sell",
                                    index,
                                    "tax_rate",
                                    e.target.value
                                  )
                                }
                                style={{
                                  width: "100%",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>{formatCurrency(item.amount_aft_tax)}</td>
                            <td>
                              <button
                                onClick={() =>
                                  handleDeletePriceItem("sell", index)
                                }
                                style={{
                                  background: "#f44336",
                                  border: "none",
                                  borderRadius: "4px",
                                  padding: "5px 10px",
                                  cursor: "pointer",
                                  color: "white",
                                  fontSize: "12px",
                                }}
                              >
                                X
                              </button>
                            </td>
                          </tr>
                        ))}
                      <tr
                        style={{
                          fontWeight: "bold",
                          borderTop: "2px solid #eee",
                        }}
                      >
                        <td>Tổng cộng</td>
                        <td>
                          {formatCurrency(
                            editedValues.ticketReceive
                              ? editedValues.ticketReceive.reduce(
                                  (sum, item) =>
                                    sum +
                                    (parseFloat(item.amount_bfr_tax) || 0),
                                  0
                                )
                              : 0
                          )}
                        </td>
                        <td></td>
                        <td>
                          {formatCurrency(
                            editedValues.ticketReceive
                              ? editedValues.ticketReceive.reduce(
                                  (sum, item) =>
                                    sum +
                                    (parseFloat(item.amount_aft_tax) || 0),
                                  0
                                )
                              : 0
                          )}
                        </td>
                        <td></td>
                      </tr>
                    </tbody>
                  </table>
                  <button
                    onClick={() => handleAddPriceItem("sell")}
                    style={{
                      marginTop: "10px",
                      background: "#4a90e2",
                      border: "none",
                      borderRadius: "4px",
                      padding: "5px 10px",
                      cursor: "pointer",
                      color: "white",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                      width: "fit-content",
                    }}
                  >
                    + Thêm mục
                  </button>
                </div>
              ) : ticketReceive && ticketReceive.length > 0 ? (
                <table className="price-table">
                  <thead>
                    <tr>
                      <th>Mô tả</th>
                      <th>Giá trước thuế</th>
                      <th>Thuế (%)</th>
                      <th>Giá sau thuế</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ticketReceive.map((item, index) => (
                      <tr key={index}>
                        <td>{item.description}</td>
                        <td>{formatCurrency(item.amount_bfr_tax)}</td>
                        <td>{item.tax_rate}%</td>
                        <td>{formatCurrency(item.amount_aft_tax)}</td>
                      </tr>
                    ))}
                    <tr
                      style={{
                        fontWeight: "bold",
                        borderTop: "2px solid #eee",
                      }}
                    >
                      <td>Tổng cộng</td>
                      <td>
                        {formatCurrency(
                          ticketReceive.reduce(
                            (sum, item) => sum + item.amount_bfr_tax,
                            0
                          )
                        )}
                      </td>
                      <td></td>
                      <td>
                        {formatCurrency(
                          ticketReceive.reduce(
                            (sum, item) => sum + item.amount_aft_tax,
                            0
                          )
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              ) : (
                <div className="detail-value">Chưa có thông tin giá bán</div>
              )}
            </div>
            )}

            {/* Row 4: Notes */}
            <div className="ticket-detail-row">
              {/* Left column for Notes - takes up 2/3 of width */}
              <div style={{ flex: 2 }}>
                <div
                  className="ticket-detail-section"
                  style={{ position: "relative" }}
                >
                  <h3>
                    <FiFileText /> Ghi chú khác
                    {sectionSuccess["notes"] && (
                      <span
                        style={{
                          fontSize: "14px",
                          color: "#4caf50",
                          marginLeft: "10px",
                        }}
                      >
                        ✓ {sectionSuccess["notes"]}
                      </span>
                    )}
                    {sectionError["notes"] && (
                      <span
                        style={{
                          fontSize: "14px",
                          color: "#f44336",
                          marginLeft: "10px",
                        }}
                      >
                        ⚠ {sectionError["notes"]}
                      </span>
                    )}
                  </h3>
                  {editingSections["notes"] ? (
                    <div
                      style={{
                        position: "absolute",
                        top: "15px",
                        right: "15px",
                        display: "flex",
                        gap: "10px",
                      }}
                    >
                      <button
                        onClick={() => handleConfirm("notes")}
                        disabled={sectionLoading["notes"]}
                        style={{
                          background: "#4caf50",
                          border: "none",
                          borderRadius: "4px",
                          padding: "5px 10px",
                          cursor: sectionLoading["notes"]
                            ? "not-allowed"
                            : "pointer",
                          color: "white",
                          display: "flex",
                          alignItems: "center",
                          fontSize: "14px",
                          opacity: sectionLoading["notes"] ? 0.7 : 1,
                        }}
                      >
                        {sectionLoading["notes"] ? (
                          <>
                            <span
                              className="loading-spinner"
                              style={{ marginRight: "5px" }}
                            ></span>{" "}
                            Đang xử lý...
                          </>
                        ) : (
                          <>
                            <FiCheckCircle style={{ marginRight: "5px" }} /> Xác
                            nhận
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => handleCancel("notes")}
                        style={{
                          background: "#f44336",
                          border: "none",
                          borderRadius: "4px",
                          padding: "5px 10px",
                          cursor: "pointer",
                          color: "white",
                          display: "flex",
                          alignItems: "center",
                          fontSize: "14px",
                        }}
                      >
                        Huỷ bỏ
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => handleEditSection("notes")}
                      style={{
                        position: "absolute",
                        top: "15px",
                        right: "15px",
                        background: "none",
                        border: "none",
                        cursor: "pointer",
                        color: "#4a90e2",
                        display: "flex",
                        alignItems: "center",
                        fontSize: "14px",
                      }}
                    >
                      <FiEdit style={{ marginRight: "5px" }} /> Chỉnh sửa
                    </button>
                  )}
                  {editingSections["notes"] ? (
                    <textarea
                      value={editedValues.note || ""}
                      onChange={(e) =>
                        handleInputChange("note", e.target.value)
                      }
                      style={{
                        padding: "10px",
                        borderRadius: "4px",
                        border: "1px solid #ddd",
                        width: "100%",
                        minHeight: "100px",
                        resize: "vertical",
                      }}
                    />
                  ) : (
                    <div className="detail-value">
                      {ticket.note || "Không có ghi chú"}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right column - takes up 1/3 of width */}
          <div style={{ flex: 1 }}>
            {/* Empty space or can be used for future content */}
            <div className="ticket-detail-row">
              {/* Right column - takes up 1/3 of width - Recent Activity */}
              <div style={{ flex: 1 }}>
                <div
                  className="ticket-detail-section"
                  style={{ position: "relative" }}
                >
                  <h3>
                    <FiActivity /> Hoạt động gần đây
                  </h3>
                  {ticketLogs && ticketLogs.length > 0 ? (
                    <div className="activity-container">
                      {ticketLogs.map((log, index) => (
                        <div className="activity-item" key={index}>
                          <div className="activity-icon">
                            {log.log_type === "change_status" ? (
                              <FiActivity />
                            ) : log.log_type === "change_basic" ? (
                              <FiInfo />
                            ) : log.log_type === "change_vehicle" ? (
                              <FiTruck />
                            ) : log.log_type === "change_staff" ? (
                              <FiUsers />
                            ) : log.log_type === "change_receive" ? (
                              <FiDollarSign />
                            ) : log.log_type === "change_pay" ? (
                              <FiDollarSign />
                            ) : (
                              <FiEdit />
                            )}
                          </div>
                          <div className="activity-content">
                            <div className="activity-type">
                              {log.log_type === "change_status"
                                ? "Thay đổi trạng thái"
                                : log.log_type === "init_ticket"
                                ? "Khởi tạo Ticket"
                                : log.log_type === "change_basic"
                                ? "Cập nhật thông tin cơ bản"
                                : log.log_type === "change_vehicle"
                                ? "Cập nhật thông tin xe"
                                : log.log_type === "change_staff"
                                ? "Cập nhật người phụ trách"
                                : log.log_type === "change_receive"
                                ? "Cập nhật giá bán"
                                : log.log_type === "change_pay"
                                ? "Cập nhật giá mua"
                                : log.log_type === "field_change"
                                ? "Cập nhật thông tin"
                                : log.log_type}
                            </div>
                            {/* <div className="activity-value">{log.value}</div> */}
                            <div className="activity-meta">
                              <div className="activity-user">
                                bởi {getUserNameById(log.changed_by)}
                              </div>
                              <div className="activity-date">
                                {formatDateTime(log.created_at)}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="detail-value">
                      Không có hoạt động gần đây
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Row 5: Status Timeline */}
        <div className="ticket-detail-row">
          {/* Status Timeline */}
          <div
            className="ticket-detail-section"
            style={{ position: "relative" }}
          >
            <h3>
              <FiBarChart2 /> Timeline Trạng thái
            </h3>
            <div className="timeline-container">
              {statusTimeline.map((status, index) => (
                <div
                  className={`timeline-item ${
                    status.status === ticket.current_status ? "current" : ""
                  }`}
                  key={index}
                >
                  <div className="timeline-icon">
                    {status.date ? <FiCheckCircle /> : <FiClock />}
                  </div>
                  <div className="timeline-content">
                    <div className="timeline-status">{status.label}</div>
                    {status.date && (
                      <div className="timeline-meta">
                        <div className="timeline-user">
                          bởi {getUserNameById(status.changed_by)}
                        </div>
                        <div className="timeline-date">
                          {formatDateTime(status.date)}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Status Update Modal */}
      {showStatusModal && (
        <div className="status-modal-overlay">
          <div className="status-modal">
            <h3>Cập nhật trạng thái</h3>
            <div className="status-current">
              <span>Trạng thái hiện tại: </span>
              <span className={`ticket-status ${ticket.current_status}`}>
                {getStatusLabel(ticket.current_status)}
              </span>
            </div>

            <div className="status-options">
              <h4>Chọn trạng thái mới:</h4>
              {statusTimeline.map((status, index) => {
                // Only show statuses that come after the current one in the workflow
                const currentIndex = statusTimeline.findIndex(
                  (s) => s.status === ticket.current_status
                );
                if (index <= currentIndex) return null;

                // Only allow updating to the next status in the sequence
                const isNextStatus = index === currentIndex + 1;

                return (
                  <div
                    key={status.status}
                    className={`status-option ${
                      selectedStatus === status.status ? "selected" : ""
                    } ${!isNextStatus ? "disabled" : ""}`}
                    onClick={() =>
                      isNextStatus && handleStatusChange(status.status)
                    }
                  >
                    <div className="status-option-icon">
                      {isNextStatus ? <FiCheckCircle /> : <FiClock />}
                    </div>
                    <div className="status-option-label">{status.label}</div>
                    {!isNextStatus && (
                      <div className="status-option-message">
                        Cần hoàn thành các trạng thái trước
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {statusUpdateError && (
              <div
                style={{
                  color: "#f44336",
                  marginBottom: "15px",
                  fontSize: "14px",
                }}
              >
                <span style={{ marginRight: "5px" }}>⚠</span>
                {statusUpdateError}
              </div>
            )}

            <div className="status-modal-actions">
              <button
                className="btn-cancel"
                onClick={() => setShowStatusModal(false)}
                disabled={statusUpdateLoading}
              >
                <FiX /> Huỷ bỏ
              </button>
              <button
                className="btn-confirm"
                onClick={handleConfirmStatusChange}
                disabled={
                  statusUpdateLoading ||
                  !selectedStatus ||
                  selectedStatus === ticket.current_status
                }
              >
                {statusUpdateLoading ? (
                  <>
                    <span
                      className="loading-spinner"
                      style={{ marginRight: "5px" }}
                    ></span>{" "}
                    Đang xử lý...
                  </>
                ) : (
                  <>
                    <FiCheckCircle style={{ marginRight: "5px" }} /> Xác nhận
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Print Modal */}
      {showPrintModal && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              backgroundColor: "#f0f0f0",
              borderRadius: "8px",
              boxShadow: "0 4px 20px rgba(0, 0, 0, 0.2)",
              width: "90%",
              height: "90%",
              display: "flex",
              flexDirection: "column",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                padding: "15px",
                borderBottom: "1px solid #ddd",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <h3 style={{ margin: 0 }}>Xem trước bản in</h3>
              <div>
                <button
                  onClick={() => {
                    if (printFrameRef.current) {
                      const iframe = printFrameRef.current;
                      const iframeWindow = iframe.contentWindow;
                      iframeWindow.focus();
                      iframeWindow.print();
                    }
                  }}
                  style={{
                    marginRight: "10px",
                    padding: "8px 16px",
                    backgroundColor: "#4CAF50",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer",
                    display: "inline-flex",
                    alignItems: "center",
                  }}
                >
                  <FiPrinter style={{ marginRight: "5px" }} /> In
                </button>
                <button
                  onClick={() => setShowPrintModal(false)}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: "#f44336",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer",
                    display: "inline-flex",
                    alignItems: "center",
                  }}
                >
                  <FiX style={{ marginRight: "5px" }} /> Đóng
                </button>
              </div>
            </div>
            <div
              style={{
                flex: 1,
                overflow: "auto",
                padding: "15px",
                display: "flex",
                justifyContent: "center",
                alignItems: "flex-start",
              }}
            >
              <iframe
                ref={printFrameRef}
                style={{
                  width: "100%",
                  height: "100%",
                  border: "1px solid #ddd",
                  display: "block",
                  backgroundColor: "white",
                  maxWidth: "900px", /* Slightly wider than A4 to show border */
                }}
                srcDoc={getPrintContent()}
                title="Print Preview"
                onLoad={() => {
                  // Ensure iframe content is fully loaded and visible
                  if (printFrameRef.current) {
                    try {
                      const iframe = printFrameRef.current;
                      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                      // Add custom styles to ensure content is visible
                      const style = iframeDoc.createElement('style');
                      style.textContent = `
                        html, body {
                          height: auto !important;
                          overflow: visible !important;
                          min-height: 297mm !important;
                        }
                        h2, h3, p {
                          display: block !important;
                          visibility: visible !important;
                        }
                      `;
                      iframeDoc.head.appendChild(style);

                      // Log for debugging
                      console.log("Iframe loaded, checking content...");
                      console.log("Title elements:", iframeDoc.querySelectorAll('h2, h3').length);

                      // Force layout recalculation
                      setTimeout(() => {
                        iframe.style.height = (iframeDoc.body.scrollHeight + 50) + 'px';
                      }, 100);
                    } catch (e) {
                      console.error("Error accessing iframe content:", e);
                    }
                  }
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TicketChiTiet;
