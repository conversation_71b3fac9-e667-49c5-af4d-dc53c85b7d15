import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  FiArrowLeft,
  FiCalendar,
  FiUser,
  FiPhone,
  FiMapPin,
  FiTruck,
  FiFileText,
  FiDollarSign,
  FiClock,
  FiActivity,
  FiCheckCircle,
  FiEdit,
  FiInfo,
  FiUsers,
  FiBarChart2,
  FiX,
  FiAlertCircle,
} from "react-icons/fi";
import apiService from "../../services/api.service";
import { ENDPOINTS } from "../../config/api.config";
import "../../styles/RequestDetail.css";

const TicketChiTiet = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [ticket, setTicket] = useState({});
  const [ticketLogs, setTicketLogs] = useState([]);
  const [ticketPay, setTicketPay] = useState([]);
  const [ticketReceive, setTicketReceive] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [customerLoading, setCustomerLoading] = useState(true);
  const [suppliers, setSuppliers] = useState([]);
  const [supplierLoading, setSupplierLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(true);

  // All sections are always in edit mode
  const [editingSections, setEditingSections] = useState({
    "basic-info": true,
    "vehicle-info": true,
    "staff-info": true,
    "buy-price": true,
    "sell-price": true,
    notes: true,
  });
  const [editedValues, setEditedValues] = useState({});
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("");
  const [validationErrors, setValidationErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const customerSearchRef = useRef(null);
  const supplierSearchRef = useRef(null);
  const pricingStaffSearchRef = useRef(null);
  const dispatchStaffSearchRef = useRef(null);
  const saleStaffSearchRef = useRef(null);

  // Handle clicks outside of search dropdowns
  useEffect(() => {
    function handleClickOutside(event) {
      // Handle customer dropdown
      if (
        customerSearchRef.current &&
        !customerSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showCustomerDropdown: false,
        }));
      }

      // Handle supplier dropdown
      if (
        supplierSearchRef.current &&
        !supplierSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showSupplierDropdown: false,
        }));
      }

      // Handle pricing staff dropdown
      if (
        pricingStaffSearchRef.current &&
        !pricingStaffSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showPricingStaffDropdown: false,
        }));
      }

      // Handle dispatch staff dropdown
      if (
        dispatchStaffSearchRef.current &&
        !dispatchStaffSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showDispatchStaffDropdown: false,
        }));
      }

      // Handle sale staff dropdown
      if (
        saleStaffSearchRef.current &&
        !saleStaffSearchRef.current.contains(event.target)
      ) {
        setEditedValues((prev) => ({
          ...prev,
          showSaleStaffDropdown: false,
        }));
      }

      // Handle supplier dropdowns in price table rows
      // Check if the click was outside any supplier dropdown in the price table
      const isClickOutsideSupplierDropdowns =
        !event.target.closest('[id^="supplier-dropdown-"]') &&
        !event.target.closest('[id^="supplier-input-"]');

      if (isClickOutsideSupplierDropdowns) {
        // Close all supplier dropdowns in price table rows
        setEditedValues((prev) => {
          const newValues = { ...prev };

          // Find all keys that start with "showSupplierDropdown_" and set them to false
          Object.keys(newValues).forEach((key) => {
            if (key.startsWith("showSupplierDropdown_")) {
              newValues[key] = false;
            }
          });

          return newValues;
        });
      }
    }

    // Add event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // Remove event listener on cleanup
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch customer data
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setCustomerLoading(true);
        const response = await apiService.get(ENDPOINTS.CUSTOMERS.GET_BASIC);
        // console.log("Customer API response:", response); // Log the response to see its structure

        // Check different possible response structures
        if (response && response.customers) {
          setCustomers(response.customers);
        } else if (response && Array.isArray(response)) {
          // If the response itself is an array
          setCustomers(response);
        } else if (response && response.data) {
          // If the data is in a 'data' property
          setCustomers(response.data);
        } else {
          console.error("Unexpected response structure:", response);
        }
      } catch (err) {
        console.error("Error fetching customers:", err);
      } finally {
        setCustomerLoading(false);
      }
    };

    fetchCustomers();
  }, []);

  // Fetch supplier data
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        setSupplierLoading(true);
        const response = await apiService.get(ENDPOINTS.SUPPLIERS.GET_BASIC);
        // console.log("Supplier API response:", response); // Log the response to see its structure

        // Check different possible response structures
        if (response && response.suppliers) {
          setSuppliers(response.suppliers);
        } else if (response && Array.isArray(response)) {
          // If the response itself is an array
          setSuppliers(response);
        } else if (response && response.data) {
          // If the data is in a 'data' property
          setSuppliers(response.data);
        } else {
          console.error("Unexpected response structure:", response);
        }
      } catch (err) {
        console.error("Error fetching suppliers:", err);
      } finally {
        setSupplierLoading(false);
      }
    };

    fetchSuppliers();
  }, []);

  // Fetch users data
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setUsersLoading(true);
        const response = await apiService.get(ENDPOINTS.USERS.GET_BASIC);
        // console.log("Users API response:", response); // Log the response to see its structure

        // Check different possible response structures
        if (response && response.users) {
          setUsers(response.users);
        } else if (response && Array.isArray(response)) {
          // If the response itself is an array
          setUsers(response);
        } else if (response && response.data) {
          // If the data is in a 'data' property
          setUsers(response.data);
        } else {
          console.error("Unexpected response structure:", response);
        }
      } catch (err) {
        console.error("Error fetching users:", err);
      } finally {
        setUsersLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Fetch ticket details
  useEffect(() => {
    // Initialize with empty values
    setTicket({
      current_status: "initial", // Set default status to initial
    });
    setTicketLogs([]);

    // Initialize ticketPay with empty array (no rows)
    setTicketPay([]);

    // Initialize ticketReceive with empty array (no rows)
    setTicketReceive([]);

    setLoading(false);

    // Initialize editedValues for basic-info section
    setEditedValues({
      request_date: new Date(),
      report_closing_date: "",
      customer_id: "",
      customerSearch: "",
      showCustomerDropdown: false,
      pickup_address: "",
      delivery_address: "",
      ticketPay: [],
      ticketReceive: [],
    });
  }, [location]);

  const handleBack = () => {
    navigate("/tickets");
  };

  // const handleEditSection = (sectionId) => {
  //   // Initialize the edited values for this section
  //   // Note: All sections are always in edit mode now, but we keep this function for future use
  //     if (sectionId === "basic-info") {
  //       // Find customer name for the current customer_id
  //       const currentCustomer = customers.find(
  //         (c) => (c.id || c._id) === ticket.customer_id
  //       );
  //       const customerName = currentCustomer
  //         ? currentCustomer.customer_name ||
  //           currentCustomer.name ||
  //           currentCustomer.fullName
  //         : "";

  //       // Find supplier name for the current supplier_id
  //       const currentSupplier = suppliers.find(
  //         (s) => (s.id || s._id) === ticket.supplier_id
  //       );
  //       const supplierName = currentSupplier
  //         ? currentSupplier.supplier_name ||
  //           currentSupplier.name ||
  //           currentSupplier.fullName
  //         : "";

  //       setEditedValues((prev) => ({
  //         ...prev,
  //         request_date: ticket.request_date,
  //         customer_id: ticket.customer_id,
  //         customerSearch: customerName, // Initialize search field with current customer name
  //         showCustomerDropdown: false, // Initially hide the dropdown
  //         supplier_id: ticket.supplier_id,
  //         supplierSearch: supplierName, // Initialize search field with current supplier name
  //         showSupplierDropdown: false, // Initially hide the dropdown
  //         phone: ticket.phone || "",
  //         pickup_address: ticket.pickup_address,
  //         delivery_address: ticket.delivery_address,
  //       }));

  //       // If customers haven't been loaded yet, fetch them
  //       if (customers.length === 0 && !customerLoading) {
  //         const fetchCustomers = async () => {
  //           try {
  //             setCustomerLoading(true);
  //             const response = await apiService.get(
  //               ENDPOINTS.CUSTOMERS.GET_BASIC
  //             );
  //             console.log("Customer API response (edit):", response);

  //             if (response && response.customers) {
  //               setCustomers(response.customers);
  //             } else if (response && Array.isArray(response)) {
  //               setCustomers(response);
  //             } else if (response && response.data) {
  //               setCustomers(response.data);
  //             }
  //           } catch (err) {
  //             console.error("Error fetching customers:", err);
  //           } finally {
  //             setCustomerLoading(false);
  //           }
  //         };

  //         fetchCustomers();
  //       }

  //       // If suppliers haven't been loaded yet, fetch them
  //       if (suppliers.length === 0 && !supplierLoading) {
  //         const fetchSuppliers = async () => {
  //           try {
  //             setSupplierLoading(true);
  //             const response = await apiService.get(
  //               ENDPOINTS.SUPPLIERS.GET_BASIC
  //             );
  //             console.log("Supplier API response (edit):", response);

  //             if (response && response.suppliers) {
  //               setSuppliers(response.suppliers);
  //             } else if (response && Array.isArray(response)) {
  //               setSuppliers(response);
  //             } else if (response && response.data) {
  //               setSuppliers(response.data);
  //             }
  //           } catch (err) {
  //             console.error("Error fetching suppliers:", err);
  //           } finally {
  //             setSupplierLoading(false);
  //           }
  //         };

  //         fetchSuppliers();
  //       }
  //     } else if (sectionId === "vehicle-info") {
  //       setEditedValues((prev) => ({
  //         ...prev,
  //         vehicle_info: ticket.vehicle_info,
  //         vehicle_quantity: ticket.vehicle_quantity,
  //         note: ticket.note || "",
  //       }));
  //     } else if (sectionId === "staff-info") {
  //       // Find staff names for the current staff IDs
  //       const pricingStaff = users.find(
  //         (u) => (u.id || u._id) === ticket.pricing_staff_id
  //       );
  //       const dispatchStaff = users.find(
  //         (u) => (u.id || u._id) === ticket.dispatch_staff_id
  //       );
  //       const saleStaff = users.find(
  //         (u) => (u.id || u._id) === ticket.sale_staff_id
  //       );

  //       const pricingStaffName = pricingStaff
  //         ? pricingStaff.display_name ||
  //           pricingStaff.name ||
  //           pricingStaff.fullName
  //         : "";
  //       const dispatchStaffName = dispatchStaff
  //         ? dispatchStaff.display_name ||
  //           dispatchStaff.name ||
  //           dispatchStaff.fullName
  //         : "";
  //       const saleStaffName = saleStaff
  //         ? saleStaff.display_name || saleStaff.name || saleStaff.fullName
  //         : "";

  //       setEditedValues((prev) => ({
  //         ...prev,
  //         pricing_staff_id: ticket.pricing_staff_id || "",
  //         dispatch_staff_id: ticket.dispatch_staff_id || "",
  //         sale_staff_id: ticket.sale_staff_id || "",
  //         pricingStaffSearch: pricingStaffName,
  //         dispatchStaffSearch: dispatchStaffName,
  //         saleStaffSearch: saleStaffName,
  //         showPricingStaffDropdown: false,
  //         showDispatchStaffDropdown: false,
  //         showSaleStaffDropdown: false,
  //       }));

  //       // If users haven't been loaded yet, fetch them
  //       if (users.length === 0 && !usersLoading) {
  //         const fetchUsers = async () => {
  //           try {
  //             setUsersLoading(true);
  //             const response = await apiService.get(ENDPOINTS.USERS.GET_BASIC);
  //             console.log("Users API response (edit):", response);

  //             if (response && response.users) {
  //               setUsers(response.users);
  //             } else if (response && Array.isArray(response)) {
  //               setUsers(response);
  //             } else if (response && response.data) {
  //               setUsers(response.data);
  //             }
  //           } catch (err) {
  //             console.error("Error fetching users:", err);
  //           } finally {
  //             setUsersLoading(false);
  //           }
  //         };

  //         fetchUsers();
  //       }
  //     } else if (sectionId === "notes") {
  //       setEditedValues((prev) => ({
  //         ...prev,
  //         note: ticket.note || "",
  //       }));
  //     } else if (sectionId === "buy-price") {
  //       // Create a deep copy of the ticketPay array
  //       setEditedValues((prev) => ({
  //         ...prev,
  //         ticketPay: ticketPay.map((item) => ({ ...item })),
  //       }));
  //     } else if (sectionId === "sell-price") {
  //       // Create a deep copy of the ticketReceive array
  //       setEditedValues((prev) => ({
  //         ...prev,
  //         ticketReceive: ticketReceive.map((item) => ({ ...item })),
  //       }));
  //     }
  //   // No need to toggle edit mode as all sections are always editable
  // };

  const handleInputChange = (field, value) => {
    setEditedValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // These functions are no longer needed as we don't have confirm/cancel buttons
  // but we keep them for future reference
  /*
  const handleConfirm = (sectionId) => {
    // Update the ticket with edited values
    if (sectionId === "buy-price") {
      setTicketPay(editedValues.ticketPay);
    } else if (sectionId === "sell-price") {
      setTicketReceive(editedValues.ticketReceive);
    } else {
      setTicket((prev) => ({
        ...prev,
        ...editedValues,
      }));
    }

    // Clear any validation errors related to this section
    const newValidationErrors = { ...validationErrors };
    if (sectionId === "basic-info") {
      delete newValidationErrors.request_date;
      delete newValidationErrors.customer_id;
      delete newValidationErrors.pickup_address;
      delete newValidationErrors.delivery_address;
      delete newValidationErrors.supplier_id;
      delete newValidationErrors.company_id;
    } else if (sectionId === "vehicle-info") {
      delete newValidationErrors.vehicle_info;
      delete newValidationErrors.vehicle_quantity;
    } else if (sectionId === "staff-info") {
      delete newValidationErrors.pricing_staff_id;
      delete newValidationErrors.dispatch_staff_id;
      delete newValidationErrors.sale_staff_id;
    } else if (sectionId === "buy-price") {
      delete newValidationErrors.ticketPay;
    } else if (sectionId === "sell-price") {
      delete newValidationErrors.ticketReceive;
    }
    setValidationErrors(newValidationErrors);
  };

  const handleCancel = (sectionId) => {
    // Discard changes and exit edit mode
    setEditingSections((prev) => ({
      ...prev,
      [sectionId]: false,
    }));
  };
  */

  const handleUpdateStatus = () => {
    setSelectedStatus(ticket.current_status);
    setShowStatusModal(true);
  };

  const handleStatusChange = (status) => {
    setSelectedStatus(status);
  };

  const handleConfirmStatusChange = () => {
    // Create a new log entry
    const newLog = {
      log_type: "status_change",
      value: selectedStatus,
      changed_by: "Người dùng hiện tại", // Replace with actual user name when available
      created_at: new Date(),
    };

    // Update ticket and logs
    setTicket((prev) => ({
      ...prev,
      current_status: selectedStatus,
    }));

    setTicketLogs((prev) => [newLog, ...prev]);

    // Close the modal
    setShowStatusModal(false);
  };

  // Validate function removed as it's now inline in handleSubmitTicket

  // Get border style based on validation error
  const getInputStyle = (fieldName) => {
    const baseStyle = {
      padding: "5px",
      borderRadius: "4px",
      border: "1px solid #ddd",
      width: "100%",
    };

    if (validationErrors[fieldName]) {
      return {
        ...baseStyle,
        border: "1px solid #f44336",
        backgroundColor: "#fff8f8",
        boxShadow: "0 0 0 2px rgba(244, 67, 54, 0.2)",
      };
    }

    return baseStyle;
  };

  // Handle submit ticket
  const handleSubmitTicket = async () => {
    // First, update the ticket object with all edited values
    const updatedTicket = {
      ...ticket,
      request_date: editedValues.request_date,
      report_closing_date: editedValues.report_closing_date,
      customer_id: editedValues.customer_id,
      pickup_address: editedValues.pickup_address,
      delivery_address: editedValues.delivery_address,
      vehicle_info: editedValues.vehicle_info,
      vehicle_quantity: editedValues.vehicle_quantity,
      vehicle_plate: editedValues.vehicle_plate,
      pricing_staff_id: editedValues.pricing_staff_id,
      dispatch_staff_id: editedValues.dispatch_staff_id,
      sale_staff_id: editedValues.sale_staff_id,
      note: editedValues.note, // This is the ticket note
      company_id: JSON.parse(localStorage.getItem("user")).company_id,
    };
    // console.log("Updated ticket:", updatedTicket);

    // Update the ticket state
    setTicket(updatedTicket);

    // Update price tables
    if (editedValues.ticketPay) {
      setTicketPay(editedValues.ticketPay);
    }

    if (editedValues.ticketReceive) {
      setTicketReceive(editedValues.ticketReceive);
    }

    // Validate with the updated values
    const errors = {};

    // Basic info validation
    if (!updatedTicket.request_date)
      errors.request_date = "Ngày yêu cầu là bắt buộc";
    // if (!updatedTicket.report_closing_date)
    //   errors.report_closing_date = "Ngày yêu cầu là bắt buộc";
    if (!updatedTicket.customer_id)
      errors.customer_id = "Khách hàng là bắt buộc";
    if (!updatedTicket.pickup_address)
      errors.pickup_address = "Địa chỉ lấy hàng là bắt buộc";
    if (!updatedTicket.delivery_address)
      errors.delivery_address = "Địa chỉ giao hàng là bắt buộc";
    if (!updatedTicket.company_id) errors.company_id = "Công ty là bắt buộc";

    // Vehicle info validation
    if (!updatedTicket.vehicle_info)
      errors.vehicle_info = "Thông tin xe là bắt buộc";
    if (!updatedTicket.vehicle_quantity)
      errors.vehicle_quantity = "Số lượng xe là bắt buộc";

    // Staff info validation
    if (!updatedTicket.pricing_staff_id)
      errors.pricing_staff_id = "Nhân viên định giá là bắt buộc";
    if (!updatedTicket.dispatch_staff_id)
      errors.dispatch_staff_id = "Nhân viên điều phối là bắt buộc";
    if (!updatedTicket.sale_staff_id)
      errors.sale_staff_id = "Nhân viên kinh doanh là bắt buộc";

    // Price validation
    const currentTicketPay = editedValues.ticketPay || ticketPay;
    const currentTicketReceive = editedValues.ticketReceive || ticketReceive;

    // if (
    //   !currentTicketPay ||
    //   currentTicketPay.length === 0 ||
    //   (currentTicketPay.length === 1 &&
    //     !currentTicketPay[0].description &&
    //     (currentTicketPay[0].amount_bfr_tax === 0 ||
    //       currentTicketPay[0].amount_bfr_tax === ""))
    // ) {
    //   errors.ticketPay = "Cần có ít nhất một mục giá mua";
    // }

    // if (
    //   !currentTicketReceive ||
    //   currentTicketReceive.length === 0 ||
    //   (currentTicketReceive.length === 1 &&
    //     !currentTicketReceive[0].description &&
    //     (currentTicketReceive[0].amount_bfr_tax === 0 ||
    //       currentTicketReceive[0].amount_bfr_tax === ""))
    // ) {
    //   errors.ticketReceive = "Cần có ít nhất một mục giá bán";
    // }
    // console.log("Validation errors:", errors);

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      // No need to scroll to top as we're highlighting individual fields
      return;
    }

    setIsSubmitting(true);

    try {
      // Format request date to YYYY-MM-DD
      const formatDateToString = (date) => {
        if (!date) return null;
        const d = new Date(date);
        return d.toISOString().split("T")[0];
      };

      // Prepare body_ticket object
      const body_ticket = {
        request_date: formatDateToString(updatedTicket.request_date),
        report_closing_date: formatDateToString(
          updatedTicket.report_closing_date
        ),
        customer_id: updatedTicket.customer_id,
        company_id:
          updatedTicket.company_id ||
          (localStorage.getItem("user")
            ? JSON.parse(localStorage.getItem("user")).company_id
            : null), // Use company from user data if not set
        pickup_address: updatedTicket.pickup_address,
        delivery_address: updatedTicket.delivery_address,
        vehicle_info: updatedTicket.vehicle_info,
        vehicle_quantity: updatedTicket.vehicle_quantity,
        pricing_staff_id: updatedTicket.pricing_staff_id,
        dispatch_staff_id: updatedTicket.dispatch_staff_id,
        sale_staff_id: updatedTicket.sale_staff_id,
      };

      // Add optional fields only if they have values
      if (updatedTicket.note) body_ticket.note = updatedTicket.note; // This is the ticket note

      // Prepare body_receive object
      const body_receive = {
        items: currentTicketReceive.map((item) => ({
          description: item.description,
          amount_bfr_tax: item.amount_bfr_tax === "" ? 0 : item.amount_bfr_tax,
          tax_rate: item.tax_rate,
          amount_aft_tax: item.amount_aft_tax,
        })),
      };

      // Prepare body_pay object
      const body_pay = {
        items: currentTicketPay.map((item) => ({
          description: item.description,
          amount_bfr_tax: item.amount_bfr_tax === "" ? 0 : item.amount_bfr_tax,
          tax_rate: item.tax_rate,
          amount_aft_tax: item.amount_aft_tax,
        })),
      };

      // Combine all into the final request body
      const requestData = {
        body_ticket,
        body_receive,
        body_pay,
      };

      console.log("Request data:", requestData);

      // Call API to create ticket
      const response = await apiService.post(
        ENDPOINTS.TICKET.CREATE,
        requestData
      );
      console.log("Ticket created:", response);

      // Show success notification before redirecting
      if (response && response.id) {
        // The response contains the ID of the newly created ticket
        setSuccessMessage(`Đã tạo thành công ticket #${response.id}`);

        // Wait a moment to show the success message before redirecting
        setTimeout(() => {
          // Navigate to ticket detail page after showing the notification
          navigate(`/ticket/detail?id=${response.id}`);
        }, 2000); // 2 seconds delay to show the success message
      } else {
        // Fallback to tickets list if no ID is returned
        console.warn("No ticket ID returned from API");
        navigate("/tickets");
      }
    } catch (error) {
      console.error("Error creating ticket:", error);
      alert("Có lỗi xảy ra khi tạo ticket. Vui lòng thử lại.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // This function is used in the UI to determine if a status can be selected
  // It's kept here for future use in validation logic

  // Functions for price table editing
  const handlePriceItemChange = (type, index, field, value) => {
    const arrayName = type === "buy" ? "ticketPay" : "ticketReceive";

    setEditedValues((prev) => {
      const newArray = [...prev[arrayName]];

      // If it's a numeric field, convert to number
      if (field === "amount_bfr_tax" || field === "tax_rate") {
        // Cho phép giá trị trống cho amount_bfr_tax
        if (field === "amount_bfr_tax" && value === "") {
          newArray[index] = { ...newArray[index], [field]: "" };
          // Nếu giá trước thuế trống, giá sau thuế cũng trống
          newArray[index].amount_aft_tax = 0;
        } else {
          // Đảm bảo giá trị không âm
          const numValue = Math.max(0, parseFloat(value) || 0);
          newArray[index] = { ...newArray[index], [field]: numValue };

          // Recalculate amount_aft_tax
          const amount =
            field === "amount_bfr_tax"
              ? numValue
              : newArray[index].amount_bfr_tax;
          const taxRate =
            field === "tax_rate" ? numValue : newArray[index].tax_rate;

          // Nếu amount_bfr_tax là chuỗi rỗng, amount_aft_tax = 0
          if (amount === "") {
            newArray[index].amount_aft_tax = 0;
          } else {
            newArray[index].amount_aft_tax = amount + (amount * taxRate) / 100;
          }
        }
      } else {
        newArray[index] = { ...newArray[index], [field]: value };
      }

      return { ...prev, [arrayName]: newArray };
    });
  };

  const handleAddPriceItem = (type) => {
    const arrayName = type === "buy" ? "ticketPay" : "ticketReceive";

    setEditedValues((prev) => {
      const newItem = {
        description: "",
        amount_bfr_tax: "",
        tax_rate: 10,
        amount_aft_tax: 0,
      };

      return {
        ...prev,
        [arrayName]: [...prev[arrayName], newItem],
      };
    });
  };

  const handleDeletePriceItem = (type, index) => {
    const arrayName = type === "buy" ? "ticketPay" : "ticketReceive";

    setEditedValues((prev) => {
      const newArray = [...prev[arrayName]];
      newArray.splice(index, 1);

      return { ...prev, [arrayName]: newArray };
    });
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    })
      .format(amount)
      .replace("₫", "VNĐ");
  };

  const getStatusLabel = (status) => {
    const labels = {
      initial: "Khởi tạo",
      approved: "Đã duyệt",
      delivered: "Đã giao hàng",
      customerpaid: "Đã thu tiền khách hàng",
      supplierpaid: "Đã trả tiền nhà cung cấp",
      completed: "Hoàn thành",
    };
    return labels[status] || status;
  };

  if (loading) {
    return (
      <div className="loading-container">
        <p>Đang tải thông tin ticket...</p>
      </div>
    );
  }

  // Sort timeline statuses in chronological order
  const statusTimeline = [
    { status: "initial", label: "Khởi tạo", date: null, changed_by: null },
    { status: "approved", label: "Đã duyệt", date: null, changed_by: null },
    {
      status: "delivered",
      label: "Đã giao hàng",
      date: null,
      changed_by: null,
    },
    {
      status: "customerpaid",
      label: "Đã thu tiền khách hàng",
      date: null,
      changed_by: null,
    },
    {
      status: "supplierpaid",
      label: "Đã trả tiền nhà cung cấp",
      date: null,
      changed_by: null,
    },
    { status: "completed", label: "Hoàn thành", date: null, changed_by: null },
  ];

  // Fill in the timeline with actual status changes from logs
  if (ticketLogs && ticketLogs.length > 0) {
    ticketLogs.forEach((log) => {
      if (log.log_type === "status_change") {
        const statusItem = statusTimeline.find(
          (item) => item.status === log.value
        );
        if (statusItem) {
          statusItem.date = log.created_at;
          statusItem.changed_by = log.changed_by;
        }
      }
    });
  }

  return (
    <div className="ticket-detail-container">
      <div className="ticket-detail-header">
        <button className="back-button" onClick={handleBack}>
          <FiArrowLeft /> Quay lại
        </button>
        <div className="ticket-detail-title">
          <h2>{ticket.company_name || "Công ty"}</h2>
          <div className="ticket-code">Mã ticket: #{ticket.id}</div>
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <span className={`ticket-status ${ticket.current_status}`}>
            {getStatusLabel(ticket.current_status)}
          </span>
          <button
            onClick={handleSubmitTicket}
            style={{
              background: "#4caf50",
              border: "none",
              borderRadius: "4px",
              padding: "8px 16px",
              cursor: isSubmitting ? "not-allowed" : "pointer",
              color: "white",
              fontSize: "14px",
              fontWeight: "bold",
              display: "flex",
              alignItems: "center",
              opacity: isSubmitting ? 0.7 : 1,
            }}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span
                  className="loading-spinner"
                  style={{ marginRight: "8px" }}
                ></span>{" "}
                Đang xử lý...
              </>
            ) : (
              <>
                <FiCheckCircle style={{ marginRight: "8px" }} /> Hoàn thành
              </>
            )}
          </button>
        </div>
      </div>

      {successMessage && (
        <div className="success-message" style={{ margin: "10px 0" }}>
          <FiCheckCircle />
          <span>{successMessage}</span>
        </div>
      )}

      <div className="ticket-detail-content">
        {/* Row 1: Basic Info & Vehicle Info */}
        <div className="ticket-detail-row">
          {/* Basic Info */}
          <div
            className="ticket-detail-section"
            style={{ position: "relative" }}
          >
            <h3>
              <FiInfo /> Thông tin cơ bản
            </h3>
            {/* No edit/confirm/cancel buttons as all sections are always editable */}
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiCalendar /> Ngày yêu cầu
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="date"
                  value={
                    editedValues.request_date
                      ? new Date(editedValues.request_date)
                          .toISOString()
                          .split("T")[0]
                      : ""
                  }
                  onChange={(e) =>
                    handleInputChange("request_date", new Date(e.target.value))
                  }
                  style={getInputStyle("request_date")}
                />
              ) : (
                <div className="detail-value">
                  {formatDate(ticket.request_date)}
                </div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiCalendar /> Ngày chốt báo cáo
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="date"
                  value={
                    editedValues.report_closing_date
                      ? new Date(editedValues.report_closing_date)
                          .toISOString()
                          .split("T")[0]
                      : ""
                  }
                  onChange={(e) =>
                    handleInputChange(
                      "report_closing_date",
                      new Date(e.target.value)
                    )
                  }
                  style={getInputStyle("report_closing_date")}
                />
              ) : (
                <div className="detail-value">
                  {formatDate(ticket.report_closing_date)}
                </div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiUser /> Khách hàng
              </div>
              {editingSections["basic-info"] ? (
                <div
                  style={{ position: "relative", flex: 1 }}
                  ref={customerSearchRef}
                >
                  <input
                    type="text"
                    value={editedValues.customerSearch || ""}
                    onChange={(e) => {
                      const searchValue = e.target.value;
                      setEditedValues((prev) => ({
                        ...prev,
                        customerSearch: searchValue,
                        showCustomerDropdown: true, // Show dropdown when typing
                      }));
                    }}
                    onFocus={() => {
                      setEditedValues((prev) => ({
                        ...prev,
                        showCustomerDropdown: true, // Show dropdown on focus
                      }));
                    }}
                    placeholder="Gõ để tìm khách hàng..."
                    style={getInputStyle("customer_id")}
                  />
                  {editedValues.showCustomerDropdown && (
                    <div
                      style={{
                        position: "absolute",
                        top: "100%",
                        left: 0,
                        right: 0,
                        maxHeight: "200px",
                        overflowY: "auto",
                        backgroundColor: "white",
                        border: "1px solid #ddd",
                        borderRadius: "4px",
                        zIndex: 10,
                        boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                      }}
                    >
                      {customers
                        .filter((customer) => {
                          if (!editedValues.customerSearch) return true; // Show all when no search text
                          const customerName =
                            customer.customer_name ||
                            customer.name ||
                            customer.fullName ||
                            "";
                          return customerName
                            .toLowerCase()
                            .includes(
                              editedValues.customerSearch.toLowerCase()
                            );
                        })
                        .map((customer) => (
                          <div
                            key={customer.id || customer._id}
                            onClick={() => {
                              const customerId = customer.id || customer._id;
                              const customerName =
                                customer.customer_name ||
                                customer.name ||
                                customer.fullName ||
                                customerId;
                              handleInputChange("customer_id", customerId);
                              setEditedValues((prev) => ({
                                ...prev,
                                customerSearch: customerName,
                                showCustomerDropdown: false, // Hide dropdown after selection
                              }));
                            }}
                            style={{
                              padding: "8px 12px",
                              cursor: "pointer",
                              borderBottom: "1px solid #eee",
                              backgroundColor:
                                editedValues.customer_id ===
                                (customer.id || customer._id)
                                  ? "#f0f7ff"
                                  : "white",
                            }}
                            onMouseOver={(e) =>
                              (e.currentTarget.style.backgroundColor =
                                "#f5f5f5")
                            }
                            onMouseOut={(e) =>
                              (e.currentTarget.style.backgroundColor =
                                editedValues.customer_id ===
                                (customer.id || customer._id)
                                  ? "#f0f7ff"
                                  : "white")
                            }
                          >
                            {customer.customer_name ||
                              customer.name ||
                              customer.fullName ||
                              `${customer.id || customer._id}`}
                          </div>
                        ))}
                      {customers.filter((customer) => {
                        if (!editedValues.customerSearch) return true; // Show all when no search text
                        const customerName =
                          customer.customer_name ||
                          customer.name ||
                          customer.fullName ||
                          "";
                        return customerName
                          .toLowerCase()
                          .includes(editedValues.customerSearch.toLowerCase());
                      }).length === 0 && (
                        <div style={{ padding: "8px 12px", color: "#999" }}>
                          Không tìm thấy khách hàng
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="detail-value">
                  {customerLoading
                    ? "Đang tải..."
                    : (() => {
                        const customer = customers.find(
                          (c) => (c.id || c._id) === ticket.customer_id
                        );
                        return customer
                          ? customer.customer_name ||
                              customer.name ||
                              customer.fullName
                          : ticket.customer_id;
                      })()}
                </div>
              )}
            </div>

            {/* <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiPhone /> Số điện thoại
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="tel"
                  value={editedValues.phone || ""}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  style={{
                    padding: "5px",
                    borderRadius: "4px",
                    border: "1px solid #ddd",
                    flex: 1,
                  }}
                />
              ) : (
                <div className="detail-value">{ticket.phone || "N/A"}</div>
              )}
            </div> */}
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiMapPin /> Địa chỉ lấy hàng
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="text"
                  value={editedValues.pickup_address || ""}
                  onChange={(e) =>
                    handleInputChange("pickup_address", e.target.value)
                  }
                  style={getInputStyle("pickup_address")}
                />
              ) : (
                <div className="detail-value">{ticket.pickup_address}</div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiMapPin /> Địa chỉ giao hàng
              </div>
              {editingSections["basic-info"] ? (
                <input
                  type="text"
                  value={editedValues.delivery_address || ""}
                  onChange={(e) =>
                    handleInputChange("delivery_address", e.target.value)
                  }
                  style={getInputStyle("delivery_address")}
                />
              ) : (
                <div className="detail-value">{ticket.delivery_address}</div>
              )}
            </div>
          </div>

          {/* Vehicle Info */}
          <div
            className="ticket-detail-section"
            style={{ position: "relative" }}
          >
            <h3
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <div>
                <FiTruck /> Thông tin xe
              </div>
              {/* {(validationErrors.vehicle_info || validationErrors.vehicle_quantity) && (
                <div
                  style={{
                    color: "#f44336",
                    fontSize: "14px",
                    fontWeight: "normal",
                    marginLeft: "10px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FiX style={{ marginRight: "5px" }} />{" "}
                  {validationErrors.vehicle_info || validationErrors.vehicle_quantity}
                </div>
              )} */}
            </h3>
            {/* No edit/confirm/cancel buttons as all sections are always editable */}
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiTruck /> Thông tin xe
              </div>
              {editingSections["vehicle-info"] ? (
                <textarea
                  value={editedValues.vehicle_info || ""}
                  onChange={(e) =>
                    handleInputChange("vehicle_info", e.target.value)
                  }
                  style={getInputStyle("vehicle_info")}
                />
              ) : (
                <div className="detail-value">{ticket.vehicle_info}</div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiTruck /> Số lượng xe
              </div>
              {editingSections["vehicle-info"] ? (
                <input
                  type="text"
                  value={editedValues.vehicle_quantity || ""}
                  onChange={(e) =>
                    handleInputChange("vehicle_quantity", e.target.value)
                  }
                  style={getInputStyle("vehicle_quantity")}
                />
              ) : (
                <div className="detail-value">{ticket.vehicle_quantity}</div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiTruck /> Biển số xe (để xuất bảng kê cước vận chuyển)
              </div>
              {editingSections["vehicle-info"] ? (
                <input
                  type="text"
                  value={editedValues.vehicle_plate || ""}
                  onChange={(e) =>
                    handleInputChange("vehicle_plate", e.target.value)
                  }
                  style={getInputStyle("vehicle_plate")}
                />
              ) : (
                <div className="detail-value">{ticket.vehicle_plate}</div>
              )}
            </div>
            <div className="detail-item" style={{ display: "flex" }}>
              <div
                className="detail-label"
                style={{ width: "150px", minWidth: "150px" }}
              >
                <FiFileText /> Ghi chú
              </div>
              {editingSections["vehicle-info"] ? (
                <textarea
                  value={editedValues.vehicle_note || ""}
                  onChange={(e) =>
                    handleInputChange("vehicle_note", e.target.value)
                  }
                  style={{
                    ...getInputStyle("vehicle_note"),
                    flex: 1,
                    minHeight: "60px",
                    resize: "vertical",
                  }}
                />
              ) : (
                <div className="detail-value">
                  {ticket.vehicle_note || "Không có ghi chú"}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Row 2: Staff Info */}
        <div className="ticket-detail-row">
          {/* Staff Info */}
          <div
            className="ticket-detail-section"
            style={{ position: "relative" }}
          >
            <h3>
              <FiUsers /> Người phụ trách
            </h3>
            {/* No edit/confirm/cancel buttons as all sections are always editable */}
            <div style={{ display: "flex", gap: "20px" }}>
              <div style={{ flex: 1 }}>
                <div className="detail-item">
                  <div className="detail-label">
                    <FiUser /> Nhân viên định giá
                  </div>
                  {editingSections["staff-info"] ? (
                    <div
                      style={{ position: "relative" }}
                      ref={pricingStaffSearchRef}
                    >
                      <input
                        type="text"
                        value={editedValues.pricingStaffSearch || ""}
                        onChange={(e) => {
                          const searchValue = e.target.value;
                          setEditedValues((prev) => ({
                            ...prev,
                            pricingStaffSearch: searchValue,
                            showPricingStaffDropdown: true,
                          }));
                        }}
                        onFocus={() => {
                          setEditedValues((prev) => ({
                            ...prev,
                            showPricingStaffDropdown: true,
                          }));
                        }}
                        placeholder="Gõ để tìm nhân viên..."
                        style={getInputStyle("pricing_staff_id")}
                      />
                      {editedValues.showPricingStaffDropdown && (
                        <div
                          style={{
                            position: "absolute",
                            top: "100%",
                            left: 0,
                            right: 0,
                            maxHeight: "200px",
                            overflowY: "auto",
                            backgroundColor: "white",
                            border: "1px solid #ddd",
                            borderRadius: "4px",
                            zIndex: 10,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                          }}
                        >
                          {users
                            .filter((user) => {
                              if (!editedValues.pricingStaffSearch)
                                return user.role === "purchaser"; // Show only purchasers when no search text
                              const userName =
                                user.display_name ||
                                user.name ||
                                user.fullName ||
                                "";
                              return (
                                user.role === "purchaser" &&
                                userName
                                  .toLowerCase()
                                  .includes(
                                    editedValues.pricingStaffSearch.toLowerCase()
                                  )
                              );
                            })
                            .map((user) => (
                              <div
                                key={user.id || user._id}
                                onClick={() => {
                                  const userId = user.id || user._id;
                                  const userName =
                                    user.display_name ||
                                    user.name ||
                                    user.fullName ||
                                    userId;
                                  handleInputChange("pricing_staff_id", userId);
                                  setEditedValues((prev) => ({
                                    ...prev,
                                    pricingStaffSearch: userName,
                                    showPricingStaffDropdown: false,
                                  }));
                                }}
                                style={{
                                  padding: "8px 12px",
                                  cursor: "pointer",
                                  borderBottom: "1px solid #eee",
                                  backgroundColor:
                                    editedValues.pricing_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white",
                                }}
                                onMouseOver={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    "#f5f5f5")
                                }
                                onMouseOut={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    editedValues.pricing_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white")
                                }
                              >
                                {user.display_name ||
                                  user.name ||
                                  user.fullName ||
                                  `${user.id || user._id}`}
                              </div>
                            ))}
                          {users.filter((user) => {
                            if (!editedValues.pricingStaffSearch) return true; // Show all when no search text
                            const userName =
                              user.display_name ||
                              user.name ||
                              user.fullName ||
                              "";
                            return userName
                              .toLowerCase()
                              .includes(
                                editedValues.pricingStaffSearch.toLowerCase()
                              );
                          }).length === 0 && (
                            <div style={{ padding: "8px 12px", color: "#999" }}>
                              Không tìm thấy nhân viên
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="detail-value">
                      {usersLoading
                        ? "Đang tải..."
                        : (() => {
                            const user = users.find(
                              (u) => (u.id || u._id) === ticket.pricing_staff_id
                            );
                            return user
                              ? user.display_name || user.name || user.fullName
                              : ticket.pricing_staff_id || "Chưa phân công";
                          })()}
                    </div>
                  )}
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <div className="detail-item">
                  <div className="detail-label">
                    <FiUser /> Nhân viên điều phối
                  </div>
                  {editingSections["staff-info"] ? (
                    <div
                      style={{ position: "relative" }}
                      ref={dispatchStaffSearchRef}
                    >
                      <input
                        type="text"
                        value={editedValues.dispatchStaffSearch || ""}
                        onChange={(e) => {
                          const searchValue = e.target.value;
                          setEditedValues((prev) => ({
                            ...prev,
                            dispatchStaffSearch: searchValue,
                            showDispatchStaffDropdown: true,
                          }));
                        }}
                        onFocus={() => {
                          setEditedValues((prev) => ({
                            ...prev,
                            showDispatchStaffDropdown: true,
                          }));
                        }}
                        placeholder="Gõ để tìm nhân viên..."
                        style={getInputStyle("dispatch_staff_id")}
                      />
                      {editedValues.showDispatchStaffDropdown && (
                        <div
                          style={{
                            position: "absolute",
                            top: "100%",
                            left: 0,
                            right: 0,
                            maxHeight: "200px",
                            overflowY: "auto",
                            backgroundColor: "white",
                            border: "1px solid #ddd",
                            borderRadius: "4px",
                            zIndex: 10,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                          }}
                        >
                          {users
                            .filter((user) => {
                              if (!editedValues.dispatchStaffSearch)
                                return user.role === "moderator"; // Show only moderators when no search text
                              const userName =
                                user.display_name ||
                                user.name ||
                                user.fullName ||
                                "";
                              return (
                                user.role === "moderator" &&
                                userName
                                  .toLowerCase()
                                  .includes(
                                    editedValues.dispatchStaffSearch.toLowerCase()
                                  )
                              );
                            })
                            .map((user) => (
                              <div
                                key={user.id || user._id}
                                onClick={() => {
                                  const userId = user.id || user._id;
                                  const userName =
                                    user.display_name ||
                                    user.name ||
                                    user.fullName ||
                                    userId;
                                  handleInputChange(
                                    "dispatch_staff_id",
                                    userId
                                  );
                                  setEditedValues((prev) => ({
                                    ...prev,
                                    dispatchStaffSearch: userName,
                                    showDispatchStaffDropdown: false,
                                  }));
                                }}
                                style={{
                                  padding: "8px 12px",
                                  cursor: "pointer",
                                  borderBottom: "1px solid #eee",
                                  backgroundColor:
                                    editedValues.dispatch_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white",
                                }}
                                onMouseOver={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    "#f5f5f5")
                                }
                                onMouseOut={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    editedValues.dispatch_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white")
                                }
                              >
                                {user.display_name ||
                                  user.name ||
                                  user.fullName ||
                                  `${user.id || user._id}`}
                              </div>
                            ))}
                          {users.filter((user) => {
                            if (!editedValues.dispatchStaffSearch) return true; // Show all when no search text
                            const userName =
                              user.display_name ||
                              user.name ||
                              user.fullName ||
                              "";
                            return userName
                              .toLowerCase()
                              .includes(
                                editedValues.dispatchStaffSearch.toLowerCase()
                              );
                          }).length === 0 && (
                            <div style={{ padding: "8px 12px", color: "#999" }}>
                              Không tìm thấy nhân viên
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="detail-value">
                      {usersLoading
                        ? "Đang tải..."
                        : (() => {
                            const user = users.find(
                              (u) =>
                                (u.id || u._id) === ticket.dispatch_staff_id
                            );
                            return user
                              ? user.display_name || user.name || user.fullName
                              : ticket.dispatch_staff_id || "Chưa phân công";
                          })()}
                    </div>
                  )}
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <div className="detail-item">
                  <div className="detail-label">
                    <FiUser /> Nhân viên bán hàng
                  </div>
                  {editingSections["staff-info"] ? (
                    <div
                      style={{ position: "relative" }}
                      ref={saleStaffSearchRef}
                    >
                      <input
                        type="text"
                        value={editedValues.saleStaffSearch || ""}
                        onChange={(e) => {
                          const searchValue = e.target.value;
                          setEditedValues((prev) => ({
                            ...prev,
                            saleStaffSearch: searchValue,
                            showSaleStaffDropdown: true,
                          }));
                        }}
                        onFocus={() => {
                          setEditedValues((prev) => ({
                            ...prev,
                            showSaleStaffDropdown: true,
                          }));
                        }}
                        placeholder="Gõ để tìm nhân viên..."
                        style={getInputStyle("sale_staff_id")}
                      />
                      {editedValues.showSaleStaffDropdown && (
                        <div
                          style={{
                            position: "absolute",
                            top: "100%",
                            left: 0,
                            right: 0,
                            maxHeight: "200px",
                            overflowY: "auto",
                            backgroundColor: "white",
                            border: "1px solid #ddd",
                            borderRadius: "4px",
                            zIndex: 10,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                          }}
                        >
                          {users
                            .filter((user) => {
                              if (!editedValues.saleStaffSearch)
                                return user.role === "sale"; // Show only sales when no search text
                              const userName =
                                user.display_name ||
                                user.name ||
                                user.fullName ||
                                "";
                              return (
                                user.role === "sale" &&
                                userName
                                  .toLowerCase()
                                  .includes(
                                    editedValues.saleStaffSearch.toLowerCase()
                                  )
                              );
                            })
                            .map((user) => (
                              <div
                                key={user.id || user._id}
                                onClick={() => {
                                  const userId = user.id || user._id;
                                  const userName =
                                    user.display_name ||
                                    user.name ||
                                    user.fullName ||
                                    userId;
                                  handleInputChange("sale_staff_id", userId);
                                  setEditedValues((prev) => ({
                                    ...prev,
                                    saleStaffSearch: userName,
                                    showSaleStaffDropdown: false,
                                  }));
                                }}
                                style={{
                                  padding: "8px 12px",
                                  cursor: "pointer",
                                  borderBottom: "1px solid #eee",
                                  backgroundColor:
                                    editedValues.sale_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white",
                                }}
                                onMouseOver={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    "#f5f5f5")
                                }
                                onMouseOut={(e) =>
                                  (e.currentTarget.style.backgroundColor =
                                    editedValues.sale_staff_id ===
                                    (user.id || user._id)
                                      ? "#f0f7ff"
                                      : "white")
                                }
                              >
                                {user.display_name ||
                                  user.name ||
                                  user.fullName ||
                                  `${user.id || user._id}`}
                              </div>
                            ))}
                          {users.filter((user) => {
                            if (!editedValues.saleStaffSearch) return true; // Show all when no search text
                            const userName =
                              user.display_name ||
                              user.name ||
                              user.fullName ||
                              "";
                            return userName
                              .toLowerCase()
                              .includes(
                                editedValues.saleStaffSearch.toLowerCase()
                              );
                          }).length === 0 && (
                            <div style={{ padding: "8px 12px", color: "#999" }}>
                              Không tìm thấy nhân viên
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="detail-value">
                      {usersLoading
                        ? "Đang tải..."
                        : (() => {
                            const user = users.find(
                              (u) => (u.id || u._id) === ticket.sale_staff_id
                            );
                            return user
                              ? user.display_name || user.name || user.fullName
                              : ticket.sale_staff_id || "Chưa phân công";
                          })()}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Row 3: Pricing Info */}
        <div className="ticket-detail-row">
          <div
            style={{
              flex: 2,
              display: "flex",
              flexDirection: "column",
              gap: "20px",
            }}
          >
            {/* Buy Price */}
            <div
              className="ticket-detail-section"
              style={{ position: "relative" }}
            >
              <h3
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <div>
                  <FiDollarSign /> Thông tin giá mua
                </div>
                {validationErrors.ticketPay && (
                  <div
                    style={{
                      color: "#f44336",
                      fontSize: "14px",
                      fontWeight: "normal",
                      marginLeft: "10px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <FiX style={{ marginRight: "5px" }} />{" "}
                    {validationErrors.ticketPay}
                  </div>
                )}
              </h3>
              {/* No edit/confirm/cancel buttons as all sections are always editable */}
              {editingSections["buy-price"] ? (
                <div>
                  <table
                    className="price-table"
                    style={
                      validationErrors.ticketPay
                        ? {
                            border: "1px solid #f44336",
                            boxShadow: "0 0 0 2px rgba(244, 67, 54, 0.2)",
                          }
                        : {}
                    }
                  >
                    <thead>
                      <tr>
                        <th>Nhà cung cấp</th>
                        <th>Mô tả</th>
                        <th>Giá trước thuế</th>
                        <th>Thuế (%)</th>
                        <th>Giá sau thuế</th>
                      </tr>
                    </thead>
                    <tbody>
                      {editedValues.ticketPay &&
                        editedValues.ticketPay.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <div style={{ position: "relative" }}>
                                <input
                                  id={`supplier-input-${index}`}
                                  type="text"
                                  value={item.supplierSearch || ""}
                                  onChange={(e) => {
                                    const searchValue = e.target.value;
                                    // Update the search value in the item
                                    const updatedItem = {
                                      ...item,
                                      supplierSearch: searchValue,
                                    };
                                    const newArray = [
                                      ...editedValues.ticketPay,
                                    ];
                                    newArray[index] = updatedItem;
                                    setEditedValues((prev) => ({
                                      ...prev,
                                      ticketPay: newArray,
                                      [`showSupplierDropdown_${index}`]: true,
                                    }));
                                  }}
                                  onFocus={() => {
                                    setEditedValues((prev) => ({
                                      ...prev,
                                      [`showSupplierDropdown_${index}`]: true,
                                    }));
                                  }}
                                  placeholder="Tìm nhà cung cấp..."
                                  style={{
                                    width: "100%",
                                    padding: "5px",
                                    borderRadius: "4px",
                                    border: "1px solid #ddd",
                                  }}
                                />
                                {editedValues[
                                  `showSupplierDropdown_${index}`
                                ] && (
                                  <div
                                    id={`supplier-dropdown-${index}`}
                                    style={{
                                      position: "absolute",
                                      top: "100%", /* Position below the input */
                                      left: 0,
                                      right: 0,
                                      maxHeight: "150px",
                                      overflowY: "auto",
                                      backgroundColor: "white",
                                      border: "1px solid #ddd",
                                      borderRadius: "4px",
                                      zIndex: 9999, /* Very high z-index to ensure it's always on top */
                                      boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                                    }}
                                  >
                                    {suppliers
                                      .filter((supplier) => {
                                        if (!item.supplierSearch) return true; // Show all when no search text
                                        const supplierName =
                                          supplier.supplier_name ||
                                          supplier.name ||
                                          supplier.fullName ||
                                          "";
                                        return supplierName
                                          .toLowerCase()
                                          .includes(
                                            item.supplierSearch.toLowerCase()
                                          );
                                      })
                                      .map((supplier) => (
                                        <div
                                          key={supplier.id || supplier._id}
                                          onClick={() => {
                                            const supplierId =
                                              supplier.id || supplier._id;
                                            const supplierName =
                                              supplier.supplier_name ||
                                              supplier.name ||
                                              supplier.fullName ||
                                              supplierId;

                                            // Update both the supplier_id and the search text
                                            const updatedItem = {
                                              ...item,
                                              supplier_id: supplierId,
                                              supplierSearch: supplierName,
                                            };
                                            const newArray = [
                                              ...editedValues.ticketPay,
                                            ];
                                            newArray[index] = updatedItem;

                                            setEditedValues((prev) => ({
                                              ...prev,
                                              ticketPay: newArray,
                                              [`showSupplierDropdown_${index}`]: false,
                                            }));
                                          }}
                                          style={{
                                            padding: "8px 12px",
                                            cursor: "pointer",
                                            borderBottom: "1px solid #eee",
                                            backgroundColor:
                                              item.supplier_id ===
                                              (supplier.id || supplier._id)
                                                ? "#f0f7ff"
                                                : "white",
                                          }}
                                          onMouseOver={(e) =>
                                            (e.currentTarget.style.backgroundColor =
                                              "#f5f5f5")
                                          }
                                          onMouseOut={(e) =>
                                            (e.currentTarget.style.backgroundColor =
                                              item.supplier_id ===
                                              (supplier.id || supplier._id)
                                                ? "#f0f7ff"
                                                : "white")
                                          }
                                        >
                                          {supplier.supplier_name ||
                                            supplier.name ||
                                            supplier.fullName ||
                                            `${supplier.id || supplier._id}`}
                                        </div>
                                      ))}
                                    {suppliers.filter((supplier) => {
                                      if (!item.supplierSearch) return true;
                                      const supplierName =
                                        supplier.supplier_name ||
                                        supplier.name ||
                                        supplier.fullName ||
                                        "";
                                      return supplierName
                                        .toLowerCase()
                                        .includes(
                                          item.supplierSearch.toLowerCase()
                                        );
                                    }).length === 0 && (
                                      <div
                                        style={{
                                          padding: "8px 12px",
                                          color: "#999",
                                        }}
                                      >
                                        Không tìm thấy nhà cung cấp
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </td>
                            <td>
                              <textarea
                                type="text"
                                value={item.description}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "buy",
                                    index,
                                    "description",
                                    e.target.value
                                  )
                                }
                                style={{
                                  minWidth: "100%",
                                  height: "80px",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: validationErrors.ticketPay
                                    ? "1px solid #f44336"
                                    : "1px solid #ddd",
                                  backgroundColor: validationErrors.ticketPay
                                    ? "#fff8f8"
                                    : "white",
                                }}
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={item.amount_bfr_tax}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "buy",
                                    index,
                                    "amount_bfr_tax",
                                    e.target.value
                                  )
                                }
                                min="0"
                                placeholder="Nhập giá"
                                style={{
                                  width: "100%",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={item.tax_rate}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "buy",
                                    index,
                                    "tax_rate",
                                    e.target.value
                                  )
                                }
                                style={{
                                  width: "100%",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>{formatCurrency(item.amount_aft_tax)}</td>
                            <td>
                              <button
                                onClick={() =>
                                  handleDeletePriceItem("buy", index)
                                }
                                style={{
                                  background: "#f44336",
                                  border: "none",
                                  borderRadius: "4px",
                                  padding: "5px 10px",
                                  cursor: "pointer",
                                  color: "white",
                                  fontSize: "12px",
                                }}
                              >
                                X
                              </button>
                            </td>
                          </tr>
                        ))}
                      <tr
                        style={{
                          fontWeight: "bold",
                          borderTop: "2px solid #eee",
                        }}
                      >
                        <td>Tổng cộng</td>
                        <td></td>
                        <td>
                          {formatCurrency(
                            editedValues.ticketPay
                              ? editedValues.ticketPay.reduce(
                                  (sum, item) =>
                                    sum +
                                    (parseFloat(item.amount_bfr_tax) || 0),
                                  0
                                )
                              : 0
                          )}
                        </td>
                        <td></td>
                        <td>
                          {formatCurrency(
                            editedValues.ticketPay
                              ? editedValues.ticketPay.reduce(
                                  (sum, item) =>
                                    sum +
                                    (parseFloat(item.amount_aft_tax) || 0),
                                  0
                                )
                              : 0
                          )}
                        </td>
                        <td></td>
                      </tr>
                    </tbody>
                  </table>
                  <button
                    onClick={() => handleAddPriceItem("buy")}
                    style={{
                      marginTop: "10px",
                      background: "#4a90e2",
                      border: "none",
                      borderRadius: "4px",
                      padding: "5px 10px",
                      cursor: "pointer",
                      color: "white",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                      width: "fit-content",
                    }}
                  >
                    + Thêm mục
                  </button>
                </div>
              ) : ticketPay && ticketPay.length > 0 ? (
                <table className="price-table">
                  <thead>
                    <tr>
                      <th>Mô tả</th>
                      <th>Giá trước thuế</th>
                      <th>Thuế (%)</th>
                      <th>Giá sau thuế</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ticketPay.map((item, index) => (
                      <tr key={index}>
                        <td>{item.description}</td>
                        <td>{formatCurrency(item.amount_bfr_tax)}</td>
                        <td>{item.tax_rate}%</td>
                        <td>{formatCurrency(item.amount_aft_tax)}</td>
                      </tr>
                    ))}
                    <tr
                      style={{
                        fontWeight: "bold",
                        borderTop: "2px solid #eee",
                      }}
                    >
                      <td>Tổng cộng</td>
                      <td></td>
                      <td>
                        {formatCurrency(
                          ticketPay.reduce(
                            (sum, item) => sum + item.amount_bfr_tax,
                            0
                          )
                        )}
                      </td>
                      <td></td>
                      <td>
                        {formatCurrency(
                          ticketPay.reduce(
                            (sum, item) => sum + item.amount_aft_tax,
                            0
                          )
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              ) : (
                <div className="detail-value">Chưa có thông tin giá mua</div>
              )}
            </div>

            {/* Sell Price */}
            <div
              className="ticket-detail-section"
              style={{ position: "relative" }}
            >
              <h3
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <div>
                  <FiDollarSign /> Thông tin giá bán
                </div>
                {validationErrors.ticketReceive && (
                  <div
                    style={{
                      color: "#f44336",
                      fontSize: "14px",
                      fontWeight: "normal",
                      marginLeft: "10px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <FiX style={{ marginRight: "5px" }} />{" "}
                    {validationErrors.ticketReceive}
                  </div>
                )}
              </h3>
              {/* No edit/confirm/cancel buttons as all sections are always editable */}
              {editingSections["sell-price"] ? (
                <div>
                  <table
                    className="price-table"
                    style={
                      validationErrors.ticketReceive
                        ? {
                            border: "1px solid #f44336",
                            boxShadow: "0 0 0 2px rgba(244, 67, 54, 0.2)",
                          }
                        : {}
                    }
                  >
                    <thead>
                      <tr>
                        <th>Mô tả</th>
                        <th>Giá trước thuế</th>
                        <th>Thuế (%)</th>
                        <th>Giá sau thuế</th>
                      </tr>
                    </thead>
                    <tbody>
                      {editedValues.ticketReceive &&
                        editedValues.ticketReceive.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <textarea
                                type="text"
                                value={item.description}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "sell",
                                    index,
                                    "description",
                                    e.target.value
                                  )
                                }
                                style={{
                                  minWidth: "100%",
                                  padding: "5px",
                                  height: "80px",
                                  borderRadius: "4px",
                                  border: validationErrors.ticketReceive
                                    ? "1px solid #f44336"
                                    : "1px solid #ddd",
                                  backgroundColor:
                                    validationErrors.ticketReceive
                                      ? "#fff8f8"
                                      : "white",
                                }}
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={item.amount_bfr_tax}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "sell",
                                    index,
                                    "amount_bfr_tax",
                                    e.target.value
                                  )
                                }
                                min="0"
                                placeholder="Nhập giá"
                                style={{
                                  width: "100%",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>
                              <input
                                type="number"
                                value={item.tax_rate}
                                onChange={(e) =>
                                  handlePriceItemChange(
                                    "sell",
                                    index,
                                    "tax_rate",
                                    e.target.value
                                  )
                                }
                                style={{
                                  width: "100%",
                                  padding: "5px",
                                  borderRadius: "4px",
                                  border: "1px solid #ddd",
                                }}
                              />
                            </td>
                            <td>{formatCurrency(item.amount_aft_tax)}</td>
                            <td>
                              <button
                                onClick={() =>
                                  handleDeletePriceItem("sell", index)
                                }
                                style={{
                                  background: "#f44336",
                                  border: "none",
                                  borderRadius: "4px",
                                  padding: "5px 10px",
                                  cursor: "pointer",
                                  color: "white",
                                  fontSize: "12px",
                                }}
                              >
                                X
                              </button>
                            </td>
                          </tr>
                        ))}
                      <tr
                        style={{
                          fontWeight: "bold",
                          borderTop: "2px solid #eee",
                        }}
                      >
                        <td>Tổng cộng</td>
                        <td>
                          {formatCurrency(
                            editedValues.ticketReceive
                              ? editedValues.ticketReceive.reduce(
                                  (sum, item) =>
                                    sum +
                                    (parseFloat(item.amount_bfr_tax) || 0),
                                  0
                                )
                              : 0
                          )}
                        </td>
                        <td></td>
                        <td>
                          {formatCurrency(
                            editedValues.ticketReceive
                              ? editedValues.ticketReceive.reduce(
                                  (sum, item) =>
                                    sum +
                                    (parseFloat(item.amount_aft_tax) || 0),
                                  0
                                )
                              : 0
                          )}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <button
                    onClick={() => handleAddPriceItem("sell")}
                    style={{
                      marginTop: "10px",
                      background: "#4a90e2",
                      border: "none",
                      borderRadius: "4px",
                      padding: "5px 10px",
                      cursor: "pointer",
                      color: "white",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "14px",
                      width: "fit-content",
                    }}
                  >
                    + Thêm mục
                  </button>
                </div>
              ) : ticketReceive && ticketReceive.length > 0 ? (
                <table className="price-table">
                  <thead>
                    <tr>
                      <th>Mô tả</th>
                      <th>Giá trước thuế</th>
                      <th>Thuế (%)</th>
                      <th>Giá sau thuế</th>
                    </tr>
                  </thead>
                  <tbody>
                    {ticketReceive.map((item, index) => (
                      <tr key={index}>
                        <td>{item.description}</td>
                        <td>{formatCurrency(item.amount_bfr_tax)}</td>
                        <td>{item.tax_rate}%</td>
                        <td>{formatCurrency(item.amount_aft_tax)}</td>
                      </tr>
                    ))}
                    <tr
                      style={{
                        fontWeight: "bold",
                        borderTop: "2px solid #eee",
                      }}
                    >
                      <td>Tổng cộng</td>
                      <td></td>
                      <td>
                        {formatCurrency(
                          ticketReceive.reduce(
                            (sum, item) => sum + item.amount_bfr_tax,
                            0
                          )
                        )}
                      </td>
                      <td></td>
                      <td>
                        {formatCurrency(
                          ticketReceive.reduce(
                            (sum, item) => sum + item.amount_aft_tax,
                            0
                          )
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              ) : (
                <div className="detail-value">Chưa có thông tin giá bán</div>
              )}
            </div>

            {/* Row 4: Notes */}
            <div className="ticket-detail-row">
              <div
                className="ticket-detail-section"
                style={{ position: "relative" }}
              >
                <h3>
                  <FiFileText /> Ghi chú khác
                </h3>
                {/* No edit/confirm/cancel buttons as all sections are always editable */}
                {editingSections["notes"] ? (
                  <textarea
                    value={editedValues.note || ""}
                    onChange={(e) => handleInputChange("note", e.target.value)}
                    style={{
                      ...getInputStyle("note"),
                      padding: "10px",
                      minHeight: "100px",
                      resize: "vertical",
                    }}
                  />
                ) : (
                  <div className="detail-value">
                    {ticket.note || "Không có ghi chú"}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right column - takes up 1/3 of width */}
          <div style={{ flex: 1 }}>
            {/* Empty space or can be used for future content */}
            <div className="ticket-detail-row">
              {/* Right column - takes up 1/3 of width - Recent Activity */}
              <div style={{ flex: 1 }}>
                <div
                  className="ticket-detail-section"
                  style={{ position: "relative" }}
                >
                  <h3>
                    <FiActivity /> Hoạt động gần đây
                  </h3>
                  {ticketLogs && ticketLogs.length > 0 ? (
                    <div className="activity-container">
                      {ticketLogs.map((log, index) => (
                        <div className="activity-item" key={index}>
                          <div className="activity-icon">
                            {log.log_type === "change_status" ? (
                              <FiActivity />
                            ) : log.log_type === "change_basic" ? (
                              <FiInfo />
                            ) : log.log_type === "change_vehicle" ? (
                              <FiTruck />
                            ) : log.log_type === "change_staff" ? (
                              <FiUsers />
                            ) : log.log_type === "change_receive" ? (
                              <FiDollarSign />
                            ) : log.log_type === "change_pay" ? (
                              <FiDollarSign />
                            ) : (
                              <FiEdit />
                            )}
                          </div>
                          <div className="activity-content">
                            <div className="activity-type">
                              {log.log_type === "change_status"
                                ? "Thay đổi trạng thái"
                                : log.log_type === "change_basic"
                                ? "Cập nhật thông tin cơ bản"
                                : log.log_type === "change_vehicle"
                                ? "Cập nhật thông tin xe"
                                : log.log_type === "change_staff"
                                ? "Cập nhật người phụ trách"
                                : log.log_type === "change_receive"
                                ? "Cập nhật giá bán"
                                : log.log_type === "change_pay"
                                ? "Cập nhật giá mua"
                                : log.log_type === "field_change"
                                ? "Cập nhật thông tin"
                                : log.log_type}
                            </div>
                            {/* <div className="activity-value">{log.value}</div> */}
                            <div className="activity-meta">
                              <div className="activity-user">
                                bởi {log.changed_by || "Hệ thống"}
                              </div>
                              <div className="activity-date">
                                {formatDateTime(log.created_at)}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="detail-value">
                      Không có hoạt động gần đây
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Status Update Modal */}
      {showStatusModal && (
        <div className="status-modal-overlay">
          <div className="status-modal">
            <h3>Cập nhật trạng thái</h3>
            <div className="status-current">
              <span>Trạng thái hiện tại: </span>
              <span className={`ticket-status ${ticket.current_status}`}>
                {getStatusLabel(ticket.current_status)}
              </span>
            </div>

            <div className="status-options">
              <h4>Chọn trạng thái mới:</h4>
              {statusTimeline.map((status, index) => {
                // Only show statuses that come after the current one in the workflow
                const currentIndex = statusTimeline.findIndex(
                  (s) => s.status === ticket.current_status
                );
                if (index <= currentIndex) return null;

                // Only allow updating to the next status in the sequence
                const isNextStatus = index === currentIndex + 1;

                return (
                  <div
                    key={status.status}
                    className={`status-option ${
                      selectedStatus === status.status ? "selected" : ""
                    } ${!isNextStatus ? "disabled" : ""}`}
                    onClick={() =>
                      isNextStatus && handleStatusChange(status.status)
                    }
                  >
                    <div className="status-option-icon">
                      {isNextStatus ? <FiCheckCircle /> : <FiClock />}
                    </div>
                    <div className="status-option-label">{status.label}</div>
                    {!isNextStatus && (
                      <div className="status-option-message">
                        Cần hoàn thành các trạng thái trước
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            <div className="status-modal-actions">
              <button
                className="btn-cancel"
                onClick={() => setShowStatusModal(false)}
              >
                <FiX /> Huỷ bỏ
              </button>
              <button
                className="btn-confirm"
                onClick={handleConfirmStatusChange}
                disabled={
                  !selectedStatus || selectedStatus === ticket.current_status
                }
              >
                <FiCheckCircle /> Xác nhận
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Submit Button removed from here and moved to header */}

      {/* We no longer show validation errors in a summary - they are highlighted in the fields */}
    </div>
  );
};

export default TicketChiTiet;
