import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  FiDollarSign,
  FiArrowLeft,
  FiClock,
  FiUser,
  FiCalendar,
  FiDatabase,
  FiServer,
  FiCheckCircle,
  FiXCircle,
  FiLoader,
  FiFileText,
} from "react-icons/fi";
import apiService from "../../services/api.service";
import { ENDPOINTS } from "../../config/api.config";
import "../../styles/RequestDetail.css";

const RequestDetail = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [request, setRequest] = useState(null);
  const [requestLog, setRequestLog] = useState(null);
  const [requestPay, setRequestPay] = useState(null);
  const [requestReceive, setRequestReceive] = useState(null);
  const requestIdRef = useRef(null);

  useEffect(() => {
    let isMounted = true;
    const requestId = new URLSearchParams(location.search).get("id");

    // Skip if the same request ID
    if (requestId === requestIdRef.current) {
      return;
    }
    requestIdRef.current = requestId;

    const fetchRequest = async () => {
      if (!requestId) {
        setError("Request ID is required");
        return;
      }

      try {
        const response = await apiService.get(ENDPOINTS.TICKET.GET(requestId));
        if (isMounted) {
          setRequest(response.ticket_basic);
          setRequestLog(response.ticket_logs);
          setRequestPay(response.ticket_pay);
          setRequestReceive(response.ticket_receive);
        }
      } catch (err) {
        console.error("Error fetching request:", err);
        if (isMounted) {
          setError("Failed to load request details");
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchRequest();

    return () => {
      isMounted = false;
    };
  }, [location.search]);

  const getStatusSteps = () => {
    // Define the possible status flows
    const steps = [
      { key: "initial", label: "Initial", icon: <FiClock /> },
      { key: "approved", label: "Approved", icon: <FiCheckCircle /> },
      { key: "delivered", label: "Delivered", icon: <FiLoader /> },
      { key: "customerpaid", label: "CustomerPaid", icon: <FiDollarSign /> },
      { key: "supplierpaid", label: "SupplierPaid", icon: <FiDollarSign /> },
      { key: "rejected", label: "Rejected", icon: <FiXCircle /> },
      { key: "completed", label: "Completed", icon: <FiCheckCircle /> },
    ];

    // Determine the current flow and active step
    let currentFlow = [];
    let activeIndex = -1;

    if (request) {
      // Default flow: initial -> approved -> delivered -> [customerpaid, supplierpaid] -> completed
      if (request.current_status === "rejected") {
        currentFlow = [steps[0], steps[2]]; // initial -> rejected
        activeIndex = 1;
      } else {
        // Standard flow
        currentFlow = [
          steps[0],
          steps[1],
          steps[2],
          steps[3],
          steps[4],
          steps[5],
        ]; // initial -> approved -> delivered -> [customerpaid, supplierpaid] -> completed

        switch (request.current_status) {
          case "initial":
            activeIndex = 0;
            break;
          case "approved":
            activeIndex = 1;
            break;
          case "delivered":
            activeIndex = 2;
            break;
          case "customerpaid":
            activeIndex = 3;
            break;
          case "supplierpaid":
            activeIndex = 3;
            break;
          case "completed":
            activeIndex = 4;
            break;
          default:
            activeIndex = 0;
        }
      }
    }

    return { currentFlow, activeIndex };
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Not specified";
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getRequestTypeLabel = (type) => {
    switch (type) {
      case "adhoc-data":
        return "Ad-hoc Data";
      case "access-platform":
        return "Platform Access";
      case "access-database":
        return "Database Access";
      case "access-report":
        return "Report Access";
      default:
        return type;
    }
  };

  if (loading) {
    return <div className="request-detail-container loading">Loading...</div>;
  }

  if (error) {
    return (
      <div className="request-detail-container error">
        <div className="error-message">{error}</div>
        <button className="btn-primary" onClick={() => navigate("/request")}>
          <FiArrowLeft /> Back to All Requests
        </button>
      </div>
    );
  }

  const { currentFlow, activeIndex } = getStatusSteps();

  return (
    <div className="request-detail-container">
      <div className="request-detail-header">
        <button className="back-button" onClick={() => navigate("/request")}>
          <FiArrowLeft /> Back to Requests
        </button>
        <h1>{request.title}</h1>
        <div className={`status-badge ${request.current_status}`}>
          {request.current_status.replace("_", " ")}
        </div>
      </div>

      <div className="request-detail-status-flow">
        {currentFlow.map((step, index) => (
          <div
            key={step.key}
            className={`status-step ${index <= activeIndex ? "active" : ""} ${
              index === activeIndex ? "current" : ""
            }`}
          >
            <div className="step-icon">{step.icon}</div>
            <div className="step-label">{step.label}</div>
            {index < currentFlow.length - 1 && (
              <div
                className={`step-connector ${
                  index < activeIndex ? "active" : ""
                }`}
              ></div>
            )}
          </div>
        ))}
      </div>

      <div className="request-detail-body">
        <div className="request-detail-section">
          <h2>Request Information</h2>
          <div className="request-detail-grid">
            {request.request_date && (
              <div className="detail-item">
                <div className="detail-icon">
                  <FiCalendar />
                </div>
                <div className="detail-content">
                  <div className="detail-label">Request Date</div>
                  <div className="detail-value">{request.request_date}</div>
                </div>
              </div>
            )}

            <div className="detail-item">
              <div className="detail-icon">
                <FiServer />
              </div>
              <div className="detail-content">
                <div className="detail-label">Customer ID</div>
                <div className="detail-value">{request.customer_id}</div>
              </div>
            </div>

            <div className="detail-item">
              <div className="detail-icon">
                <FiDatabase />
              </div>
              <div className="detail-content">
                <div className="detail-label">Supplier ID</div>
                <div className="detail-value">{request.supplier_id}</div>
              </div>
            </div>

            <div className="detail-item">
              <div className="detail-icon">
                <FiDatabase />
              </div>
              <div className="detail-content">
                <div className="detail-label">Company ID</div>
                <div className="detail-value">{request.company_id}</div>
              </div>
            </div>

            <div className="detail-item">
              <div className="detail-icon">
                <FiFileText />
              </div>
              <div className="detail-content">
                <div className="detail-label">Pickup Address</div>
                <div className="detail-value description">
                  {request.pickup_address}
                </div>
              </div>
            </div>

            <div className="detail-item">
              <div className="detail-icon">
                <FiFileText />
              </div>
              <div className="detail-content">
                <div className="detail-label">Delivery Address</div>
                <div className="detail-value description">
                  {request.delivery_address}
                </div>
              </div>
            </div>

            <div className="detail-item">
              <div className="detail-icon">
                <FiDatabase />
              </div>
              <div className="detail-content">
                <div className="detail-label">Vehicle Info</div>
                <div className="detail-value">{request.vehicle_info}</div>
              </div>
            </div>

            <div className="detail-item">
              <div className="detail-icon">
                <FiDatabase />
              </div>
              <div className="detail-content">
                <div className="detail-label">Vehicle Quantity</div>
                <div className="detail-value">{request.vehicle_quantity}</div>
              </div>
            </div>
          </div>
        </div>

        <div className="request-detail-section">
          <h2>People</h2>
          <div className="request-people">
            <div className="people-item">
              <div className="people-role">Pricing Staff</div>
              <div className="people-id">ID: {request.pricing_staff_id}</div>
            </div>
            <div className="people-item">
              <div className="people-role">Dispatch Staff</div>
              <div className="people-id">ID: {request.dispatch_staff_id}</div>
            </div>
            <div className="people-item">
              <div className="people-role">Sale Staff</div>
              <div className="people-id">ID: {request.sale_staff_id}</div>
            </div>
          </div>
        </div>

        <div className="request-detail-section">
          <h2>Timeline</h2>
          <div className="request-timeline">
            <div className="timeline-item">
              <div className="timeline-icon">
                <FiClock />
              </div>
              <div className="timeline-content">
                <div className="timeline-title">Ticket Initiated</div>
                <div className="timeline-time">
                  {formatDate(request.created_at)}
                </div>
                <div className="timeline-description">Ticket was initiated</div>
              </div>
            </div>

            {requestLog &&
              requestLog.length > 0 &&
              requestLog.map((log, idx) => (
                <div className="timeline-item" key={idx}>
                  <div className="timeline-icon">
                    <FiClock />
                  </div>
                  <div className="timeline-content">
                    <div className="timeline-title">
                      Ticket {log.value || "No Value"}
                    </div>
                    <div className="timeline-time">
                      {formatDate(log.created_at)}
                    </div>
                    {log.note && (
                      <div className="timeline-description">{log.note}</div>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </div>

        <div className="request-detail-section">
          <h2>Pay & Receive Info</h2>
          <div className="request-detail-section__columns">
            <div className="request-detail-section__column">
              <h3>Pay</h3>
              {requestPay && requestPay.length > 0 ? (
                requestPay.map((item) => (
                  <div className="people-item" key={item.item_id}>
                    <div className="people-role">{item.description}</div>
                    <div className="people-id">Amount (before tax): {item.amount_bfr_tax}</div>
                    <div className="people-id">Tax Rate: {item.tax_rate}%</div>
                    <div className="people-id">Amount (after tax): {item.amount_aft_tax}</div>
                    <div className="people-id">Created At: {formatDate(item.created_at)}</div>
                  </div>
                ))
              ) : (
                <div className="people-item">No pay info available.</div>
              )}
            </div>
            <div className="request-detail-section__column">
              <h3>Receive</h3>
              {requestReceive && requestReceive.length > 0 ? (
                requestReceive.map((item) => (
                  <div className="people-item" key={item.item_id}>
                    <div className="people-role">{item.description}</div>
                    <div className="people-id">Amount (before tax): {item.amount_bfr_tax}</div>
                    <div className="people-id">Tax Rate: {item.tax_rate}%</div>
                    <div className="people-id">Amount (after tax): {item.amount_aft_tax}</div>
                    <div className="people-id">Created At: {formatDate(item.created_at)}</div>
                  </div>
                ))
              ) : (
                <div className="people-item">No receive info available.</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestDetail;
