import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>hart, FiLineChart } from 'react-icons/fi';
import {
  <PERSON>Chart, Pie, Cell, ResponsiveContainer, Tooltip, Legend,
  LineChart, Line, XAxis, YAxis, CartesianGrid, ReferenceLine,
  Brush, Label
} from 'recharts';
import '../styles/Dashboard.css';

const Dashboard = ({ navigateToTickets }) => {
  // Xử lý click vào card thống kê
  const handleStatCardClick = (status) => {
    navigateToTickets(status);
  };

  // Dữ liệu cho biểu đồ phân bố trạng thái
  const statusData = [
    { name: 'Khởi tạo', value: 15 },
    { name: 'Đ<PERSON> duyệt', value: 25 },
    { name: 'Đã điền giá', value: 30 },
    { name: 'Đ<PERSON> giao hàng', value: 20 },
    { name: 'Đ<PERSON> thu tiền KH', value: 18 },
    { name: '<PERSON><PERSON> tr<PERSON> tiền NCC', value: 12 },
    { name: '<PERSON><PERSON><PERSON> thành', value: 35 }
  ];

  // <PERSON><PERSON> liệu cho biểu đồ theo thời gian - b<PERSON> sung thêm data series
  const timeData = [
    { date: '01/08', new: 5, processing: 8, completed: 3, total: 16 },
    { date: '02/08', new: 7, processing: 12, completed: 5, total: 24 },
    { date: '03/08', new: 8, processing: 15, completed: 7, total: 30 },
    { date: '04/08', new: 6, processing: 10, completed: 9, total: 25 },
    { date: '05/08', new: 9, processing: 18, completed: 11, total: 38 },
    { date: '06/08', new: 7, processing: 14, completed: 13, total: 34 },
    { date: '07/08', new: 8, processing: 16, completed: 15, total: 39 }
  ];

  // Màu sắc cho các trạng thái (more vibrant colors)
  const COLORS = [
    '#64b5f6', // Khởi tạo - Blue
    '#7986cb', // Đã duyệt - Indigo
    '#ba68c8', // Đã điền giá - Purple
    '#4dd0e1', // Đã giao hàng - Cyan
    '#81c784', // Đã thu tiền KH - Green
    '#ffb74d', // Đã trả tiền NCC - Orange
    '#f06292'  // Hoàn thành - Pink
  ];

  // Màu sắc cho line chart
  const LINE_COLORS = {
    new: '#2196F3',       // Blue
    processing: '#FF9800', // Orange
    completed: '#4CAF50',  // Green
    total: '#9C27B0'       // Purple
  };

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip">
          <p className="label">{`${payload[0].name}: ${payload[0].value}`}</p>
        </div>
      );
    }
    return null;
  };

  const LineChartTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip line-tooltip">
          <p className="tooltip-date">{label}</p>
          <div className="tooltip-items">
            {payload.map((entry, index) => (
              <div key={`item-${index}`} className="tooltip-item">
                <div 
                  className="tooltip-marker" 
                  style={{ backgroundColor: entry.color }} 
                />
                <span className="tooltip-label">{entry.name}:</span>
                <span className="tooltip-value">{entry.value}</span>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  const renderLegend = (props) => {
    const { payload } = props;
    return (
      <ul className="pie-chart-legend">
        {payload.map((entry, index) => (
          <li key={entry.value} className="legend-item">
            <div 
              className="legend-marker" 
              style={{ 
                backgroundColor: COLORS[index],
                boxShadow: `0 0 3px ${COLORS[index]}80`
              }} 
            />
            <span 
              className="legend-text"
              style={{ color: COLORS[index], filter: 'brightness(0.7)' }}
            >{entry.value}</span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div className="dashboard-container">
      <div className="stats-cards">
        <div 
          className="stat-card clickable" 
          onClick={() => handleStatCardClick('all')}
        >
          <h3>Tổng số phiếu yêu cầu</h3>
          <p className="stat-number">254</p>
          <p className="stat-trend positive">+12% so với tháng trước</p>
        </div>
        <div 
          className="stat-card clickable" 
          onClick={() => handleStatCardClick('pending')}
        >
          <h3>Phiếu đang xử lý</h3>
          <p className="stat-number">42</p>
          <p className="stat-trend negative">-5% so với tháng trước</p>
        </div>
        <div 
          className="stat-card clickable" 
          onClick={() => handleStatCardClick('completed')}
        >
          <h3>Phiếu đã giải quyết</h3>
          <p className="stat-number">198</p>
          <p className="stat-trend positive">+18% so với tháng trước</p>
        </div>
        <div 
          className="stat-card clickable" 
          onClick={() => handleStatCardClick('all')}
        >
          <h3>Thời gian xử lý TB</h3>
          <p className="stat-number">2.4 ngày</p>
          <p className="stat-trend positive">-0.5 ngày so với tháng trước</p>
        </div>
      </div>
      
      <div className="dashboard-charts">
        <div className="chart-container">
          <h3>Phân bố phiếu yêu cầu theo trạng thái</h3>
          <div className="chart-content pie-chart-container">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusData}
                  cx={200}
                  cy={150}
                  labelLine={false}
                  outerRadius={120}
                  innerRadius={50}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${(percent * 100).toFixed(0)}%`}
                  paddingAngle={2}
                >
                  {statusData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={COLORS[index % COLORS.length]}
                      stroke="#fff"
                      strokeWidth={2}
                    />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend 
                  content={renderLegend}
                  layout="vertical"
                  align="right"
                  verticalAlign="middle"
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="chart-container">
          <h3>Phiếu yêu cầu theo thời gian</h3>
          <div className="chart-content">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart
                data={timeData}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 40,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fill: '#666' }}
                  tickLine={{ stroke: '#666' }}
                  axisLine={{ stroke: '#666' }}
                >
                  <Label value="Ngày" position="insideBottom" offset={-25} />
                </XAxis>
                <YAxis 
                  tick={{ fill: '#666' }}
                  tickLine={{ stroke: '#666' }}
                  axisLine={{ stroke: '#666' }}
                >
                  <Label value="Số phiếu" angle={-90} position="insideLeft" style={{ textAnchor: 'middle' }} />
                </YAxis>
                <Tooltip content={<LineChartTooltip />} />
                <Legend 
                  verticalAlign="top" 
                  height={36} 
                  wrapperStyle={{ paddingTop: '10px' }} 
                />
                <ReferenceLine y={20} label="Mục tiêu" stroke="#ff0000" strokeDasharray="3 3" />
                <Line
                  name="Mới"
                  type="monotone"
                  dataKey="new"
                  stroke={LINE_COLORS.new}
                  strokeWidth={2}
                  dot={{ fill: LINE_COLORS.new, stroke: '#fff', strokeWidth: 1, r: 4 }}
                  activeDot={{ r: 6, stroke: '#fff', strokeWidth: 2 }}
                />
                <Line
                  name="Đang xử lý"
                  type="monotone"
                  dataKey="processing"
                  stroke={LINE_COLORS.processing}
                  strokeWidth={2}
                  dot={{ fill: LINE_COLORS.processing, stroke: '#fff', strokeWidth: 1, r: 4 }}
                  activeDot={{ r: 6, stroke: '#fff', strokeWidth: 2 }}
                />
                <Line
                  name="Hoàn thành"
                  type="monotone"
                  dataKey="completed"
                  stroke={LINE_COLORS.completed}
                  strokeWidth={2}
                  dot={{ fill: LINE_COLORS.completed, stroke: '#fff', strokeWidth: 1, r: 4 }}
                  activeDot={{ r: 6, stroke: '#fff', strokeWidth: 2 }}
                />
                <Line
                  name="Tổng cộng"
                  type="monotone"
                  dataKey="total"
                  stroke={LINE_COLORS.total}
                  strokeWidth={3}
                  dot={{ fill: LINE_COLORS.total, stroke: '#fff', strokeWidth: 1, r: 5 }}
                  activeDot={{ r: 7, stroke: '#fff', strokeWidth: 2 }}
                />
                <Brush dataKey="date" height={30} stroke="#8884d8" fill="#f5f5f5" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
      
      <div className="recent-tickets">
        <h3>Phiếu yêu cầu gần đây</h3>
        <table className="tickets-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Ngày yêu cầu</th>
              <th>Tên khách hàng</th>
              <th>NV định giá</th>
              <th>NV điều phối</th>
              <th>NV bán hàng</th>
              <th>Trạng thái</th>
            </tr>
          </thead>
          <tbody>
            <tr onClick={() => navigateToTickets('pending')} className="clickable-row">
              <td>#1234</td>
              <td>15/08/2023</td>
              <td>Công ty TNHH ABC</td>
              <td>Nguyễn Văn A</td>
              <td>Trần Thị B</td>
              <td>Lê Văn C</td>
              <td><span className="status-badge pending">Đang xử lý</span></td>
            </tr>
            <tr onClick={() => navigateToTickets('completed')} className="clickable-row">
              <td>#1233</td>
              <td>14/08/2023</td>
              <td>Công ty TNHH XYZ</td>
              <td>Phạm Văn D</td>
              <td>Hoàng Thị E</td>
              <td>Mai Văn F</td>
              <td><span className="status-badge completed">Hoàn thành</span></td>
            </tr>
            <tr onClick={() => navigateToTickets('new')} className="clickable-row">
              <td>#1232</td>
              <td>14/08/2023</td>
              <td>Công ty TNHH 123</td>
              <td>Đỗ Văn G</td>
              <td>Bùi Thị H</td>
              <td>Vũ Văn I</td>
              <td><span className="status-badge new">Mới</span></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Hàm helper để lấy màu cho trạng thái
const getStatusColor = (status) => {
  const colors = {
    'Khởi tạo': '#e3f2fd',
    'Đã duyệt': '#e8eaf6',
    'Đã điền giá': '#f3e5f5',
    'Đã giao hàng': '#e0f7fa',
    'Đã thu tiền KH': '#e8f5e9',
    'Đã trả tiền NCC': '#fff3e0',
    'Hoàn thành': '#fce4ec'
  };
  return colors[status] || '#e0e0e0';
};

export default Dashboard; 