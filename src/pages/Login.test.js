import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Login from './Login';

// Import the actual modules to mock
import apiService from '../services/api.service';
import axios from 'axios';

// Mock the modules
jest.mock('../services/api.service');
jest.mock('axios', () => {
  const mockAxios = {
    post: jest.fn(() => Promise.resolve({ data: {} }))
  };
  mockAxios.create = jest.fn(() => mockAxios);
  mockAxios.interceptors = {
    request: { use: jest.fn() },
    response: { use: jest.fn() }
  };
  return mockAxios;
});

describe('Login Component', () => {
  const mockOnLogin = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  test('renders login form correctly', () => {
    render(<Login onLogin={mockOnLogin} />);

    // Check if form elements are rendered
    expect(screen.getByText(/Đăng nhập để tiếp tục/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Tên đăng nhập/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Mật khẩu/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Đăng nhập/i })).toBeInTheDocument();
    expect(screen.getByText(/Chưa có tài khoản\? Đăng ký/i)).toBeInTheDocument();
  });

  test('toggles between login and register forms', async () => {
    render(<Login onLogin={mockOnLogin} />);

    // Initially in login mode
    expect(screen.getByText(/Đăng nhập để tiếp tục/i)).toBeInTheDocument();

    // Click register button
    await userEvent.click(screen.getByText(/Chưa có tài khoản\? Đăng ký/i));

    // Should now be in register mode
    expect(screen.getByText(/Đăng ký tài khoản mới/i)).toBeInTheDocument();
    expect(screen.getAllByLabelText(/Tên hiển thị/i)[0]).toBeInTheDocument();

    // Click login button
    await userEvent.click(screen.getByText(/Đã có tài khoản\? Đăng nhập/i));

    // Should be back in login mode
    expect(screen.getByText(/Đăng nhập để tiếp tục/i)).toBeInTheDocument();
  });

  test('shows password when toggle button is clicked', async () => {
    render(<Login onLogin={mockOnLogin} />);

    // Password field should be type="password" initially
    const passwordInput = screen.getByLabelText(/Mật khẩu/i);
    expect(passwordInput).toHaveAttribute('type', 'password');

    // Click the eye icon to show password
    const toggleButton = screen.getByRole('button', { name: '' }); // Eye icon button
    await userEvent.click(toggleButton);

    // Password field should now be type="text"
    expect(passwordInput).toHaveAttribute('type', 'text');

    // Click again to hide password
    await userEvent.click(toggleButton);

    // Password field should be back to type="password"
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  test('handles successful login', async () => {
    // Mock successful API responses
    const mockPostResponse = {
      data: {
        status: 1,
        access_token: 'fake-token'
      }
    };

    const mockProfileResponse = {
      username: 'testuser',
      display_name: 'Test User',
      role: 'admin',
      id: 1,
      company_id: 1
    };

    // Setup mocks
    axios.post.mockResolvedValue(mockPostResponse);
    apiService.get.mockResolvedValue(mockProfileResponse);

    // Render component
    render(<Login onLogin={mockOnLogin} />);

    // Fill in login form
    await userEvent.type(screen.getByLabelText(/Tên đăng nhập/i), 'testuser');
    await userEvent.type(screen.getByLabelText(/Mật khẩu/i), 'password123');

    // Use fireEvent instead of userEvent for form submission to avoid act() warnings
    fireEvent.submit(screen.getByRole('form'));

    // Wait for API calls to resolve with longer timeout
    await waitFor(() => {
      expect(axios.post).toHaveBeenCalled();
      expect(apiService.get).toHaveBeenCalled();
      expect(mockOnLogin).toHaveBeenCalled();
    }, { timeout: 3000 });

    // Check if user data was passed to onLogin
    expect(mockOnLogin).toHaveBeenCalledWith(expect.objectContaining({
      username: 'testuser',
      name: 'Test User',
      role: 'admin',
      isLoggedIn: true
    }));
  });

  test('handles login error', async () => {
    // Mock failed API response
    axios.post.mockRejectedValue({
      response: {
        data: {
          detail: 'Invalid credentials'
        }
      }
    });

    render(<Login onLogin={mockOnLogin} />);

    // Fill in login form
    await userEvent.type(screen.getByLabelText(/Tên đăng nhập/i), 'wronguser');
    await userEvent.type(screen.getByLabelText(/Mật khẩu/i), 'wrongpass');

    // Submit form
    await userEvent.click(screen.getByRole('button', { name: /Đăng nhập/i }));

    // Wait for error message
    await waitFor(() => {
      expect(screen.getByText(/Đã có lỗi xảy ra/i)).toBeInTheDocument();
    });

    // onLogin should not have been called
    expect(mockOnLogin).not.toHaveBeenCalled();
  });

  test('handles successful registration', async () => {
    // Mock successful registration response
    const mockRegisterResponse = {
      message: 'User created successfully'
    };

    // Setup mock
    apiService.post.mockResolvedValue(mockRegisterResponse);

    render(<Login onLogin={mockOnLogin} />);

    // Switch to register mode
    fireEvent.click(screen.getByText(/Chưa có tài khoản\? Đăng ký/i));

    // Fill in registration form
    await userEvent.type(screen.getByLabelText(/Tên đăng nhập/i), 'newuser');
    await userEvent.type(screen.getAllByLabelText(/Tên hiển thị/i)[0], 'New User');
    await userEvent.type(screen.getByLabelText(/Mật khẩu/i), 'newpass123');

    // Submit form using fireEvent instead of userEvent
    fireEvent.submit(screen.getByRole('form'));

    // Wait for API call to be made
    await waitFor(() => {
      expect(apiService.post).toHaveBeenCalled();
    }, { timeout: 3000 });

    // Mock the state update that would happen after successful registration
    // This is a workaround since we can't directly test the component's internal state
    fireEvent.click(screen.getByText(/Đã có tài khoản\? Đăng nhập/i));

    // Should be back in login mode
    expect(screen.getByText(/Đăng nhập để tiếp tục/i)).toBeInTheDocument();
  });
});
