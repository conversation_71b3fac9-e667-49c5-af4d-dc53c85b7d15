import { render, screen } from '@testing-library/react';
import App from './App';

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  BrowserRouter: ({ children }) => <div>{children}</div>,
  Routes: ({ children }) => <div>{children}</div>,
  Route: ({ children }) => <div>{children}</div>,
  Navigate: () => <div>Navigate</div>,
  useNavigate: () => jest.fn(),
  useLocation: () => ({ pathname: '/' }),
}));

// Mock localStorage
const mockLocalStorage = (() => {
  let store = {};
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn(key => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock the API service
jest.mock('./services/api.service', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));

const renderWithRouter = (ui) => {
  return render(ui);
};

describe('App Component', () => {
  beforeEach(() => {
    mockLocalStorage.clear();
    jest.clearAllMocks();
  });

  test('renders login page when user is not logged in', () => {
    renderWithRouter(<App />);
    expect(screen.getByText(/Đăng nhập để tiếp tục/i)).toBeInTheDocument();
  });

  test('renders app with sidebar when user is logged in', () => {
    // Setup mock logged in user
    const mockUser = {
      username: 'testuser',
      name: 'Test User',
      role: 'admin',
      isLoggedIn: true,
      token: 'fake-token',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockUser));

    renderWithRouter(<App />);

    // Check if sidebar is rendered
    expect(screen.getByText(/Ticket Management/i)).toBeInTheDocument();
  });
});
