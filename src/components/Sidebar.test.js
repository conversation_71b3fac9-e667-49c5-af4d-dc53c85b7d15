import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Sidebar from './Sidebar';

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/dashboard' })
}));

const renderWithRouter = (ui) => {
  return render(ui);
};

describe('Sidebar Component', () => {
  const mockOnLogout = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders correctly for admin user', () => {
    const adminUser = {
      name: 'Admin User',
      role: 'admin'
    };

    renderWithRouter(<Sidebar user={adminUser} onLogout={mockOnLogout} />);

    // Check if user info is displayed
    expect(screen.getByText('Admin User')).toBeInTheDocument();
    expect(screen.getByText('Administrator')).toBeInTheDocument();

    // Check if admin menu items are displayed
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Tickets')).toBeInTheDocument();
    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByText('Customers')).toBeInTheDocument();
    expect(screen.getByText('Suppliers')).toBeInTheDocument();
    expect(screen.getByText('Reports')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Audit Logs')).toBeInTheDocument();
  });

  test('renders correctly for approval user', () => {
    const approvalUser = {
      name: 'Approval User',
      role: 'approval'
    };

    renderWithRouter(<Sidebar user={approvalUser} onLogout={mockOnLogout} />);

    // Check if user info is displayed
    expect(screen.getByText('Approval User')).toBeInTheDocument();
    expect(screen.getByText('Approval')).toBeInTheDocument();

    // Check if approval menu items are displayed
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Tickets')).toBeInTheDocument();
    expect(screen.getByText('Pending Tickets')).toBeInTheDocument();
    expect(screen.getByText('Approved Tickets')).toBeInTheDocument();

    // Admin-only items should not be present
    expect(screen.queryByText('Users')).not.toBeInTheDocument();
    expect(screen.queryByText('Settings')).not.toBeInTheDocument();
  });

  test('renders correctly for operator user', () => {
    const operatorUser = {
      name: 'Operator User',
      role: 'operator'
    };

    renderWithRouter(<Sidebar user={operatorUser} onLogout={mockOnLogout} />);

    // Check if user info is displayed
    expect(screen.getByText('Operator User')).toBeInTheDocument();
    expect(screen.getByText('User')).toBeInTheDocument();

    // Check if operator menu items are displayed
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Tickets')).toBeInTheDocument();
    expect(screen.getByText('Your Tickets')).toBeInTheDocument();
    expect(screen.getByText('Help Center')).toBeInTheDocument();

    // Admin-only items should not be present
    expect(screen.queryByText('Users')).not.toBeInTheDocument();
    expect(screen.queryByText('Settings')).not.toBeInTheDocument();
  });

  test('navigates when menu item is clicked', async () => {
    const adminUser = {
      name: 'Admin User',
      role: 'admin'
    };

    renderWithRouter(<Sidebar user={adminUser} onLogout={mockOnLogout} />);

    // Click on Users menu item
    await userEvent.click(screen.getByText('Users'));

    // Check if navigate was called with correct path
    expect(mockNavigate).toHaveBeenCalledWith('/users');
  });

  test('calls onLogout when logout button is clicked', async () => {
    const adminUser = {
      name: 'Admin User',
      role: 'admin'
    };

    renderWithRouter(<Sidebar user={adminUser} onLogout={mockOnLogout} />);

    // Click on logout button
    await userEvent.click(screen.getByText('Logout'));

    // Check if onLogout was called
    expect(mockOnLogout).toHaveBeenCalled();
  });

  test('handles missing user gracefully', () => {
    renderWithRouter(<Sidebar user={null} onLogout={mockOnLogout} />);

    // Should display default user info
    expect(screen.getByText('User')).toBeInTheDocument();

    // Menu should be empty
    expect(screen.queryByText('Dashboard')).not.toBeInTheDocument();
    expect(screen.queryByText('Tickets')).not.toBeInTheDocument();
  });
});
