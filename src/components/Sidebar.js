import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import '../styles/Sidebar.css';
import {
  FiFileText,
  FiUsers,
  FiBarChart2,
  FiSettings,
  FiLogOut,
  FiHelpCircle,
  FiCheckCircle,
  FiClock,
  FiPlus,
  FiList,
  FiActivity,
  FiBell,
  FiUserCheck,
  FiInbox,
  FiTruck
} from 'react-icons/fi';
import { RiDashboardLine } from 'react-icons/ri';
import { BiUserCircle } from 'react-icons/bi';

const Sidebar = ({ user, onLogout }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get current active page from URL path
  const getActivePage = () => {
    const path = location.pathname.split('/')[1] || 'dashboard';
    return path;
  };

  // Define menu items based on user role
  const getMenuItems = () => {
    if (!user) return [];

    switch (user.role) {
      case 'admin':
        return [
          // { id: 'dashboard', label: 'Dashboard', icon: <RiDashboardLine />, path: '/dashboard' },
          { id: 'tickets', label: 'Tickets', icon: <FiInbox />, path: '/tickets' },
          { id: 'users', label: 'Users', icon: <FiUsers />, path: '/users' },
          { id: 'customers', label: 'Customers', icon: <FiUserCheck />, path: '/customers' },
          { id: 'suppliers', label: 'Suppliers', icon: <FiTruck />, path: '/suppliers' },
          // { id: 'tickets', label: 'Tickets', icon: <FiList />, path: '/tickets' },
          { id: 'reports', label: 'Reports', icon: <FiBarChart2 />, path: '/report/select' },
          { id: 'notifications', label: 'Notifications', icon: <FiBell />, path: '/notifications' },
          // { id: 'settings', label: 'Settings', icon: <FiSettings />, path: '/settings' },
          // { id: 'audit', label: 'Audit Logs', icon: <FiActivity />, path: '/audit' },
          // { id: 'test', label: 'Test Table', icon: <FiFileText />, path: '/test' }
          { id: 'helpcenter', label: 'Help Center', icon: <FiHelpCircle />, path: '/helpcenter' },
        ];
      case 'accountant':
        return [
          // { id: 'dashboard', label: 'Dashboard', icon: <RiDashboardLine />, path: '/dashboard' },
          { id: 'tickets', label: 'Tickets', icon: <FiInbox />, path: '/tickets' },
          // { id: 'users', label: 'Users', icon: <FiUsers />, path: '/users' },
          { id: 'customers', label: 'Customers', icon: <FiUserCheck />, path: '/customers' },
          { id: 'suppliers', label: 'Suppliers', icon: <FiTruck />, path: '/suppliers' },
          // { id: 'tickets', label: 'Tickets', icon: <FiList />, path: '/tickets' },
          { id: 'reports', label: 'Reports', icon: <FiBarChart2 />, path: '/report/select' },
          { id: 'notifications', label: 'Notifications', icon: <FiBell />, path: '/notifications' },
          // { id: 'settings', label: 'Settings', icon: <FiSettings />, path: '/settings' },
          // { id: 'audit', label: 'Audit Logs', icon: <FiActivity />, path: '/audit' },
          // { id: 'test', label: 'Test Table', icon: <FiFileText />, path: '/test' }
          { id: 'helpcenter', label: 'Help Center', icon: <FiHelpCircle />, path: '/helpcenter' },
        ];
      case 'operator':
        return [
          // { id: 'dashboard', label: 'Dashboard', icon: <RiDashboardLine />, path: '/dashboard' },
          { id: 'tickets', label: 'Tickets', icon: <FiInbox />, path: '/tickets' },
          // { id: 'users', label: 'Users', icon: <FiUsers />, path: '/users' },
          { id: 'customers', label: 'Customers', icon: <FiUserCheck />, path: '/customers' },
          { id: 'suppliers', label: 'Suppliers', icon: <FiTruck />, path: '/suppliers' },
          // { id: 'tickets', label: 'Tickets', icon: <FiList />, path: '/tickets' },
          { id: 'reports', label: 'Reports', icon: <FiBarChart2 />, path: '/report/select' },
          { id: 'notifications', label: 'Notifications', icon: <FiBell />, path: '/notifications' },
          // { id: 'settings', label: 'Settings', icon: <FiSettings />, path: '/settings' },
          // { id: 'audit', label: 'Audit Logs', icon: <FiActivity />, path: '/audit' },
          // { id: 'test', label: 'Test Table', icon: <FiFileText />, path: '/test' }
          { id: 'helpcenter', label: 'Help Center', icon: <FiHelpCircle />, path: '/helpcenter' },
        ];
      case 'sale':
      case 'moderator':
      case 'purchaser':
        return [
          // { id: 'dashboard', label: 'Dashboard', icon: <RiDashboardLine />, path: '/dashboard' },
          { id: 'tickets', label: 'Tickets', icon: <FiInbox />, path: '/tickets' },
          // { id: 'users', label: 'Users', icon: <FiUsers />, path: '/users' },
          { id: 'customers', label: 'Customers', icon: <FiUserCheck />, path: '/customers' },
          { id: 'suppliers', label: 'Suppliers', icon: <FiTruck />, path: '/suppliers' },
          // { id: 'your-tickets', label: 'Your Tickets', icon: <FiFileText />, path: '/your-tickets' },
          // { id: 'pending', label: 'Pending Tickets', icon: <FiClock />, path: '/pending' },
          // { id: 'approved', label: 'Approved Tickets', icon: <FiCheckCircle />, path: '/approved' },
          { id: 'notifications', label: 'Notifications', icon: <FiBell />, path: '/notifications' },
          { id: 'helpcenter', label: 'Help Center', icon: <FiHelpCircle />, path: '/helpcenter' },
          // { id: 'test', label: 'Test Table', icon: <FiFileText />, path: '/test' },
        ];
      case 'accountant':
        return [
          // { id: 'dashboard', label: 'Dashboard', icon: <RiDashboardLine />, path: '/dashboard' },
          { id: 'tickets', label: 'Tickets', icon: <FiInbox />, path: '/tickets' },
          { id: 'users', label: 'Users', icon: <FiUsers />, path: '/users' },
          { id: 'customers', label: 'Customers', icon: <FiUserCheck />, path: '/customers' },
          { id: 'suppliers', label: 'Suppliers', icon: <FiTruck />, path: '/suppliers' },
          { id: 'notifications', label: 'Notifications', icon: <FiBell />, path: '/notifications' },
          { id: 'reports', label: 'Reports', icon: <FiBarChart2 />, path: '/report/select' },
          { id: 'helpcenter', label: 'Help Center', icon: <FiHelpCircle />, path: '/helpcenter' },
          // { id: 'test', label: 'Test Table', icon: <FiFileText />, path: '/test' }
        ];
      default:
        return [];
    }
  };

  const menuItems = getMenuItems();
  const activePage = getActivePage();

  const handleMenuClick = (path) => {
    navigate(path);
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2 className="logo">Ticket Management</h2>
      </div>

      <div className="user-info">
        <BiUserCircle className="user-avatar-icon" />
        <div className="user-details">
          <h3>{user ? user.name : 'User'}</h3>
          <p>{user ? (
            user.role === 'admin' ? 'Administrator' :
            user.role === 'approval' ? 'Approval' : 'User'
          ) : ''}</p>
        </div>
      </div>

      <nav className="sidebar-menu">
        <ul>
          {menuItems.map(item => (
            <li
              key={item.id}
              className={activePage === item.id ? 'active' : ''}
              onClick={() => handleMenuClick(item.path)}
            >
              <span className="menu-icon">{item.icon}</span>
              <span className="menu-label">{item.label}</span>
            </li>
          ))}
        </ul>
      </nav>

      <div className="sidebar-footer">
        <button className="logout-btn" onClick={onLogout}>
          <FiLogOut className="logout-icon" />
          <span>Logout</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;