/* Reset CSS */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Segoe UI', sans-serif;
  background-color: #f5f7fb;
  color: #333;
}

/* Main Layout */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* Content Area */
.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.content-header h1 {
  font-size: 24px;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 15px;
}

/* Common Button Styles */
.btn-primary,
.btn-secondary,
.btn-danger,
.btn-success {
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  /* box-shadow: 0 2px 5px rgba(74, 108, 247, 0.2); */
}

.btn-primary:hover {
  background-color: #3a5ce5;
}

.btn-primary:active {
  transform: translateY(0);
  /* box-shadow: 0 2px 3px rgba(74, 108, 247, 0.2); */
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-danger {
  background-color: #f44336;
  color: white;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.btn-success {
  background-color: #4caf50;
  color: white;
}

.btn-success:hover {
  background-color: #45a049;
} 


.btn-save,
.btn-reset {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-save {
  background-color: var(--btn-primary-bg);
  color: white;
}

.btn-save:hover {
  background-color: var(--btn-primary-hover);
}

.btn-reset {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
}

.btn-reset:hover {
  background-color: var(--btn-secondary-hover);
}

.btn-icon-left {
  font-size: 18px;
}

.btn-delete {
  background: none;
  border: none;
  color: var(--error-text);
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s;
}

.btn-delete:hover {
  color: var(--error-text);
  opacity: 0.8;
}


.btn-add {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--btn-primary-bg);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-add:hover {
  background-color: var(--btn-primary-hover);
}


/* Filter Settings Styles */
.filters-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.platform-filters {
  background-color: var(--filter-bg);
  padding: 15px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.platform-filters h4 {
  margin: 0 0 15px 0;
  color: var(--text-color);
  font-size: 16px;
  text-transform: capitalize;
}

/* .filter-group {
  margin-bottom: 15px;
} */

.filter-group h5 {
  margin: 0 0 10px 0;
  color: var(--text-color);
  font-size: 14px;
  text-transform: capitalize;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  background-color: var(--filter-option-bg);
  color: var(--filter-option-text);
  border-radius: 12px;
  font-size: 13px;
}

.btn-remove-option {
  background: none;
  border: none;
  color: var(--text-color);
  opacity: 0.7;
  cursor: pointer;
  padding: 0;
  font-size: 16px;
  line-height: 1;
  transition: all 0.3s;
}

.btn-remove-option:hover {
  color: var(--error-text);
  opacity: 1;
}

.add-option input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.add-option input:focus {
  outline: none;
  border-color: var(--btn-primary-bg);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}



.btn-back {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #f4f4f4;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-back:hover {
  background-color: #e0e0e0;
}

.btn-back svg {
  margin-right: 5px;
}



/* Common Search Box */
.search-box {
  position: relative;
  align-content: center;
}

.search-box input {
  padding: 8px 12px 8px 35px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 14px;
  width: 200px;
  transition: all 0.3s;
  outline: none;
}

.search-box input:focus {
  width: 240px;
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 16px;
}

.content-body {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  min-height: calc(100vh - 120px);
  margin-bottom: 20px;
}

/* Common Button Styles */
.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.btn-icon:hover {
  background-color: #f5f5f5;
}

/* Common Status and Priority Badges */
.status-badge {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-badge.inactive {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

.status-badge.remove {
  background-color: #f2d4d4;
  color: #eb6464;
}

.status-badge.new {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-badge.pending {
  background-color: #fff8e1;
  color: #ffc107;
}

.status-badge.approved {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-badge.rejected {
  background-color: #ffebee;
  color: #f44336;
}

.status-badge.in_progress {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-badge.done {
  background-color: #e8f5e9;
  color: #388e3c;
}

.status-badge.closed {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

/* Dark theme support for status badges */
[data-theme="dark"] .status-badge.new {
  background-color: #1a2b3c;
  color: #64b5f6;
}

[data-theme="dark"] .status-badge.pending {
  background-color: #2d2a1a;
  color: #ffd54f;
}

[data-theme="dark"] .status-badge.approved {
  background-color: #1a472a;
  color: #81c784;
}

[data-theme="dark"] .status-badge.rejected {
  background-color: #3d1a1a;
  color: #e57373;
}

[data-theme="dark"] .status-badge.in_progress {
  background-color: #1a2b3c;
  color: #64b5f6;
}

[data-theme="dark"] .status-badge.done {
  background-color: #1a472a;
  color: #81c784;
}

[data-theme="dark"] .status-badge.closed {
  background-color: #2d333b;
  color: #adbac7;
}

.priority-badge {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.priority-badge.high {
  background-color: #ffebee;
  color: #f44336;
}

.priority-badge.medium {
  background-color: #fff8e1;
  color: #ffc107;
}

.priority-badge.low {
  background-color: #e8f5e9;
  color: #4caf50;
}

/* Common Utilities */
.clickable {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.clickable:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.clickable-row {
  cursor: pointer;
}

.clickable-row:hover {
  background-color: #f5f7fb;
}

.loading-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #4a6cf7;
  background-color: #f5f7fb;
}

/* Pagination - Common across multiple components */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 5px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  background-color: white;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.pagination-btn:hover {
  background-color: #f5f7fb;
}

.pagination-btn.active {
  background-color: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

/* Search Results Dropdown - Used in global search */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 5px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.search-result-item {
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: #f5f5f5;
}

.search-result-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.search-result-description {
  font-size: 12px;
  color: #666;
}

/* Dark Theme Support for Common Elements */
[data-theme="dark"] .content-header {
  border-color: #444c56;
}

[data-theme="dark"] .content-header h1 {
  color: #e6edf3;
}

[data-theme="dark"] .content-body {
  background-color: #2d333b;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .btn-icon:hover {
  background-color: #444c56;
}

[data-theme="dark"] .search-results {
  background-color: #2d333b;
  border-color: #444c56;
}

[data-theme="dark"] .search-result-item {
  border-color: #373e47;
}

[data-theme="dark"] .search-result-item:hover {
  background-color: #22272e;
}

[data-theme="dark"] .search-result-title {
  color: #e6edf3;
}

[data-theme="dark"] .search-result-description {
  color: #adbac7;
}

/* Media Queries for Responsive Design */
@media (max-width: 768px) {
  .header-actions {
    flex-wrap: wrap;
  }
  
  .search-box {
    order: 3;
    width: 100%;
    margin-top: 10px;
  }
  
  .search-box input {
    width: 100%;
  }
  
  .search-box input:focus {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 15px;
  }
  
  .content-body {
    padding: 15px;
  }
}

/* Request Form Styles */
.request-form-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.request-form-container h2 {
  margin-bottom: 24px;
  color: #333;
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #0066cc;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

/* Style for readonly fields */
.form-group input.readonly-field {
  background-color: #f5f7fb;
  color: #666;
  cursor: not-allowed;
  border-color: #e0e0e0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

/* Error message styling */
.error-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
  gap: 10px;
  background-color: #ffebee;
  color: #d32f2f;
  border-left: 4px solid #d32f2f;
  animation: fadeIn 0.3s ease-in-out;
}

.error-message svg {
  color: #d32f2f;
  font-size: 20px;
  flex-shrink: 0;
}

/* Success message styling */
.success-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
  gap: 10px;
  background-color: #e8f5e9;
  color: #388e3c;
  border-left: 4px solid #388e3c;
  animation: fadeIn 0.3s ease-in-out;
}

.success-message svg {
  color: #388e3c;
  font-size: 20px;
  flex-shrink: 0;
}

/* .error-message {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 20px;
  color: #cf1322;
}

.error-message p {
  margin: 0;
} */



.toggle-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--input-border);
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--btn-primary-bg);
}

input:disabled + .toggle-slider {
  background-color: var(--input-border);
  cursor: not-allowed;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}


/* Dark theme adjustments */
.dark-theme .request-form-container {
  background-color: #1f1f1f;
}

.dark-theme .request-form-container h2 {
  color: #f0f0f0;
}

.dark-theme .form-group label {
  color: #f0f0f0;
}

.dark-theme .form-group input,
.dark-theme .form-group select,
.dark-theme .form-group textarea {
  background-color: #2a2a2a;
  border-color: #444;
  color: #f0f0f0;
}

.dark-theme .error-message {
  background-color: #2a1215;
  border-color: #5c262c;
  color: #ff7875;
}

/* Common Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  font-weight: 500;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #888;
}

.modal-body {
  padding: 20px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Modal error message styling */
.modal-error-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
  gap: 10px;
  background-color: #fff0f0;
  color: #d32f2f;
  border: 1px solid #ffd0d0;
  animation: fadeIn 0.3s ease-in-out;
}

.modal-error-message .error-icon {
  color: #d32f2f;
  font-size: 20px;
  flex-shrink: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark theme support for modals */
[data-theme="dark"] .modal-container {
  background-color: #2d333b;
  color: #e6edf3;
}

[data-theme="dark"] .modal-header {
  border-color: #444c56;
}

[data-theme="dark"] .modal-close {
  color: #adbac7;
}

[data-theme="dark"] .modal-error-message {
  background-color: #2a1215;
  border-color: #5c262c;
  color: #ff7875;
}

[data-theme="dark"] .modal-error-message .error-icon {
  color: #ff7875;
}
