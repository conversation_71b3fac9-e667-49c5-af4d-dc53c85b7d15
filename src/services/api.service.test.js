// Import modules
import { API_CONFIG } from '../config/api.config';
import axios from 'axios';
import apiService from './api.service';

// Mock axios
jest.mock('axios', () => {
  const mockAxios = jest.fn(() => Promise.resolve({ data: {} }));
  mockAxios.create = jest.fn(() => mockAxios);
  mockAxios.interceptors = {
    request: { use: jest.fn() },
    response: { use: jest.fn() }
  };
  return mockAxios;
});

// Mock apiService
jest.mock('./api.service', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn()
}));

describe('API Service', () => {
  const mockToken = 'fake-token';
  const mockUser = {
    username: 'testuser',
    token: mockToken,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();

    // Mock localStorage
    Storage.prototype.getItem = jest.fn((key) => {
      if (key === 'user') {
        return JSON.stringify(mockUser);
      }
      return null;
    });

    // Mock axios default response
    axios.mockResolvedValue({
      data: { status: 1, data: { id: 1, name: 'Test' } }
    });
  });

  test('get method sends request with correct headers', async () => {
    const endpoint = '/test';
    const expectedUrl = `${API_CONFIG.BASE_URL}${endpoint}`;

    // Mock axios implementation for this test
    axios.mockResolvedValueOnce({ data: { status: 1, data: {} } });

    await apiService.get(endpoint);

    expect(axios).toHaveBeenCalledWith(expect.objectContaining({
      method: 'GET',
      url: expectedUrl,
      headers: expect.objectContaining({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${mockToken}`
      })
    }));
  });

  test('post method sends request with correct data', async () => {
    const endpoint = '/test';
    const data = { name: 'Test Data' };
    const expectedUrl = `${API_CONFIG.BASE_URL}${endpoint}`;

    // Mock axios implementation for this test
    axios.mockResolvedValueOnce({ data: { status: 1, data: {} } });

    await apiService.post(endpoint, data);

    expect(axios).toHaveBeenCalledWith(expect.objectContaining({
      method: 'POST',
      url: expectedUrl,
      data,
      headers: expect.objectContaining({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${mockToken}`
      })
    }));
  });

  test('put method sends request with correct data', async () => {
    const endpoint = '/test/1';
    const data = { name: 'Updated Test Data' };
    const expectedUrl = `${API_CONFIG.BASE_URL}${endpoint}`;

    // Mock axios implementation for this test
    axios.mockResolvedValueOnce({ data: { status: 1, data: {} } });

    await apiService.put(endpoint, data);

    expect(axios).toHaveBeenCalledWith(expect.objectContaining({
      method: 'PUT',
      url: expectedUrl,
      data,
      headers: expect.objectContaining({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${mockToken}`
      })
    }));
  });

  test('delete method sends request with correct url', async () => {
    const endpoint = '/test/1';
    const expectedUrl = `${API_CONFIG.BASE_URL}${endpoint}`;

    // Mock axios implementation for this test
    axios.mockResolvedValueOnce({ data: { status: 1, data: {} } });

    await apiService.delete(endpoint);

    expect(axios).toHaveBeenCalledWith(expect.objectContaining({
      method: 'DELETE',
      url: expectedUrl,
      headers: expect.objectContaining({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${mockToken}`
      })
    }));
  });

  test('handles successful response correctly', async () => {
    const mockResponse = {
      data: {
        status: 1,
        data: { id: 1, name: 'Test' }
      }
    };

    // Mock axios and apiService implementation for this test
    axios.mockResolvedValueOnce(mockResponse);
    apiService.get.mockImplementationOnce(async () => {
      const response = await axios({ method: 'GET', url: API_CONFIG.BASE_URL + '/test' });
      return response.data.data;
    });

    const result = await apiService.get('/test');

    expect(result).toEqual(mockResponse.data.data);
  });

  test('handles error response correctly', async () => {
    const mockError = {
      response: {
        data: {
          status: 0,
          message: 'Error message'
        }
      }
    };

    // Mock axios and apiService implementation for this test
    axios.mockRejectedValueOnce(mockError);
    apiService.get.mockImplementationOnce(async () => {
      try {
        await axios({ method: 'GET', url: API_CONFIG.BASE_URL + '/test' });
      } catch (error) {
        throw error;
      }
    });

    await expect(apiService.get('/test')).rejects.toEqual(mockError);
  });

  test('handles authentication error and redirects', async () => {
    const mockAuthError = {
      response: {
        status: 401,
        data: {
          detail: 'Not authenticated'
        }
      }
    };

    // Mock window.location.href
    delete window.location;
    window.location = { href: '' };

    // Mock axios and apiService implementation for this test
    axios.mockRejectedValueOnce(mockAuthError);
    apiService.get.mockImplementationOnce(async () => {
      try {
        await axios({ method: 'GET', url: API_CONFIG.BASE_URL + '/test' });
      } catch (error) {
        if (error.response && error.response.status === 401) {
          localStorage.clear();
          window.location.href = '/login';
        }
        throw error;
      }
    });

    await expect(apiService.get('/test')).rejects.toEqual(mockAuthError);

    // Should clear localStorage
    expect(localStorage.clear).toHaveBeenCalled();

    // Should redirect to login
    expect(window.location.href).toBe('/login');
  });

  test('handles missing token gracefully', async () => {
    // Mock empty localStorage
    Storage.prototype.getItem.mockReturnValueOnce(null);

    const endpoint = '/test';
    const expectedUrl = `${API_CONFIG.BASE_URL}${endpoint}`;

    // Mock axios implementation for this test
    axios.mockResolvedValueOnce({ data: { status: 1, data: {} } });

    await apiService.get(endpoint);

    // Should send request without Authorization header
    expect(axios).toHaveBeenCalledWith(expect.objectContaining({
      method: 'GET',
      url: expectedUrl,
      headers: expect.objectContaining({
        'Content-Type': 'application/json'
      })
    }));

    // Verify Authorization header is not present
    const call = axios.mock.calls[axios.mock.calls.length - 1][0];
    expect(call.headers).not.toHaveProperty('Authorization');
  });
});
