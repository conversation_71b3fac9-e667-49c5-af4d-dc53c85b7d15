import React, { useState, useEffect, useRef } from 'react';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { HashRouter as Router } from 'react-router-dom';
import { ThemeProvider, useTheme } from './context/ThemeContext';
import './App.css';
import Sidebar from './components/Sidebar';
import NotificationCenter from './components/NotificationCenter';
import Notifications from './pages/Notifications';

import Login from './pages/Login';

import Dashboard from './pages/Dashboard';
import Tickets from './pages/requests/Requests';
import CreateTicket from './pages/requests/CreateTicket';
import Requests from './pages/requests/Requests';
// import Tickets from './pages/Tickets';
// import TicketDetail from './pages/TicketDetail';
import Users from './pages/Users';
import UserDetail from './pages/UserDetail';
import Customers from './pages/Customers';
import Suppliers from './pages/Suppliers';
import Reports from './pages/Reports';
import ReportSelect from './pages/report/ReportSelect';
import ReportCustomer from './pages/report/ReportCustomer';
import Settings from './pages/Settings';
// import CreateRequest from './pages/requests/CreateRequest';
import RequestDetail from './pages/requests/RequestDetail';
// import TicketChiTiet from './pages/requests/TicketChiTiet';
// import { testApiConnection } from './services/debug.service';

// Main content component that uses React Router hooks
const MainContent = ({ user, activePage, setActivePage, handleLogout, navigateToTickets }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { theme, language } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const searchRef = useRef(null);

  // Define searchable app features
  const appFeatures = [
    {
      id: 'dashboard',
      title: language === 'en' ? 'Dashboard' : 'Trang chủ',
      description: language === 'en' ? 'View system overview' : 'Xem tổng quan hệ thống',
      path: '/dashboard'
    },
    {
      id: 'create-ticket',
      title: language === 'en' ? 'Create New Ticket' : 'Tạo yêu cầu mới',
      description: language === 'en' ? 'Submit a new request or ticket' : 'Gửi yêu cầu hoặc ticket mới',
      path: '/request/types',
      action: () => { navigate('/request/types'); }
    },
    {
      id: 'view-tickets',
      title: language === 'en' ? 'View Tickets' : 'Xem danh sách yêu cầu',
      description: language === 'en' ? 'Browse all tickets' : 'Duyệt tất cả các yêu cầu',
      path: '/tickets'
    },
    {
      id: 'notifications',
      title: language === 'en' ? 'Notifications' : 'Thông báo',
      description: language === 'en' ? 'View all notifications' : 'Xem tất cả thông báo',
      path: '/notifications'
    },
    {
      id: 'settings',
      title: language === 'en' ? 'Settings' : 'Cài đặt',
      description: language === 'en' ? 'Manage system settings' : 'Quản lý cài đặt hệ thống',
      path: '/settings'
    },
    {
      id: 'users',
      title: language === 'en' ? 'User Management' : 'Quản lý người dùng',
      description: language === 'en' ? 'Manage users and permissions' : 'Quản lý người dùng và quyền hạn',
      path: '/users'
    },
    {
      id: 'customers',
      title: language === 'en' ? 'Customer Management' : 'Quản lý khách hàng',
      description: language === 'en' ? 'Manage customers and their accounts' : 'Quản lý khách hàng và tài khoản của họ',
      path: '/customers'
    },
    {
      id: 'suppliers',
      title: language === 'en' ? 'Supplier Management' : 'Quản lý nhà cung cấp',
      description: language === 'en' ? 'Manage suppliers and their accounts' : 'Quản lý nhà cung cấp và tài khoản của họ',
      path: '/suppliers'
    },
    {
      id: 'tickets',
      title: language === 'en' ? 'Tickets' : 'Phiếu yêu cầu',
      description: language === 'en' ? 'View and manage tickets' : 'Xem và quản lý phiếu yêu cầu',
      path: '/tickets'
    },
    {
      id: 'request',
      title: language === 'en' ? 'Create Request' : 'Tạo yêu cầu',
      description: language === 'en' ? 'Submit a new request' : 'Gửi yêu cầu mới',
      path: '/request/types'
    },
    {
      id: 'create-ticket',
      title: language === 'en' ? 'Create Ticket' : 'Tạo phiếu yêu cầu',
      description: language === 'en' ? 'Submit a new ticket' : 'Gửi phiếu yêu cầu mới',
      path: '/ticket/create'
    },
    {
      id: 'reports',
      title: language === 'en' ? 'Reports' : 'Báo cáo',
      description: language === 'en' ? 'View system reports and analytics' : 'Xem báo cáo và phân tích hệ thống',
      path: '/report/select'
    }
  ];

  // Filter features based on search term and user role
  const filteredFeatures = searchTerm.trim() !== ''
    ? appFeatures.filter(feature => {
        // Hide "Create Ticket" features for non-admin and non-purchaser roles
        if ((feature.id === 'create-ticket' || feature.id === 'request') &&
            user.role !== 'admin' && user.role !== 'purchaser') {
          return false;
        }

        return feature.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
               feature.description.toLowerCase().includes(searchTerm.toLowerCase());
      })
    : [];

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setShowSearchResults(e.target.value.trim() !== '');
  };

  // Handle feature selection
  const handleFeatureSelect = (feature) => {
    setSearchTerm('');
    setShowSearchResults(false);
    navigate(feature.path);
    if (feature.action) {
      feature.action();
    }
  };

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSearchResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [searchRef]);

  // Update active page based on current route
  useEffect(() => {
    const path = location.pathname.split('/')[1] || 'dashboard';
    setActivePage(path);
  }, [location, setActivePage]);

  // Handle navigation to create request page
  const navigateToCreateRequest = () => {
    navigate('/ticket/create');
  };

  return (
    <main className="content">
      <div className="content-header">
        <h1>{activePage.charAt(0).toUpperCase() + activePage.slice(1)}</h1>
        <div className="header-actions">
          {/* Only show New Ticket button for admin and purchaser roles */}
          {(user.role === 'admin' || user.role === 'purchaser') && (
            <button className="btn-primary" onClick={navigateToCreateRequest}>+ New Ticket</button>
          )}
          <div className="search-box" ref={searchRef}>
            <input
              type="text"
              placeholder={language === 'en' ? "Search features..." : "Tìm kiếm chức năng..."}
              value={searchTerm}
              onChange={handleSearchChange}
            />
            <i className="search-icon"></i>

            {showSearchResults && filteredFeatures.length > 0 && (
              <div className="search-results">
                {filteredFeatures.map(feature => (
                  <div
                    key={feature.id}
                    className="search-result-item"
                    onClick={() => handleFeatureSelect(feature)}
                  >
                    <div className="search-result-title">{feature.title}</div>
                    <div className="search-result-description">{feature.description}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <NotificationCenter user={user} />
        </div>
      </div>
      <div className="content-body">
        <Routes>
          <Route path="/" element={<Navigate to="/tickets" replace />} />
          <Route path="/dashboard" element={<Dashboard navigateToTickets={navigateToTickets} />} />
          {/* <Route path="/ticket" element={<RequestDetail />} /> */}
          <Route path="/users" element={<Users />} />
          <Route path="/userdetail" element={<UserDetail />} />
          <Route path="/customers" element={<Customers />} />
          <Route path="/suppliers" element={<Suppliers />} />
          <Route path="/report/summary" element={<Reports />} />
          <Route path="/report/select" element={<ReportSelect />} />
          <Route path="/report/customer" element={<ReportCustomer />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/notifications" element={<Notifications />} />

          {/* Ticket routes */}
          <Route path="/tickets" element={<Requests />} />
          <Route
            path="/ticket/create"
            element={
              user.role === 'admin' || user.role === 'purchaser'
                ? <CreateTicket />
                : <Navigate to="/tickets" replace />
            }
          />
          {/* <Route path="/request/types" element={<CreateRequest />} /> */}
          <Route path="/ticket/detail" element={<RequestDetail />} />

          {/* <Route path="/test" element={<TicketChiTiet />} /> */}

        </Routes>
      </div>
    </main>
  );
};

function App() {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const [activePage, setActivePage] = useState(() => {
    const savedPage = localStorage.getItem('activePage');
    return savedPage || 'dashboard';
  });

  // Kiểm tra token khi ứng dụng khởi động
  useEffect(() => {
    const checkAuth = () => {
      const userData = localStorage.getItem('user');

      if (userData) {
        const parsedUser = JSON.parse(userData);

        // Kiểm tra thời gian hết hạn của token
        if (parsedUser.expiresAt && new Date(parsedUser.expiresAt) > new Date()) {
          setUser(parsedUser);
        } else {
          // Token đã hết hạn, xóa khỏi localStorage
          localStorage.removeItem('user');
          localStorage.removeItem('companyInfo');
          setUser(null);
        }
      }

      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const handlePageChange = (page) => {
    setActivePage(page);
    localStorage.setItem('activePage', page);
  };

  // Hàm để chuyển đến trang Tickets với bộ lọc
  const navigateToTickets = (filterStatus) => {
    // Lưu trạng thái bộ lọc vào localStorage
    localStorage.setItem('ticketFilterStatus', filterStatus);
    // Chuyển đến trang Tickets
    handlePageChange('tickets');
  };

  // Xử lý đăng nhập thành công
  const handleLogin = (userData) => {
    setUser(userData);
  };

  // Xử lý đăng xuất
  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('activePage');
    localStorage.removeItem('companyInfo'); // Clear company information on logout
    setUser(null);
    setActivePage('dashboard');
  };

  // Hiển thị loading khi đang kiểm tra xác thực
  if (isLoading) {
    return <div className="loading-screen">Đang tải...</div>;
  }

  // Nếu người dùng chưa đăng nhập, hiển thị trang Login
  if (!user) {
    return <Login onLogin={handleLogin} />;
  }

  return (
    <ThemeProvider>
      <Router>
        <div className="app-container">
          <Sidebar
            activePage={activePage}
            setActivePage={handlePageChange}
            user={user}
            onLogout={handleLogout}
          />
          <MainContent
            user={user}
            activePage={activePage}
            setActivePage={setActivePage}
            handleLogout={handleLogout}
            navigateToTickets={navigateToTickets}
          />
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
