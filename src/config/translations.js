export const translations = {
  en: {
    settings: {
      title: 'Settings',
      ui: {
        title: 'User Interface',
        theme: 'Theme',
        language: 'Language',
        itemsPerPage: 'Items per page',
        light: 'Light',
        dark: 'Dark'
      },
      notifications: {
        title: 'Notifications',
        email: 'Email Notifications',
        userCreation: 'User Creation Notifications',
        userModification: 'User Modification Notifications'
      },
      connections: {
        title: 'Connections',
        addNew: 'Add New Connection',
        name: 'Connection Name',
        type: 'Type',
        url: 'URL',
        add: 'Add',
        delete: 'Delete'
      },
      filters: {
        title: 'Filter Settings',
        addNew: 'Add New Filter',
        status: 'Status',
        roles: 'Roles',
        departments: 'Departments'
      },
      actions: {
        save: 'Save Settings',
        reset: 'Reset to Default'
      }
    }
  },
  vi: {
    settings: {
      title: 'Cài đặt',
      ui: {
        title: 'Giao diện người dùng',
        theme: 'Giao diện',
        language: 'Ngôn ngữ',
        itemsPerPage: '<PERSON><PERSON> mục mỗi trang',
        light: 'Sáng',
        dark: 'Tối'
      },
      notifications: {
        title: 'Thông báo',
        email: 'Thông báo qua Email',
        userCreation: 'Thông báo tạo người dùng',
        userModification: 'Thông báo sửa đổi người dùng'
      },
      connections: {
        title: 'Kết nối',
        addNew: 'Thêm kết nối mới',
        name: 'Tên kết nối',
        type: 'Loại',
        url: 'URL',
        add: 'Thêm',
        delete: 'Xóa'
      },
      filters: {
        title: 'Cài đặt bộ lọc',
        addNew: 'Thêm bộ lọc mới',
        status: 'Trạng thái',
        roles: 'Vai trò',
        departments: 'Phòng ban'
      },
      actions: {
        save: 'Lưu cài đặt',
        reset: 'Khôi phục mặc định'
      }
    }
  }
}; 