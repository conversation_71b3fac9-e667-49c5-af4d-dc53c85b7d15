import { use } from "react";

export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:3014',
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  TIMEOUT: 30000, // 30 seconds
  NOTIFICATION_POLLING_INTERVAL: 30000, // 30 seconds (1 minute)
};

export const ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/passwordlogin',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile',
  },
  // User endpoints
  USERS: {
    LIST: '/users',
    DETAIL: (id) => `/users/${id}`,
    CREATE: '/users/create',
    UPDATE: '/users/update',
    DELETE: (id) => `/users/delete/${id}`,
    GET_ALL: `/users/get_all`,
    GET_BASIC: `/users/get_basic`,
  },
  // Customer endpoints
  CUSTOMERS: {
    LIST: '/customers',
    DETAIL: (id) => `/customers/${id}`,
    CREATE: '/customers/create',
    UPDATE: '/customers/update',
    DELETE: (id) => `/customers/delete/${id}`,
    GET_ALL: `/customers/get_all`,
    GET_BASIC: `/customers/get_basic`,
  },
  // Supplier endpoints
  SUPPLIERS: {
    LIST: '/suppliers',
    DETAIL: (id) => `/suppliers/${id}`,
    CREATE: '/suppliers/create',
    UPDATE: '/suppliers/update',
    DELETE: (id) => `/suppliers/delete/${id}`,
    GET_ALL: `/suppliers/get_all`,
    GET_BASIC: `/suppliers/get_basic`,
  },
  // Company endpoints
  COMPANY: {
    // LIST: '/company',
    // DETAIL: (id) => `/company/${id}`,
    // CREATE: '/company/create',
    // UPDATE: '/company/update',
    // DELETE: (id) => `/company/delete/${id}`,
    // GET_ALL: `/company/get_all`,
    GET_BASIC: `/company/get_basic`,
  },
  NOTIFICATION: {
    GET: (userId) => `/notifications/get?user_id=${userId}`,
    CREATE: '/notifications/create',
    MARK_AS_READ: (notificationId) => `/notifications/mark-as-read?notification_id=${notificationId}`,
    // DELETE: '/notification/delete',
  },
  REQUEST: {
    CREATE: '/requests/create',
    GET: (id) => `/requests/get?request_id=${id}`,
    GET_BY_USER: (id) => `/tickets/getbyuser?user_id=${id}`,
    UPDATE: (requestId) => `/requests/update?request_id=${requestId}`,
    DELETE: (requestId) => `/requests/delete?request_id=${requestId}`,
  },
  TICKET: {
    CREATE: '/tickets/create',
    GET: (id) => `/tickets/get?ticket_id=${id}`,
    GET_BY_USER: (id) => `/tickets/getbyuser?user_id=${id}`,
    UPDATE: (ticketId) => `/tickets/update?ticket_id=${ticketId}`,
    UPDATE_BASIC: (ticketId) => `/tickets/update_basic?ticket_id=${ticketId}`,
    UPDATE_VEHICLE: (ticketId) => `/tickets/update_vehicle?ticket_id=${ticketId}`,
    UPDATE_STAFF: (ticketId) => `/tickets/update_staff?ticket_id=${ticketId}`,
    UPDATE_NOTE: (ticketId) => `/tickets/update_note?ticket_id=${ticketId}`,
    UPDATE_PAY_AMOUNT: (ticketId) => `/tickets/update_pay_amount?ticket_id=${ticketId}`,
    UPDATE_RECEIVE_AMOUNT: (ticketId) => `/tickets/update_receive_amount?ticket_id=${ticketId}`,
    UPDATE_STATUS: (ticketId) => `/tickets/update_status?ticket_id=${ticketId}`,
    DELETE: (ticketId) => `/tickets/delete?ticket_id=${ticketId}`,
  },
  REPORT: {
    GET: (params) => {
      const queryParams = [];
      if (params.start_date) queryParams.push(`start_date=${params.start_date}`);
      if (params.end_date) queryParams.push(`end_date=${params.end_date}`);
      if (params.customer_id) queryParams.push(`customer_id=${params.customer_id}`);
      if (params.supplier_id) queryParams.push(`supplier_id=${params.supplier_id}`);
      if (params.company_id) queryParams.push(`company_id=${params.company_id}`);
      if (params.sale_staff_id) queryParams.push(`sale_staff_id=${params.sale_staff_id}`);
      if (params.pricing_staff_id) queryParams.push(`pricing_staff_id=${params.pricing_staff_id}`);
      if (params.dispatch_staff_id) queryParams.push(`dispatch_staff_id=${params.dispatch_staff_id}`);

      return `/reports/get${queryParams.length ? `?${queryParams.join('&')}` : ''}`;
    },
    CUSTOMER: (params) => {
      const queryParams = [];
      if (params.start_date) queryParams.push(`start_date=${params.start_date}`);
      if (params.end_date) queryParams.push(`end_date=${params.end_date}`);
      if (params.customer_id) queryParams.push(`customer_id=${params.customer_id}`);
      if (params.company_id) queryParams.push(`company_id=${params.company_id}`);

      return `/reports/customer${queryParams.length ? `?${queryParams.join('&')}` : ''}`;
    }
  }
  // Add more endpoint groups as needed
};