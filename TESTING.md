# Hướng dẫn chạy Test

Tài liệu này cung cấp hướng dẫn chi tiết về cách chạy các test trong dự án.

## Yêu cầu

- Node.js (phiê<PERSON> bản 14.x trở lên)
- npm (phi<PERSON><PERSON> bản 6.x trở lên)

## Cài đặt

Trước khi chạy test, hãy đảm bảo bạn đã cài đặt tất cả các dependencies:

```bash
npm install
```

## Chạy Test

### Chạy tất cả các test

Để chạy tất cả các test trong dự án:

```bash
npm test
```

### Chạy một file test cụ thể

Để chạy một file test cụ thể:

```bash
npm test -- src/components/Sidebar.test.js
```

### Chạy test với coverage

Để chạy test và xem báo cáo coverage:

```bash
npm test -- --coverage
```

### Chạy test ở chế độ watch

Mặc định, khi bạn chạy `npm test`, các test sẽ chạy ở chế độ watch. Điều này có nghĩa là các test sẽ tự động chạy lại khi bạn thay đổi code. Trong chế độ watch, bạn có thể:

- Nhấn `a` để chạy tất cả các test
- Nhấn `f` để chạy các test đã fail
- Nhấn `o` để chạy các test liên quan đến các file đã thay đổi
- Nhấn `p` để lọc theo tên file
- Nhấn `t` để lọc theo tên test
- Nhấn `q` để thoát chế độ watch

## Cấu trúc Test

Các file test được đặt cùng thư mục với các file code tương ứng và có đuôi `.test.js` hoặc `.spec.js`. Ví dụ:

- `src/components/Sidebar.js` -> `src/components/Sidebar.test.js`
- `src/pages/Login.js` -> `src/pages/Login.test.js`

## Viết Test

### Các công cụ test

Dự án sử dụng các công cụ test sau:

- Jest: Framework test
- React Testing Library: Thư viện test cho React
- user-event: Thư viện mô phỏng tương tác người dùng

### Ví dụ test cơ bản

```jsx
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import MyComponent from './MyComponent';

describe('MyComponent', () => {
  test('renders correctly', () => {
    render(<MyComponent />);
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });

  test('handles click event', async () => {
    render(<MyComponent />);
    await userEvent.click(screen.getByRole('button'));
    expect(screen.getByText('Clicked')).toBeInTheDocument();
  });
});
```

### Mocking

Để mock một module:

```jsx
import { render, screen } from '@testing-library/react';
import MyComponent from './MyComponent';
import apiService from '../services/api.service';

// Mock apiService
jest.mock('../services/api.service');

describe('MyComponent', () => {
  test('fetches data correctly', async () => {
    // Setup mock
    apiService.get.mockResolvedValue({ data: 'test data' });

    render(<MyComponent />);
    expect(await screen.findByText('test data')).toBeInTheDocument();
    expect(apiService.get).toHaveBeenCalled();
  });
});
```

### Act Warnings

Khi bạn gặp cảnh báo "not wrapped in act(...)", hãy sử dụng `act` để wrap các thao tác gây ra thay đổi state:

```jsx
import { render, screen, act } from '@testing-library/react';
import MyComponent from './MyComponent';

test('updates state correctly', async () => {
  await act(async () => {
    render(<MyComponent />);
  });

  await act(async () => {
    await userEvent.click(screen.getByRole('button'));
  });

  expect(screen.getByText('Updated')).toBeInTheDocument();
});
```

## Xử lý lỗi phổ biến

### 1. "Cannot find module"

Nếu bạn gặp lỗi "Cannot find module", hãy kiểm tra:
- Đường dẫn import có chính xác không
- Module đã được cài đặt chưa

### 2. "TypeError: Cannot read properties of undefined"

Lỗi này thường xảy ra khi bạn cố gắng truy cập một thuộc tính của một đối tượng undefined. Hãy kiểm tra:
- Các mock đã được thiết lập đúng cách chưa
- Các giá trị trả về từ API có đúng định dạng không

### 3. "Test timed out"

Nếu test bị timeout, hãy kiểm tra:
- Các promise đã được resolved/rejected chưa
- Các mock đã được thiết lập đúng cách chưa
- Sử dụng `waitFor` hoặc `findBy*` để đợi các thay đổi bất đồng bộ

## Tài liệu tham khảo

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/)
- [user-event Documentation](https://testing-library.com/docs/user-event/intro)
