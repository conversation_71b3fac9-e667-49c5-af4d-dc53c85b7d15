# Nhật ký thay đổi

Tất cả những thay đổi đáng chú ý của dự án sẽ được ghi lại trong file này.

Định dạng dựa trên [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
và dự án này tuân theo [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Chưa phát hành]

### Thêm mới
- N<PERSON>t "Xóa bộ lọc" trong trang Yêu cầu để cải thiện trải nghiệm người dùng
- Phần bộ lọc có thể thu gọn trong trang Báo cáo
- Thêm mục menu 'Hỏi AI' với giao diện giống ChatGPT
- Bộ test toàn diện cho các components và services
- Unit test cho các component App, Login, Sidebar, Reports
- Unit test cho API service
- Cấ<PERSON> hình khoảng thời gian kiểm tra thông báo trong cấu hình API
- Trang ReportSelect mới tại đường dẫn /report/select để hiển thị các loại báo cáo khác nhau
- Trang ReportCustomer mới tại đường dẫn /report/customer với bộ lọc công ty, tháng (định dạng MM/YYYY) và khách hàng
- Chức năng chọn mục với checkbox trong bảng báo cáo để xuất Excel có chọn lọc
- Thông báo thành công và lỗi tự động ẩn với hiệu ứng mờ dần mượt mà

### Cải tiến
- Nâng cao độ phủ test với hơn 8 test đã pass
- Sửa cảnh báo React act() trong tests bằng cách wrap đúng cách
- Cải thiện các selectors trong test để tăng độ tin cậy
- Thêm mock đúng cách cho axios và API services
- Sửa lỗi mock axios.create trong tests
- Triển khai mock đúng cách cho axios interceptors
- Thêm tài liệu chi tiết trong các file test
- Cải thiện bộ lọc khách hàng với trường nhập liệu có tính năng tự động hoàn thành thay vì dropdown select
- Nâng cao trải nghiệm người dùng với thông báo tự động ẩn sau khi thực hiện hành động
- Lọc danh sách nhân viên theo vai trò trong RequestDetail.js và CreateTicket.js:
  - Nhân viên định giá được lọc theo role = 'purchaser'
  - Nhân viên điều phối được lọc theo role = 'moderator'
  - Nhân viên bán hàng được lọc theo role = 'sale'
- Thêm hiển thị thông tin nhân viên trong Requests.js:
  - Hiển thị tên nhân viên thay vì ID trong cả chế độ xem lưới và bảng
  - Thêm cột cho Nhân viên bán hàng, Nhân viên định giá và Nhân viên điều phối trong chế độ xem bảng
  - Thêm thông tin nhân viên trong thẻ ticket ở chế độ xem lưới
- Đồng bộ hóa màu sắc trạng thái giữa các badge và nút trạng thái:
  - Cập nhật hàm getStatusClass để ánh xạ trạng thái với các lớp CSS phù hợp
  - Chuẩn hóa màu sắc cho tất cả các loại trạng thái trong ứng dụng
  - Thêm các kiểu nút cụ thể cho trạng thái Khách hàng đã thanh toán và Nhà cung cấp đã thanh toán
- Nâng cao trang Báo cáo với trực quan hóa cải tiến:
  - Thêm biểu đồ doanh thu và chi phí theo thời gian cho tab báo cáo khách hàng và nhà cung cấp
  - Thêm biểu đồ phân bố trạng thái ticket cho tab báo cáo khách hàng và nhà cung cấp
  - Triển khai màu sắc trạng thái nhất quán trên tất cả các tab báo cáo sử dụng hàm getStatusColor thống nhất
  - Cải thiện nút "Xóa lọc" để reset tất cả các bộ lọc bao gồm công ty và khoảng thời gian

### Sửa lỗi
- Sửa lỗi với `case 'operator' || 'accountant' || 'sale'` trong Sidebar.js bằng cách sử dụng cú pháp case đúng
- Sửa cảnh báo ESLint về các biến không sử dụng trong Reports.js và Requests.js
- Sửa lỗi thiếu thư viện recharts

### Thay đổi
- Thiết kế lại hoàn toàn giao diện Báo cáo với layout dashboard hiện đại
- Cải thiện tổ chức bộ lọc trong trang Báo cáo
- Nâng cao trực quan hóa dữ liệu với biểu đồ và bảng tốt hơn
- Loại bỏ bộ lọc trạng thái để thay thế bằng thẻ đếm trạng thái trong trang Báo cáo
- Cập nhật giao diện trang Chi tiết Yêu cầu với layout 4 phần

## [1.2.0] - 15/01/2024

### Thêm mới
- Nền tảng Whitelist với chức năng Query Builder
- Tích hợp React Awesome Query Builder (phiên bản 6.6.14)
- Giao diện trực quan Query Builder với khả năng tạo SQL
- Tính năng lịch sử truy vấn và lưu truy vấn
- Liên kết hai chiều giữa truy vấn SQL và trình tạo truy vấn trực quan

### Thay đổi
- Cải thiện giao diện Query Builder với lồng nhóm tốt hơn (tối đa 3 cấp)
- Nâng cao Query Builder với đường kết nối giữa các quy tắc/nhóm
- Thêm màu nền để phân biệt các cấp nhóm lồng nhau

## [1.1.0] - 01/01/2024

### Thêm mới
- Chức năng bình luận với khả năng chỉnh sửa, xóa và trả lời
- Giao diện bình luận kiểu Facebook với đường kết nối trực quan
- Hỗ trợ tối đa 3 cấp trả lời lồng nhau
- Giữ nguyên định dạng văn bản trong bình luận
- Tính năng báo cáo tổng hợp khách hàng với các tab riêng biệt cho khách hàng và nhà cung cấp
- Biểu đồ dòng thời gian để trực quan hóa thay đổi dữ liệu khách hàng theo thời gian

### Thay đổi
- Thiết kế lại giao diện với các báo cáo đồng bộ hơn
- Tổ chức báo cáo thành các nhóm báo cáo khách hàng và báo cáo nhà cung cấp
- Cải thiện layout dashboard với thẻ tóm tắt và báo cáo dựa trên nhiều chiều
- Nâng cao bảng tóm tắt để trực quan hóa dữ liệu toàn diện

## [1.0.0] - 15/12/2023

### Thêm mới
- Phát hành ban đầu của Hệ thống Quản lý Vé
- Xác thực và phân quyền người dùng
- Dashboard với các chỉ số quan trọng
- Quản lý vé (tạo, xem, chỉnh sửa, xóa)
- Quản lý khách hàng với xác thực tên khách hàng
- Quản lý nhà cung cấp được sao chép từ trang Khách hàng với endpoints đã cập nhật
- Quản lý người dùng với xử lý mật khẩu đúng cách
- Báo cáo và phân tích với khả năng lọc
- Hệ thống thông báo
- Trang cài đặt
- Trung tâm trợ giúp
- Giao diện Kanban cho các trang yêu cầu

### Tính năng
- Kiểm soát truy cập dựa trên vai trò (Admin, Approval, Operator, Accountant, Sale)
- Theo dõi trạng thái vé với trạng thái mặc định là 'initial'
- Tìm kiếm và lọc với tìm kiếm thời gian thực
- Phân trang cho bảng dữ liệu với số mục trên mỗi trang được lưu trong localStorage
- Xuất Excel cho báo cáo và bảng dữ liệu
- Hỗ trợ chế độ tối trong toàn bộ ứng dụng
- Thiết kế responsive cho di động và máy tính
- Chức năng tự động hoàn thành cho việc chọn quản lý và công ty

### Tích hợp API
- Endpoints API vé cho các thao tác CRUD
- Tích hợp API Khách hàng và Nhà cung cấp
- API Người dùng với xác thực đúng cách
- API Công ty cho thông tin cơ bản

## [0.9.0] - 30/11/2023

### Thêm mới
- Phiên bản beta với chức năng cốt lõi
- Các component UI cơ bản với thiết kế hiện đại, đẹp mắt
- Tích hợp API với xử lý lỗi đúng cách
- Mô hình dữ liệu và services
- Component Sidebar với các mục menu dựa trên vai trò
- Chức năng lọc cho bảng dữ liệu

### Thay đổi
- Cải thiện giao diện người dùng để phù hợp với thiết kế hệ thống hiện có
- Tối đa hóa việc sử dụng không gian màn hình ngang
- Cập nhật thiết kế bảng với khả năng sắp xếp và lọc

### Vấn đề đã biết
- Vấn đề hiệu suất với tập dữ liệu lớn
- Khả năng báo cáo còn hạn chế
- Responsive cho di động chưa hoàn thiện

## [0.8.0] - 15/11/2023

### Thêm mới
- Phiên bản alpha để kiểm thử nội bộ
- Xác thực cơ bản với hệ thống dựa trên token
- Thiết kế UI sơ bộ với kiểu dáng nhất quán
- Cấu trúc dữ liệu cốt lõi và quản lý trạng thái
- Tích hợp localStorage cho tùy chọn người dùng

### Thay đổi
- Chuyển từ class components sang functional components với hooks
- Cải thiện cách tiếp cận quản lý trạng thái với React hooks
- Nâng cao xác thực form và xử lý lỗi

## [0.7.0] - 30/10/2023

### Thêm mới
- Proof of concept với chức năng cơ bản
- Thiết lập cấu trúc dự án với tổ chức component
- Cấu hình môi trường phát triển
- Thư viện component ban đầu với các phần tử có thể tái sử dụng
- Cấu trúc service API với xử lý lỗi đúng cách

### Kỹ thuật
- Thiết lập React 18 với các thực hành hiện đại
- Cấu hình Webpack cho các bản build tối ưu
- Thiết lập ESLint và Prettier cho chất lượng code
- Tổ chức CSS với cách tiếp cận module hóa
