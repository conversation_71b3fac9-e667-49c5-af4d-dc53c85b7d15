# Release Notes - Form Management System

## Version 1.3.0 (Upcoming Release)

### New Features
- **Configurable Notification Polling Interval**: Added a centralized configuration for notification polling interval in the API configuration file. This allows for easier adjustment of how frequently the system checks for new notifications.

### Improvements
- **Enhanced Code Maintainability**: Moved hardcoded notification polling interval from the NotificationCenter component to the API configuration file, making it easier to maintain and adjust system-wide settings.
- **Better Documentation**: Added clear comments explaining the notification polling mechanism and where to configure it.

### Technical Details
- The notification polling interval is now defined in `src/config/api.config.js` as `NOTIFICATION_POLLING_INTERVAL` (default: 60000ms or 1 minute)
- The NotificationCenter component now references this configuration value instead of using a hardcoded value
- This change allows for easier adjustment of the polling frequency without modifying component code

### Deployment Notes
- No database changes required
- No API changes required
- Only frontend code changes

### Known Issues
- None

---

## Previous Releases

### Version 1.2.0 (2024-01-15)
- Added Whitelist Platform with Query Builder functionality
- Integrated React Awesome Query Builder (version 6.6.14)
- Added Query Builder visual interface with SQL generation
- Added Query history and saved queries features
- Added two-way binding between SQL query and visual query builder

### Version 1.1.0 (2024-01-01)
- Added Comments functionality with editing, deleting, and replying capabilities
- Added Facebook-style comment UI with visual connecting lines
- Added support for up to 3 levels of nested replies
- Added text formatting preservation in comments
- Added Customer summary report feature with separate tabs for customers and suppliers
- Added Timeline charts for visualizing customer data changes over time

### Version 1.0.0 (2023-12-15)
- Initial release of the Ticket Management System
