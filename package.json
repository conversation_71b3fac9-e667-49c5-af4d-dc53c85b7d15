{"name": "form-management", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.10.1", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "recharts": "^2.15.3", "styled-components": "^6.1.17", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "npx react-scripts start", "build": "CI=false npx react-scripts build", "test": "npx react-scripts test", "eject": "npx react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!axios)/"], "moduleNameMapper": {"^react-router-dom$": "<rootDir>/node_modules/react-router-dom/dist/index.js"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}